from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime

class MichiganEconomicDevelopmentCorporation(OCSpider):
    name = 'MichiganEconomicDevelopmentCorporation'
    
    country = "US"

    start_urls_names = {
        'https://www.michiganbusiness.org/press-releases/': 'Press Release'
    }
    
    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Central"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[contains(@class, "feature")]//a[@role="link"]/@href').getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="news-title"]/text()').get().strip()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//p//text()').getall())

    def get_images(self, response) -> list[str]:
        return []
    
    def date_format(self) -> str:
        return "%m/%d/%Y"

    def get_date(self, response) -> str:
        raw_date = response.xpath('//div[@class="col-12 col-sm-auto"]/p[contains(text(), ",")]/text()').get().strip()
        parsed_date = datetime.strptime(raw_date, "%A, %B %d, %Y")  
        return parsed_date.strftime(self.date_format())

    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        articles = response.xpath('//div[contains(@class, "feature")]//a[@role="link"]/@href').getall()
        if not articles:
            return None
        current_url = response.url
        if 'page=' in current_url:
            current_page = int(current_url.split('page=')[-1])
        else:
            current_page = 1
        next_page = current_page + 1
        next_page_url = f"https://www.michiganbusiness.org/press-releases/?page={next_page}"
        return next_page_url