# Define here the models for your spider middleware
#
# See documentation in:
# https://docs.scrapy.org/en/latest/topics/spider-middleware.html

from scrapy import signals
import requests
import os
from scrapy.http import HtmlResponse
from scrapy.utils.project import get_project_settings

# useful for handling different item types with a single interface
from itemadapter import is_item, ItemAdapter
from urllib.parse import urlencode


class ScraperSpiderMiddleware:
    # Not all methods need to be defined. If a method is not defined,
    # scrapy acts as if the spider middleware does not modify the
    # passed objects.

    @classmethod
    def from_crawler(cls, crawler):
        # This method is used by Scrapy to create your spiders.
        s = cls()
        crawler.signals.connect(s.spider_opened, signal=signals.spider_opened)
        return s

    def process_spider_input(self, response, spider):
        # Called for each response that goes through the spider
        # middleware and into the spider.

        # Should return None or raise an exception.
        return None

    def process_spider_output(self, response, result, spider):
        # Called with the results returned from the Spider, after
        # it has processed the response.

        # Must return an iterable of Request, or item objects.
        for i in result:
            yield i

    def process_spider_exception(self, response, exception, spider):
        # Called when a spider or process_spider_input() method
        # (from other spider middleware) raises an exception.

        # Should return either None or an iterable of Request or item objects.
        pass

    def process_start_requests(self, start_requests, spider):
        # Called with the start requests of the spider, and works
        # similarly to the process_spider_output() method, except
        # that it doesn’t have a response associated.

        # Must return only requests (not items).
        for r in start_requests:
            yield r

    def spider_opened(self, spider):
        spider.logger.info('Spider opened: %s' % spider.name)


class ScraperDownloaderMiddleware:
    # Not all methods need to be defined. If a method is not defined,
    # scrapy acts as if the downloader middleware does not modify the
    # passed objects.

    @classmethod
    def from_crawler(cls, crawler):
        # This method is used by Scrapy to create your spiders.
        s = cls()
        crawler.signals.connect(s.spider_opened, signal=signals.spider_opened)
        return s

    def process_request(self, request, spider):
        # Called for each request that goes through the downloader
        # middleware.

        # Must either:
        # - return None: continue processing this request
        # - or return a Response object
        # - or return a Request object
        # - or raise IgnoreRequest: process_exception() methods of
        #   installed downloader middleware will be called
        return None

    def process_response(self, request, response, spider):
        # Called with the response returned from the downloader.

        # Must either;
        # - return a Response object
        # - return a Request object
        # - or raise IgnoreRequest
        return response

    def process_exception(self, request, exception, spider):
        # Called when a download handler or a process_request()
        # (from other downloader middleware) raises an exception.

        # Must either:
        # - return None: continue processing this exception
        # - return a Response object: stops process_exception() chain
        # - return a Request object: stops process_exception() chain
        pass

    def spider_opened(self, spider):
        spider.logger.info('Spider opened: %s' % spider.name)


class GeoProxyMiddleware(object):
    # more information ref: https://scrapeops.io/docs/web-scraping-proxy-api-aggregator/advanced-functionality/country-geotargeting/
    countries = [
        "br",
        "ca",
        "cn",
        "in",
        "it",
        "jp",
        "fr",
        "de",
        "ru",
        "es",
        "us",
        "uk"
    ]
    
    def get_proxy(self, url, country):
        if country not in self.countries:
            raise Exception(f"Country {country} not supported")
        
        scrapeops_key = os.environ["SCRAPEROPS_API_KEY"]
        _url = f'https://proxy.scrapeops.io/v1/?api_key={scrapeops_key}&url={url}&country={country}'
        return _url
    

    def process_request(self, request, spider, timeout=None):
        # proxy_country is a variable that needs to be set in the spider class
        # eg: proxy_country = "cn"
        url = self.get_proxy(request.url, spider.proxy_country)

        if timeout:
            response = requests.get(url, timeout=(timeout, None))
        else:
            response = requests.get(url)

        new_response = HtmlResponse(
            url=request.url,
            body=response.content,
            encoding='utf-8',
            status=response.status_code
        )
        return new_response
    


class ProxyMiddleware(object):    
    
    def get_proxy(self):
        scrapeops_key = os.environ["SCRAPEROPS_API_KEY"]
        proxy = f'http://scrapeops:{scrapeops_key}@proxy.scrapeops.io:5353'
        return proxy

    def process_request(self, request, spider):
        if spider.name == "CentralCommissionForDisciplineInspection" or spider.name == "tribunnews":
            proxy = self.get_proxy()
            print(f"Assiging proxy {proxy}")
            request.meta['proxy'] = proxy
        
        
class HeadlessBrowserProxy(object):
    """
    In order to use this middleware, you need to add it to the middlewares in the spider class
    vars:
        HEADLESS_BROWSER_WAIT_TIME in the spider class : it defaults to 30000 milliseconds
    """
    def get_proxy(self, url, timeout): # timeout in milliseconds
        scrapeops_key = os.environ["SCRAPEROPS_API_KEY"]
        proxy_params = {
            'api_key': scrapeops_key,
            'url': url, 
            "wait": timeout
        }
        return f'https://proxy.scrapeops.io/v1/?{urlencode(proxy_params)}'
        
    def process_request(self, request, spider, timeout=None):
        
        HEADLESS_BROWSER_WAIT_TIME = getattr(spider, "HEADLESS_BROWSER_WAIT_TIME", 40000)

        url = self.get_proxy(request.url, HEADLESS_BROWSER_WAIT_TIME)

        if timeout:
            response = requests.get(url, timeout=(timeout, None))
        else:
            response = requests.get(url)

        new_response = HtmlResponse(
            url=request.url,
            body=response.content,
            encoding='utf-8',
            status=response.status_code
        )
        return new_response