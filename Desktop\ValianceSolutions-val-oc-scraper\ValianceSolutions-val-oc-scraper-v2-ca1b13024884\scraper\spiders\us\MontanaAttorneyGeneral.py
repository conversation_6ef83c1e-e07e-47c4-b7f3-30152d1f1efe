from scraper.utils.helper import body_normalization
from scraper.OCSpider import OCSpider

class MontanaAttorneyGeneral(OCSpider):
    name = 'MontanaAttorneyGeneral'       
    
    start_urls_names = {
        'https://dojmt.gov/category/press-release/':'News'
    }

    country = "US"

    proxy_country = "us"

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES":
            {
                'scraper.middlewares.GeoProxyMiddleware': 350,
            },
        "DOWNLOAD_DELAY": 6,
    }

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "US/Central"
    
    @property
    def language(self):
        return "English"

    def get_articles(self, response) -> list:
        return response.xpath("//ul[@class='fusion-grid fusion-grid-1 fusion-flex-align-items-flex-start fusion-grid-posts-cards']//a//@href").getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//h3//text()").get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='fusion-content-tb fusion-content-tb-1']/p//text()").getall())

    def get_images(self, response) -> list:
        return []
    
    def get_document_urls(self, response, entry=None):
        return response.xpath("//div[@class='fusion-content-tb fusion-content-tb-1']/p/a[contains(@href,'pdf')]/@href").getall()
                              
    def get_authors(self, response):
        return []
    
    def date_format(self) -> str:
        return '%B %d, %Y'

    def get_date(self, response) -> str:
        date_text =response.xpath("//div[@class='fusion-meta-tb fusion-meta-tb-1 floated']/span[@class='fusion-tb-published-date']/text()").get()
        date_path = date_text.split(": ", 1)
        date = date_path[1]
        return date
    
    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        next_page = response.xpath("//div[@class='pagination clearfix']/a[@class='pagination-next']/@href").get()
        if next_page:
            return next_page
        else:
            return None