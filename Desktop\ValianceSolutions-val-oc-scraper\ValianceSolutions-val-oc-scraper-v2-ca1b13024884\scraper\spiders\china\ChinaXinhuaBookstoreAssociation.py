from scraper.OCSpider import OCSpider
import re
from typing import Optional
from scraper.utils.helper import body_normalization

class ChinaXinhuaBookstoreAssociation(OCSpider):
    name = 'ChinaXinhuaBookstoreAssociation'

    start_urls_names = {
        "https://www.xinhuabookstores.cn/status/news.php?class1=79": "协会动态",
        "https://www.xinhuabookstores.cn/hangye/news.php?class1=120": "行业资讯",
        "https://www.xinhuabookstores.cn/tongzhi/show.php?id=121": "通知公告",
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return 'IndustryAssociation'

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_articles(self, response) -> list:
        return response.xpath('//li[@class="media media-lg border-bottom1"]//h4//a/@href').getall()
  
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//h1/text()").get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//section[@class="met-editor clearfix"]//p//text()').getall())
    
    def date_format(self) -> str:
        return "%Y-%m-%d %H:%M:%S"
    
    # Note:
    # Date is not visibly present on the website.  
    # However, it is found within a commented section of the HTML source code.
    # This function specifically extracts the date from the comments
    def get_date(self, response):
        html_content = response.text
        # Using regex to find the comment containing the date
        comment_date_match = re.search(r"<!--\s*<span>(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})</span>", html_content)
        return comment_date_match.group(1)

    def get_authors(self, response) -> str:
        return []
    
    def get_images(self, response) -> list:
        images = response.xpath("//img/@src").getall()
        return [response.urljoin(img) for img in images] if images else []
    
    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> Optional[str]:
        try:
            next_page = response.xpath("//a[@class='NextA']/@href").get()
            if next_page:
                return response.urljoin(next_page)
            self.logger.info("No next page found.")
            return None
        except Exception as e:
            self.logger.error(f"Error extracting next page link from page {response.url}: {e}")
            return None