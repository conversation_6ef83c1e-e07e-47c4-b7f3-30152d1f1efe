from typing import List
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
import re
from datetime import datetime
import json
from urllib.parse import urljoin

class AlaskaGovernorMinimal(OCSpider):
    name = "alaskagovernorminimal"
    
    country = "US"
    
    # Start URL for the newsroom page
    start_urls_names = {
        "https://gov.alaska.gov/newsroom/page/1/?el_dbe_page": "Alaska Governor Newsroom",
    }
    
    custom_settings = {
        "DOWNLOAD_DELAY": 2,
    }
    
    charset = "utf-8"
    
    @property
    def source_type(self) -> str:
        return "Government"
    
    @property
    def timezone(self) -> str:
        return "America/Anchorage"
    
    def get_page_flag(self) -> bool:
        return False  # Simplified: no pagination in minimal version
    
    def get_articles(self, response) -> list:
        # Extract article URLs from the page
        articles = response.xpath('//article//h4/a/@href').getall()
        return [response.urljoin(link) for link in articles]
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response, entry=None) -> str:
        title = response.xpath('//h1/text()').get()
        return title.strip() if title else "No title found"
    
    def get_body(self, response, entry=None) -> str:
        body_parts = response.xpath('//div[@class="entry-content"]//p/text()').getall()
        return body_normalization(body_parts)
    
    def date_format(self) -> str:
        return "%b %d, %Y"
    
    def get_date(self, response, entry=None) -> int:
        date_str = response.xpath('//article//time/text()').get()
        if date_str:
            try:
                date_obj = datetime.strptime(date_str.strip(), self.date_format())
                return int(date_obj.timestamp())
            except Exception:
                pass
        return int(datetime.now().timestamp())
    
    def get_authors(self, response, entry=None) -> List[str]:
        return []
    
    def get_images(self, response, entry=None) -> List[str]:
        img_urls = response.xpath('//div[@class="entry-content"]//img/@src').getall()
        return [urljoin(response.url, img) for img in img_urls]
    
    def get_next_page(self, response) -> List[str]:
        return []  # No pagination in minimal version
    
    def parse(self, response):
        # Get all article URLs from the page
        article_urls = self.get_articles(response)
        
        # Follow each article URL
        for url in article_urls:
            yield response.follow(url, callback=self.parse_article)
    
    def parse_article(self, response):
        # Create a dictionary to store the article data
        article = {}
        
        # Extract the required fields
        article['url'] = response.url
        article['title'] = self.get_title(response)
        article['body'] = self.get_body(response)
        
        # Get images and convert to JSON string
        images = self.get_images(response)
        article['images'] = json.dumps(images)
        
        # Get date as timestamp and convert to string
        date_timestamp = self.get_date(response)
        article['date'] = datetime.fromtimestamp(date_timestamp).strftime('%Y-%m-%d %H:%M:%S')
        
        # Increment the articles_crawled counter
        self.crawler.stats.inc_value('articles_crawled')
        self.crawler.stats.inc_value('articles_successfully_scraped')
        
        # Return the article data
        return article
