import json
from scraper.OCSpider import OCSpider
from dotenv import load_dotenv
from scraper.utils.helper import body_normalization
import scrapy
from scraper.middlewares import HeadlessBrowserProxy
load_dotenv()


class ChinaFruitMarketingAssociation(OCSpider):
    name = "ChinaFruitMarketingAssociation"

    start_urls_names = {
        "https://www.china-fruit.com.cn/site/term/424.html": "要闻",
    }
    
    api_start_urls = {
        ("https://www.china-fruit.com.cn/site/term/424.html","协会动态"): {
            "url": "https://pcweb.xiehuiyi.com/pcWeb/article/list?categorySns=1303&size=10&page=1&branchId=0&shId=681",
            "payload": {
                "categorySns": "1303",
                "size": "10",
                "page": "1",
                "branchId": "0",
                "shId": "681"
            },
            "headers" : {
                'Referer': ' https://www.china-fruit.com.cn/',
                'Sign': 'ded938b4d0ddece85653484370e2c4f0',
                'SignTimestamp': '1737976683409',
                'Content-Type': 'text/plain'          
            },
        },
        ("https://www.china-fruit.com.cn/site/term/424.html","行业政策"): {
             "url": "https://pcweb.xiehuiyi.com/pcWeb/article/list?categorySns=5757&size=10&page=1&branchId=0&shId=681",
            "payload": {
                "categorySns": "5757",
                "size": "10",
                "page": "1",
                "branchId": "0",
                "shId": "681"
            },
            "headers" : {
                'Referer': ' https://www.china-fruit.com.cn/',
                'Sign': 'd0c646fb0943f0335ea9ee5638bb3a0a',
                'SignTimestamp': '1737976684626',
                'Content-Type': 'text/plain'             
            },
        },
        ("https://www.china-fruit.com.cn/site/term/424.html","产业聚焦"): {
             "url": "https://pcweb.xiehuiyi.com/pcWeb/article/list?categorySns=5758&size=10&page=1&branchId=0&shId=681",
            "payload": {
                "categorySns": "5758",
                "size": "10",
                "page": "1",
                "branchId": "0",
                "shId": "681",
            },
            "headers" : {
                'Referer': ' https://www.china-fruit.com.cn/',
                'Sign': '46351f1825ba23394a6bde6a6144a2e4',
                'SignTimestamp': '1737976686415',
                'Content-Type': 'text/plain'
                         
            },
        },
        ("https://www.china-fruit.com.cn/site/term/424.html","市场行情"): {
             "url": "https://pcweb.xiehuiyi.com/pcWeb/article/list?categorySns=5759&size=10&page=1&branchId=0&shId=681",
            "payload": {
                "categorySns": "5759",
                "size": "10",
                "page": "1",
                "branchId": "0",
                "shId": "681",
            },
            "headers" : {
                'Referer': ' https://www.china-fruit.com.cn/',
                'Sign': '69d8f846a98fc54bd70be6c92ecdfdb0',
                'SignTimestamp': '1737976688287',
                'Content-Type': 'application/json'
            },
        },
    }

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        for api_data in self.api_start_urls.values():
            payload = api_data["payload"]
            headers = api_data["headers"]
            categorySns = payload.get("categorySns")
            categorySns=int(categorySns)
            url=f"https://pcweb.xiehuiyi.com/pcWeb/article/list?categorySns={categorySns}&size=10&page=1&branchId=0&shId=681"
            yield scrapy.Request(
                url = url,
                method = "GET",
                headers=headers,
                body = json.dumps(payload),
                callback = self.parse,   
                meta={
                    "start_url": start_url,
                    "api_url": api_data["url"],
                    "payload": payload,
                    "categorySns": categorySns
                },
            )
        
    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"
    
    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_articles(self, response):
        data = json.loads(response.text)
        article_urls=[]
        hbp = HeadlessBrowserProxy()
        for item in data.get("data", {}).get("list", []):
            id=item.get('id')
            url=hbp.get_proxy(f"https://www.china-fruit.com.cn/article/13_1303_0_{id}.html",timeout=40000)
            article_urls.append(url)
        return article_urls
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response):
        return response.xpath("//h1[@class='text-center']//text()").get()

    def get_body(self, response):
        return body_normalization(response.xpath("//div[@class='inner-html']//p//text()").getall())
    
    def get_images(self, response):
        return response.xpath("//img[@class='rich_pages wxw-img js_insertlocalimg']//@src").getall()
    
    def date_format(self) -> str:
        return '%Y-%m-%d'

    def get_date(self, response):
        return response.xpath('//ul[@class="news-html-meta"]//text()').re_first(r"\d{4}-\d{2}-\d{2}")
    
    def get_authors(self, response):
        return []
    
    def get_page_flag(self):
        return False
    
    # Next pages not present 
    def get_next_page(self, response):
        return None