from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization

class WashingtonStateDepartmentOfCommerce(OCSpider):
    name = 'WashingtonStateDepartmentOfCommerce'
    
    country = "US"

    start_urls_names = {
        'https://www.commerce.wa.gov/news/media-archive/': 'News'
    }
    
    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350
            },
        "DOWNLOAD_DELAY": 2,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }
    HEADLESS_BROWSER_WAIT_TIME = 20000
    charset = "utf-8"
    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Pacific"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@class= "card-post__image"]/a/@href').getall() 
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self,response) -> str:
        return response.xpath('//h1[@class= "entry-title"]/text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class= "entry-content"]/p/text()').getall())
    
    def get_images(self, response) -> list[str]:
        return response.xpath('//div[@class= "entry-content"]/div//img/@src').getall()

    def date_format(self) -> str:
        return "%B %d, %Y"
    
    def get_date(self, response) -> str:
        return response.xpath('//span[@class= "posted-on"]/time/text()').re_first(r"([A-Za-z]+ \d{1,2}, \d{4})")
    
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        next_page = response.xpath('//div[@class="pagination"]/a[contains(@class, "next")]/@href').get()

        if next_page:
            return response.urljoin(next_page) 
        else:
            return None
    
    
    