from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import json
import scrapy
from scraper.middlewares import HeadlessBrowserProxy
from datetime import datetime

class hundsun(OCSpider):
    name = "hundsun"

    start_urls_names = {
       "https://www.hundsun.com/newslist?ctype=25&cid=42&crems=%7B%22title%22%3A%22%E5%85%AC%E5%8F%B8%E6%96%B0%E9%97%BB%22,%22path%22%3A%22%2Fnews%2Fcompany%22%7D": "Company News",
        "https://www.hundsun.com/newslist?ctype=26&cid=42&crems=%7B%22title%22%3A%22%E5%85%AC%E5%8F%B8%E6%96%B0%E9%97%BB%22,%22path%22%3A%22%2Fnews%2Fcompany%22%7D":"Company News",
       "https://www.hundsun.com/news/product":"Product News",
       "https://www.hundsun.com/investornotice?title=%25E4%25B8%25B4%25E6%2597%25B6%25E5%2585%25AC%25E5%2591%258A&id=34":"Temporary Announcement",
      "https://www.hundsun.com/investornotice?title=%25E5%25AE%259A%25E6%259C%259F%25E5%2585%25AC%25E5%2591%258A&id=35":"Regular Announcements"
    }

    api_start_urls = {
        'https://www.hundsun.com/newslist?ctype=25&cid=42&crems=%7B%22title%22%3A%22%E5%85%AC%E5%8F%B8%E6%96%B0%E9%97%BB%22,%22path%22%3A%22%2Fnews%2Fcompany%22%7D':{
            'url' : 'https://www.hundsun.com/v1api/index.php/news/gsxwpage',
            'payload': {
                "pageNo": "1",
                "pageSize": "10",
                "ctype": "25",
                "cid": "42",
            }
        },
   
    'https://www.hundsun.com/newslist?ctype=26&cid=42&crems=%7B%22title%22%3A%22%E5%85%AC%E5%8F%B8%E6%96%B0%E9%97%BB%22,%22path%22%3A%22%2Fnews%2Fcompany%22%7D':{
        'url':"https://www.hundsun.com/v1api/index.php/news/gsxwpage",
        'payload':{
           "pageNo": "1",
                "pageSize": "10",
                "ctype": "26",
                "cid": "42",
        }
    },

    'https://www.hundsun.com/news/product':{
        'url':"https://www.hundsun.com/v1api/index.php/news/cpdt",
        'payload':{
           "pageNo": "1",
                "pageSize": "10"
        }
    },

    'https://www.hundsun.com/investornotice?title=%25E4%25B8%25B4%25E6%2597%25B6%25E5%2585%25AC%25E5%2591%258A&id=34':{
        'url':"https://www.hundsun.com/v1api/index.php/invest/initPageByTypeid",
        'payload':{
            "id":"34",
           "pageNo": "1",
                "pageSize": "22"
        }
    },

     'https://www.hundsun.com/investornotice?title=%25E5%25AE%259A%25E6%259C%259F%25E5%2585%25AC%25E5%2591%258A&id=35':{
        'url':"https://www.hundsun.com/v1api/index.php/invest/initPageByTypeid",
        'payload':{
            "id":"35",
           "pageNo": "1",
                "pageSize": "22"
        }
    },
}

    article_date_map = {}

    article_data_map ={}

    def parse_intermediate(self, response):
        start_url = response.request.meta['start_url']
        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            self.logger.error(f"No API configuration found for start_url: {start_url}")
            return
        payload = dict(api_data["payload"])
        payload["pageNo"] = "1"
        yield scrapy.FormRequest(
            url=api_data["url"],
            method="POST",
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            formdata=payload,
            callback=self.parse,
            dont_filter=True,
            meta={
                "start_url": start_url,
                "api_url": api_data["url"],
                "payload": payload,
                "current_page": 1
            },
        )

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"
    
    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        start_url = response.meta.get("start_url")
        ctype = (
            self.api_start_urls.get(start_url, {})
            .get("payload", {})
            .get("ctype", "0")
        )
        data = json.loads(response.text)
        article_list = data.get("data", {}).get("list", [])
        urls = []
        for article in article_list:
            article_id = article.get("id")
            pub_date = article.get("timeline")
            title = article.get("title")
            if article_id:
                article_url = f"https://www.hundsun.com/newsview?id={article_id}&ctype={ctype}"
                if "crems" in start_url:
                    from urllib.parse import urlparse, parse_qs
                    query_params = parse_qs(urlparse(start_url).query)
                    crems = query_params.get("crems", [])
                    if crems:
                        crems_encoded = crems[0]
                        article_url += f"&crems={crems_encoded}"

                urls.append(article_url)
                if pub_date:
                    self.article_date_map[article_url] = pub_date
                article_data = {}
                if title:
                    article_data["title"] = title
                files = article.get("files")
                if files and files.get("url"):
                    article_data["pdf"] = [f"https://www.hundsun.com/upload/{files['url']}"]
                if article_data:
                    self.article_data_map[article_url] = article_data
        return urls

    def get_href(self, entry) -> str:
        return f'https://proxy.scrapeops.io/v1/?api_key=5c94e88c-bde8-4886-8028-00e490746506&url={entry}&wait=10000'
    
    def get_title(self, response) -> str:
        entry_url = response.request.meta.get('entry')
        title = self.article_data_map.get(entry_url, {}).get("title")
        if title:
            return title
        else:
            title = response.xpath("//div[@class='box1']//div[@class='txt1']/text()").get()
            return title.strip() if title else ""

    def get_body(self, response):
        print(response.text)
        texts = response.xpath("//p//text()").getall()
        return body_normalization(texts) if texts else ""

    def get_images(self, response):
        return []

    def date_format(self) -> str:
        return '%Y-%m-%d'
    
    def get_date(self, response):
        entry_url = response.meta.get("entry", response.url)
        date_str = self.article_date_map.get(entry_url)
        if date_str:
            try:
                date_obj = datetime.strptime(date_str.strip(), "%Y-%m-%d")
                return date_obj.strftime(self.date_format())
            except ValueError:
                self.logger.warning(f"Date format error for URL {response.url}: {date_str}")
        return None

    def get_authors(self, response):
        return []
    
    def get_document_urls(self, response, entry=None):
        entry_url = response.meta.get("entry", response.url)
        return self.article_data_map.get(entry_url, {}).get("pdf", [])

    def get_page_flag(self) -> bool:
        return True
    
    def go_to_next_page(self, response, start_url=None, current_page=1):
        start_url = response.meta.get("start_url")
        payload = response.meta.get("payload")
        current_page = response.meta.get("current_page", 1)
        data = json.loads(response.text)
        total_pages = data.get("data", {}).get("totalPage", 1)
        for url in self.get_articles(response):
            yield scrapy.Request(url=url, callback=self.parse_article, meta={"start_url": start_url,"entry": url })
        if current_page < total_pages:
            next_page = current_page + 1
            new_payload = dict(payload)
            new_payload["pageNo"] = str(next_page)

            yield scrapy.FormRequest(
                url=response.meta.get("api_url"),
                method="POST",
                headers={"Content-Type": "application/x-www-form-urlencoded"},
                formdata=new_payload,
                callback=self.parse,
                meta={
                    "start_url": start_url,
                    "api_url": response.meta.get("api_url"),
                    "payload": payload,
                    "current_page": next_page
                },
                dont_filter=True
            )