from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class RANDCorporation(OCSpider):
    name = "RANDCorporation"

    country="US"

    start_urls_names = {
        "https://www.rand.org/pubs.html" : "Press Releases",
    }

    charset = "utf-8"

    @property
    def language(self):
        return "English"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "US/Eastern"
    
    def get_articles(self, response) -> list:
        return response.xpath("//ul[@class='teaser-list teasers filterable list hasImg']//li//a//@href").getall()
       
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//h1//text()").get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='body-text']//p//text() | //div[@class='abstract product-page-abstract']//text()").getall())

    def get_images(self, response, entry=None) :
        return response.xpath("//div[@class='photo hero-image']//img//@src").getall()
    
    def get_authors(self, response, entry=None) -> list[str]:
        return response.xpath("//p[@class='authors']/text()").getall()

    def date_format(self) -> str:
        return "%b %d, %Y"
    
    def get_date(self, response) -> str:
        date = response.xpath("//div[@class='type-date']//p[@class='date']//text() | //div[@class='text']//p[@class='type-published']//span[@class='published']//text()").get()
        date = date.replace("Posted on rand.org ", "").replace("Published ","")
        return date 
    
    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        next_page=response.xpath("//li[@class='pagination-item next']//a//@href").get()
        if next_page:
            return next_page
        else:
            return None