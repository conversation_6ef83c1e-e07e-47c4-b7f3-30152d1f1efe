from typing import List
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
import re
from datetime import datetime
from urllib.parse import urljoin

class <PERSON>amScraper(OCSpider):
    name = "<PERSON><PERSON>Scraper"
    
   
    
    # Start URLs for the ROBAM website
    start_urls_names = {
        "https://www.robam.com/news.html": "ROBAM News",
        "https://www.robam.com/about/investment.html": "ROBAM Investment",
        "https://www.robam.com/about/company.html": "ROBAM Company",
    }
    
    # custom_settings = {
    #     "DOWNLOAD_DELAY": 2,
    # }
    
    charset = "utf-8"
    
    @property
    def source_type(self) -> str:
        return "Corporate"
    
    @property
    def language(self) -> str:
        return "Chinese"
    
    @property
    def timezone(self) -> str:
        return "Asia/Shanghai"
    
    def get_page_flag(self) -> bool:
        return True
    
    def get_articles(self, response) -> list:
        # Extract article URLs from the page
        # For news page
        if "news.html" in response.url:
            articles = response.xpath('//div[@class="news-list"]//a/@href').getall()
        # For investment page
        elif "investment.html" in response.url:
            articles = response.xpath('//div[@class="investment-list"]//a/@href').getall()
        # For company page
        elif "company.html" in response.url:
            articles = response.xpath('//div[@class="company-info"]//a/@href').getall()
        else:
            articles = response.xpath('//a[contains(@href, ".html")]/@href').getall()
            
        # Filter out navigation links and other non-article links
        articles = [link for link in articles if not link.startswith('#') and not link.startswith('javascript:')]
        
        return [response.urljoin(link) for link in articles]
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response, entry=None) -> str:
        # Try multiple selectors to find the title
        title = response.xpath('//h1/text()').get()
        if not title:
            title = response.xpath('//div[@class="article-title"]/text()').get()
        if not title:
            title = response.xpath('//div[@class="news-detail-title"]/text()').get()
        if not title:
            title = response.xpath('//title/text()').get()
        
        return title.strip() if title else ""
    
    def get_body(self, response, entry=None) -> str:
        # Try multiple selectors to find the content
        body_parts = response.xpath('//div[@class="news-detail-content"]//text()|').getall()
        if not body_parts:
            body_parts = response.xpath('//div[@class="article-content"]//text()').getall()
        if not body_parts:
            body_parts = response.xpath('//div[@class="content"]//text()').getall()
            
        return body_normalization(body_parts)
    
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response, entry=None) -> int:
        # Try to find the date in the article page
        date_str = response.xpath('//div[@class="news-detail-date"]/text()').get()
        if not date_str:
            date_str = response.xpath('//span[@class="date"]/text()').get()
        if not date_str:
            date_str = response.xpath('//div[contains(@class, "date")]/text()').get()
        
        if date_str:
            # Clean up the date string
            date_str = date_str.strip()
            # Remove any non-date text
            date_match = re.search(r'(\d{4}[-/]\d{1,2}[-/]\d{1,2})', date_str)
            if date_match:
                date_str = date_match.group(1).replace('/', '-')
                
            try:
                # Parse the date string
                date_obj = datetime.strptime(date_str, self.date_format())
                return int(date_obj.timestamp())
            except Exception as e:
                self.logger.error(f"Error parsing date: {e}")
                
        # If we can't find or parse the date, try to extract it from the URL
        try:
            url_match = re.search(r'/(\d{4})/(\d{2})/(\d{2})/', response.url)
            if url_match:
                year, month, day = url_match.groups()
                date_obj = datetime(int(year), int(month), int(day))
                return int(date_obj.timestamp())
        except Exception as e:
            self.logger.error(f"Error extracting date from URL: {e}")
            
        # Return current time if date not found
        return int(datetime.now().timestamp())
    
    def get_authors(self, response, entry=None) -> List[str]:
        # Extract the article authors
        authors = []
        
        # Try to find the author in the article
        author = response.xpath('//span[@class="author"]/text()').get()
        if not author:
            author = response.xpath('//div[@class="author"]/text()').get()
        if not author:
            # Try to find author in byline
            author = response.xpath('//p[contains(text(), "作者") or contains(text(), "来源")]/text()').get()
            if author and ("作者" in author or "来源" in author):
                author = re.sub(r'(作者|来源)[：:]\s*', '', author).strip()
        
        # Check if we found an author
        if author:
            author = author.strip()
            authors.append(author)
        
        return authors
    
    def get_images(self, response, entry=None) -> List[str]:
        # Extract image URLs from the article
        images = []
        
        # Try to find images in the article content
        img_urls = response.xpath('//div[@class="news-detail-content"]//img/@src').getall()
        if not img_urls:
            img_urls = response.xpath('//div[@class="article-content"]//img/@src').getall()
        if not img_urls:
            img_urls = response.xpath('//div[@class="content"]//img/@src').getall()
            
        for img in img_urls:
            img_url = urljoin(response.url, img)
            images.append(img_url)
        
        return images
    
    def get_next_page(self, response) -> List[str]:
        # Extract the next page URL for pagination
        next_page = response.xpath('//a[contains(text(), "下一页") or contains(@class, "next")]/@href').get()
        if next_page:
            return [response.urljoin(next_page)]
            
        return []
    
    def get_document_urls(self, response, entry=None) -> list:
        # Extract document URLs (PDFs, etc.) from the article
        docs = response.xpath('//a[contains(@href, ".pdf") or contains(@href, ".doc") or contains(@href, ".docx")]/@href').getall()
        return [urljoin(response.url, doc) for doc in docs]
    
    def get_meta(self, response, entry=None) -> list:
        # Required by OCSpider but not used in our implementation
        return []
    
    def get_pdf(self, response, entry=None):
        # Required by OCSpider but not used in our implementation
        return None
