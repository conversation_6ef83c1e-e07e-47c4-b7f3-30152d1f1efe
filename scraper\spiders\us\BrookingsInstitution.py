from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
import scrapy
from typing import Union
import json
from datetime import datetime

class BrookingsInstitution(OCSpider):
    name = 'BrookingsInstitution'
    
    country = "US"

    start_urls_names = {
        'https://www.brookings.edu/for-media/': 'News',
    }

    api_start_url = {
        'https://www.brookings.edu/for-media/': {
            'url': 'https://xgc391w2we-1.algolianet.com/1/indexes/*/queries?x-algolia-agent=Algolia%20for%20JavaScript%20(4.18.0)%3B%20Browser%20(lite)%3B%20instantsearch.js%20(4.56.5)%3B%20JS%20Helper%20(3.13.2)&x-algolia-api-key=********************************&x-algolia-application-id=XGC391W2WE',
            'payload': {
                "requests": [
                    {
                        "indexName": "prod_searchable_posts",
                        "params": "distinct=true&facets=[\"content_type\",\"tax_ids.topic_tax\",\"tax_ids.region_tax\",\"expert_ids\",\"entity_ids\",\"locale\"]&filters=(post_type:news)%20AND%20(locale:en)&highlightPostTag=__/ais-highlight__&highlightPreTag=__ais-highlight__&hitsPerPage=10&maxValuesPerFacet=30000&page=0&query=&tagFilters="
                    }
                ]
            },
            'headers': {
                "Content-Type": "application/json"
            }
        }
    }

    def parse_intermediate(self, response):
        start_url = response.meta.get('start_url')
        api_data = self.api_start_url.get(start_url)
        if not api_data:
            return
        api_url = api_data["url"]
        payload = api_data["payload"].copy()
        headers = api_data["headers"]
        current_page = response.meta.get("current_page", 1)
        page_for_payload = current_page - 1
        original_params = self.api_start_url[start_url]['payload']['requests'][0]['params']
        params_parts = original_params.split('&')
        new_params = "&".join([part if not part.startswith("page=") else f"page={page_for_payload}" for part in params_parts])
        payload["requests"][0]["params"] = new_params
        yield scrapy.Request(
            url=api_url,
            method="POST",
            headers=headers,
            body=json.dumps(payload),
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "payload": payload,
                "current_page": current_page
            },
            dont_filter=True,
        )

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"

    @property
    def timezone(self) -> str:
        return "America/Chicago"

    def get_articles(self, response) -> list:
        try:
            data = json.loads(response.text)
            hits = data.get("results", [])[0].get("hits", [])
            article_urls = [article.get("permalink") for article in hits if article.get("permalink")]
            return article_urls
        except json.JSONDecodeError:
            return []


    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="news-individual__headline"]/text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[contains(@class, 'byo-block')]//p/text()").getall())

    def get_images(self, response) -> list[str]:
        return []

    def date_format(self) -> str:
        return '%Y-%m-%d'

    def get_date(self, response) -> str:
        date_text = response.xpath("//div[contains(@class, 'news-individual__date')]/text()").re_first(r"\w+\s\d{1,2},\s\d{4}")
        if date_text:
            return datetime.strptime(date_text, "%B %d, %Y").strftime("%Y-%m-%d")
        else:
            return None

    def get_authors(self, response):
        return ""

    def get_next_page(self, response, current_page) -> Union[None, int]:
        try:
            data = json.loads(response.text)
            total_pages = data["results"][0]["nbPages"]
            if current_page < total_pages:
                return current_page + 1
        except (json.JSONDecodeError, KeyError, TypeError):
            pass
        return None

    def get_page_flag(self) -> bool:
        return True

    def go_to_next_page(self, response, start_url, current_page=1):
        api_url = response.meta.get("api_url")
        if not api_url:
            return
        next_page = self.get_next_page(response, current_page)
        if next_page:
            payload = response.meta.get("payload", {}).copy()
            original_params = self.api_start_url[start_url]['payload']['requests'][0]['params']
            params_parts = original_params.split('&')
            new_params = "&".join([part if not part.startswith("page=") else f"page={next_page - 1}" for part in params_parts])
            payload["requests"][0]["params"] = new_params
            yield scrapy.Request(
                url=api_url,
                method="POST",
                body=json.dumps(payload),
                headers=self.api_start_url[start_url]["headers"],
                callback=self.parse_intermediate,
                meta={
                    'current_page': next_page,
                    'start_url': start_url,
                    'api_url': api_url,
                    'payload': payload
                },
                dont_filter=True
            )