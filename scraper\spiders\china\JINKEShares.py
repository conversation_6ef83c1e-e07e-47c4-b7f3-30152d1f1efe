from typing import List
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class JINKEShares(OCSpider):
    name = "JINKEShares"

    start_urls_names = {
        "https://www.jinke.com/news/jt/": "金科股份",#No pdf
        # "https://www.jinke.com/news/tt/": "金科股份",
        # "https://www.jinke.com/news/gg/": "金科股份", #No pdf
        # "https://www.jinke.com/investor/report/": "金科股份",
        # "https://www.jinke.com/investor/new/": "金科股份",
    }

    article_data_map ={}

    charset = "iso-8859-1"

    @property
    def source_type(self) -> str:
        return "SOE"
    
    @property
    def timezone(self) -> str:
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
            articles = []
            for article in response.xpath("//div[@class='ny_xwff2_a']//ul//li"):
                url = article.xpath(".//a/@href").get()
                title = article.xpath(".//div[@class='ny_xwff2_a2']//h3//text()").get()
                day = article.xpath(".//div[@class='ny_xwff2_a1']//h3//text()").get()
                month = article.xpath(".//div[@class='ny_xwff2_a1']//p//text()").get()
                date = month+"."+day
                if url and title and date:
                    full_url = url
                    title = title.strip()
                    if '.pdf' in full_url.lower():
                        pdf = full_url
                    else:
                        pdf = "None"
                    clean_date = date.strip()
                    self.article_data_map[full_url] = {"title": title, "date": clean_date, "pdf": pdf}
                    articles.append(full_url) 
            return articles
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response, entry=None) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response, entry=None) -> str:
        if ".pdf" in response.url.lower():
            return ""
        return body_normalization(response.xpath("//div[@id='js_content']//text()").getall())
        
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response, entry=None) -> int:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        
    def get_authors(self, response, entry=None) -> List[str]:
        return []
    
    def get_document_urls(self, response, entry=None) -> list:
        pdf_url = self.article_data_map.get(response.request.meta.get('entry'), {}).get("pdf")
        return [pdf_url] if pdf_url and pdf_url != "None" else []

    
    def get_images(self, response, entry=None) -> List[str]:
        if ".pdf" in response.url.lower():
            return ""
        return response.xpath("//img//@src").getall()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> List[str]:
        next_page= response.xpath("//div[@class='paget']//ul//li[last()]/a/@href").get()
        if next_page:
            return next_page
        return None