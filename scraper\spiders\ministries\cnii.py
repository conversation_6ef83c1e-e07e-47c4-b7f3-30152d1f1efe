from scraper.OfficialLineSpider import OfficialLineSpider
from scraper.utils.helper import body_normalization
class cnii(OfficialLineSpider):

    name = "cnii"

    source = "吉林日报"

    proxy_country = "cn"
    
    custom_settings = { 
        "DOWNLOADER_MIDDLEWARES" : 
        {
            'scraper.middlewares.GeoProxyMiddleware': 350,
        },
        "DOWNLOAD_DELAY" : 2,            
    }


    start_urls_names = {
        'https://rmydb.cnii.com.cn/html/%s/%s/data.json'
    }

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_start_url(self, url, date):
        return url % (date.strftime('%Y'),date.strftime('%Y%m%d') )

    def get_articles(self, response) -> list:
        input = response.json()
        article_hrefs = [article["articleHref"] for page in input for article in page.get("onePageArticleList", [])]
        self.author_dict = {}
        for page in input:
            for article in page.get("onePageArticleList", []):
                self.author_dict[article["articleHref"]] = article["articleAuthor"]
        return article_hrefs

    def get_href(self, entry) -> str:
        nodes = entry.split("_")
        year = self.date.strftime('%Y')
        return f"https://rmydb.cnii.com.cn/html/{year}/{nodes[0]}/{nodes[0]}_{nodes[1]}/" + entry

    def get_layout(self, response) -> str:
        return ""

    def get_title(self, response) -> str:
        title = body_normalization(response.xpath('//h2[@id="Title"]//text()').extract())
        return title

    def get_subhead(self, response) ->str:
        return body_normalization(response.xpath('//p[@id="SubTitle"]//text()').extract())

    def get_body(self, response) -> str:
        body = body_normalization(response.xpath('//div[@id="ozoom"]//p//text()').extract())
        return body

    def date_format(self) -> str:
        return '%Y_%m_%d'

    def get_date(self, response) -> str:
        return self.date.strftime("%Y_%m_%d")

    def get_images(self, response) -> list:
        image_list = response.xpath('//img[@id="pageImg"]//@src').extract()
        return image_list

    def get_authors(self, response):
        article = response.url.split("/")[-1]
        author = body_normalization([self.author_dict[article]])
        return author if author else ''