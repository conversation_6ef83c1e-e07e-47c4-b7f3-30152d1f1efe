from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
from urllib.parse import urljoin
import re
from datetime import datetime
import pytz

class SseThreeSixtySpider(OCSpider):
    name = "SseThreeSixtySpider"
    country = "CN"

    start_urls_names = {
        "http://www.sse.com.cn/assortment/stock/list/info/announcement/index.shtml?productId=601360": "SSE Announcements",
        "https://www.360.cn/news.html": "360 News",
    }
    charset = "utf-8"
    handle_httpstatus_list = [200, 301, 302, 404, 500]

    # Custom settings for better performance
    custom_settings = {
        "DOWNLOAD_DELAY": 0.5,
        "CONCURRENT_REQUESTS_PER_DOMAIN": 4,
        "DOWNLOAD_FAIL_ON_DATALOSS": False,
        "DOWNLOAD_TIMEOUT": 60,
        "RETRY_TIMES": 3,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',  # Disable duplicate filtering
        "COOKIES_ENABLED": False,  # Disable cookies for better performance
        "REDIRECT_ENABLED": True,  # Enable redirects
    }

    @property
    def source_type(self) -> str:
        return "Corporate"

    @property
    def language(self) -> str:
        return "Chinese"

    @property
    def timezone(self):
        return "Asia/Shanghai"

    # def parse_intermediate(self, response):
    #     """Optional method for headless mode if needed"""
    #     return self.parse(response)

    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="txt"]').getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response, entry=None) -> str:
        title = response.xpath('//title/text()').get()
        return title.strip() if title else ""

    def get_body(self, response, entry=None) -> str:
        body_parts = response.xpath('//div[@class="txt"]//text()').getall()
        return body_normalization(body_parts)
    def get_images(self, response, entry=None) -> list:
        # Extract images based on the website
        if "sse.com.cn" in response.url:
            # For PDF files, no images
            if response.url.lower().endswith('.pdf'):
                return []
            
            # Try multiple XPath patterns for SSE images
            img_xpaths = [
                '//div[@class="content-text"]//img/@src',
                '//div[@class="content"]//img/@src',
                '//div[@id="content"]//img/@src',
                '//div[contains(@class, "content")]//img/@src',
                '//article//img/@src',
                '//img/@src'  # Fallback to all images if needed
            ]
        else:  # 360.cn
            # Try multiple XPath patterns for 360 images
            img_xpaths = [
                '//div[contains(@class, "news-content")]//img/@src',
                '//div[contains(@class, "article-content")]//img/@src',
                '//div[@class="content"]//img/@src',
                '//div[@id="content"]//img/@src',
                '//div[contains(@class, "content")]//img/@src',
                '//article//img/@src',
                '//img/@src'  # Fallback to all images if needed
            ]

        for xpath in img_xpaths:
            img_urls = response.xpath(xpath).getall()
            if img_urls:
                return [urljoin(response.url, img) for img in img_urls]

        return []

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response, entry=None) -> str:
        # Extract date based on the website
        if "sse.com.cn" in response.url:
            # For PDF files, try to extract date from filename or URL
            if response.url.lower().endswith('.pdf'):
                # Try to find date pattern in URL or filename
                date_pattern = r'(\d{4})[_-]?(\d{2})[_-]?(\d{2})'
                match = re.search(date_pattern, response.url)
                if match:
                    year, month, day = match.groups()
                    return f"{year}-{month}-{day}"
                return datetime.now(pytz.timezone(self.timezone)).strftime(self.date_format())
            
            # Try multiple XPath patterns for SSE dates
            date_xpaths = [
                '//div[@class="content-time"]/text()',
                '//div[contains(@class, "date")]/text()',
                '//span[contains(@class, "date")]/text()',
                '//div[contains(@class, "time")]/text()',
                '//span[contains(@class, "time")]/text()'
            ]
        else:  # 360.cn
            # Try multiple XPath patterns for 360 dates
            date_xpaths = [
                '//div[contains(@class, "news-date")]/text()',
                '//div[contains(@class, "article-date")]/text()',
                '//span[contains(@class, "date")]/text()',
                '//div[contains(@class, "time")]/text()',
                '//span[contains(@class, "time")]/text()'
            ]

        for xpath in date_xpaths:
            date_str = response.xpath(xpath).get()
            if date_str and date_str.strip():
                # Try to extract date from the string
                date_pattern = r'(\d{4})[/\-年](\d{1,2})[/\-月](\d{1,2})'
                match = re.search(date_pattern, date_str)
                if match:
                    year, month, day = match.groups()
                    return f"{year}-{int(month):02d}-{int(day):02d}"

        # If no date found, use current date
        return datetime.now(pytz.timezone(self.timezone)).strftime(self.date_format())

    def get_authors(self, response, entry=None) -> list:
        # Extract authors based on the website
        if "sse.com.cn" in response.url:
            # For SSE, usually no author information
            return ["SSE"]
        else:  # 360.cn
            # Try multiple XPath patterns for 360 authors
            author_xpaths = [
                '//div[contains(@class, "author")]/text()',
                '//span[contains(@class, "author")]/text()',
                '//div[contains(@class, "source")]/text()',
                '//span[contains(@class, "source")]/text()'
            ]

            for xpath in author_xpaths:
                author = response.xpath(xpath).get()
                if author and author.strip():
                    # Clean up the author string
                    author = re.sub(r'作者[：:]\s*', '', author.strip())
                    author = re.sub(r'来源[：:]\s*', '', author.strip())
                    if author:
                        return [author]

            # Default author if none found
            return ["360"]

    def get_document_urls(self, response, entry=None) -> list:
        # Look for document links in the page
        doc_patterns = [
            '//a[contains(@href, ".pdf")]/@href',
            '//a[contains(@href, ".doc")]/@href',
            '//a[contains(@href, ".docx")]/@href',
            '//a[contains(@href, ".xls")]/@href',
            '//a[contains(@href, ".xlsx")]/@href',
            '//a[contains(@href, ".ppt")]/@href',
            '//a[contains(@href, ".pptx")]/@href',
            '//a[contains(@href, ".zip")]/@href',
            '//a[contains(@href, ".rar")]/@href',
            '//a[contains(@href, "download")]/@href',
            '//a[contains(text(), "下载")]/@href',
            '//a[contains(text(), "附件")]/@href'
        ]

        doc_urls = []
        for pattern in doc_patterns:
            urls = response.xpath(pattern).getall()
            if urls:
                doc_urls.extend(urls)

        # For SSE, if the current page is a PDF, include it as a document
        if "sse.com.cn" in response.url and response.url.lower().endswith('.pdf'):
            doc_urls.append(response.url)

        # Remove duplicates and convert to absolute URLs
        unique_docs = []
        for url in doc_urls:
            abs_url = urljoin(response.url, url)
            if abs_url not in unique_docs:
                unique_docs.append(abs_url)

        return unique_docs

    def get_next_page(self, response) -> str:
        # Check for pagination links
        next_page_xpaths = [
            '//a[contains(text(), "下一页")]/@href',
            '//a[contains(text(), "Next")]/@href',
            '//a[@class="next"]/@href',
            '//a[contains(@class, "next")]/@href',
            '//li[contains(@class, "next")]/a/@href',
            '//div[contains(@class, "pagination")]//a[contains(text(), "下一页")]/@href',
            '//div[contains(@class, "pagination")]//a[contains(@class, "next")]/@href'
        ]
        
        for xpath in next_page_xpaths:
            next_page = response.xpath(xpath).get()
            if next_page:
                return response.urljoin(next_page)
        
        # Check for page number in URL
        if "page=" in response.url:
            current_page_match = re.search(r'page=(\d+)', response.url)
            if current_page_match:
                current_page = int(current_page_match.group(1))
                next_page = current_page + 1
                next_url = re.sub(r'page=\d+', f'page={next_page}', response.url)
                return next_url
        
        return None

    def get_page_flag(self, response) -> bool:
        # Determine if this is a page that should be parsed for articles
        # For SSE, only parse pages that are not PDF files
        if "sse.com.cn" in response.url and response.url.lower().endswith('.pdf'):
            return False
        
        # For 360, only parse pages that are not article pages
        if "360.cn" in response.url and "/n/" in response.url:
            return False
        
        return True
