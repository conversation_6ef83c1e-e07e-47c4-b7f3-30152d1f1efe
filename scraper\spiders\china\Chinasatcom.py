from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from typing import List
from datetime import datetime
from typing import Optional
import re
from urllib.parse import urljoin
from urllib.parse import urlparse, urlunparse



def normalize_url(url: str) -> str:
    """
    Normalize a URL by resolving redundant path elements like '../'
    """
    parsed = urlparse(url)
    # Clean path segments
    parts = []
    for segment in parsed.path.split('/'):
        if segment == '..':
            if parts:
                parts.pop()
        elif segment and segment != '.':
            parts.append(segment)
    clean_path = '/' + '/'.join(parts)
    return urlunparse(parsed._replace(path=clean_path))


class Chinasatcom(OCSpider):
    name = "Chinasatcom"

    start_urls_names = {
        "http://www.chinasatcom.com/n782724/n3338836/index.html": "",
        "http://www.chinasatcom.com/n3338860/n3612494/n3612547/index.html": "",
        
    }

    custom_settings = {
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350,
    },
    }
    
    HEADLESS_BROWSER_WAIT_TIME = 5000

    charset = "utf-8"

    article_date_map = {}

    @property
    def source_type(self) -> str:
        return "SOE"
    
    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        self.extract_articles_with_dates(response)
        base_url = "http://www.chinasatcom.com/"
        relative_links = response.xpath('//ul[@class="erji_list"]//div[@class="list_text2"]/a/@href | //ul[@class="erji_list"]//a/@href').getall()
        full_links = [response.urljoin(link) for link in relative_links]
        return full_links
    
    def normalize_url(url: str) -> str:
        """
        Normalize a URL by resolving any ../ patterns in the path.
        """
        parsed = urlparse(url)
        # Collapse any "../" segments from the path
        normalized_path = re.sub(r"/(\.\./)+", "/", parsed.path)
        return urlunparse(parsed._replace(path=normalized_path))
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        title = response.xpath('//div[@class="con_title"]/p//text()').get()
        return title.strip() if title else ""
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="content"]//p//text()').getall())
    
    # def get_images(self, response) -> list:
    #     return response.xpath('//div[@class="acc_content"]//img/@src').getall()
       
    def date_format(self) -> str:
        return"%Y-%m-%d"
    
    def get_date(self, response) -> str:
        """
        Extract date from article_date_map using the response URL, or directly from HTML using regex.
        """
        url = normalize_url(response.url)
        date = self.article_date_map.get(url)
        self.logger.info(f"Raw date entry for {url}: {date}")

        if date and date.startswith("发布日期"):
            parts = date.split("：")
            if len(parts) == 2:
                date = parts[1].strip()

        # Fallback: parse raw HTML using regex
        if not date:
            html = response.text
            match = re.search(r"(发布日期|发布时间)[：:]\s*([0-9\-]{10})", html)
            if match:
                date = match.group(2)

        if not date:
            self.logger.warning(f"No date entry found for URL: {url}")
            return None

        return date


    # def get_document_urls(self, response, entry=None) -> list:
    #     pdf = response.xpath("//div[@id='content_warp']//a[contains(@href,'.pdf')]//@href").get()
    #     if pdf:
    #         return f'http://www.spacesat.com.cn/{pdf}'

    def get_images(self, response) -> List[str]:
        image_paths = response.xpath('//div[@class="content"]//img/@src').getall()
        return [response.urljoin(src) for src in image_paths]
    
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]: 
        return None
    
    def extract_articles_with_dates(self, response):
        print("Extracting articles and dates...")
        for li in response.xpath('//ul[@class="erji_list"]/li'):
            url = li.xpath('.//a/@href').get()
            date = li.xpath('.//span/text()').get()
            print(f"Found article: url={url}, date={date}")
            if url and date:
                full_url = normalize_url(response.urljoin(url.strip()))  # Convert to absolute
                cleaned_date = date.strip()
                self.article_date_map[full_url] = cleaned_date
                print(f"Mapped: {full_url} -> {cleaned_date}")
            else:
                print(f"Skipping incomplete article: url={url}, date={date}")
        print("Final article_date_map:", self.article_date_map)
        return self.article_date_map
    
    
