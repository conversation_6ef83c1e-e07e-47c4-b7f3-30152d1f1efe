from scraper.OCSpider import <PERSON><PERSON>pider
from scraper.utils.helper import body_normalization
from urllib.parse import urljoin
import re
from datetime import datetime
import pytz

class ThreeSixtyNewsSpider(OCSpider):
    name = "ThreeSixtyNewsSpider"
    country = "CN"
    start_urls_names = {
        "https://www.360.cn/news.html": "360 News",
    }
    charset = "utf-8"
    handle_httpstatus_list = [200, 301, 302, 404, 500]
    custom_settings = {
        "DOWNLOAD_DELAY": 0.5, 
        "CONCURRENT_REQUESTS_PER_DOMAIN": 4,
        "DOWNLOAD_FAIL_ON_DATALOSS": False,
        "DOWNLOAD_TIMEOUT": 60,
        "RETRY_TIMES": 3,
    }

    @property
    def source_type(self) -> str: 
        return "Corporate"

    @property
    def language(self) -> str: 
        return "Chinese"

    @property
    def timezone(self): 
        return "Asia/Shanghai"

    def parse_intermediate(self, response):
        return self.parse(response)

    def get_articles(self, response) -> list:
        # Extract article links from the news page
        article_links = response.xpath('//div[contains(@class, "news-list")]//a/@href | //a[contains(@href, "/n/")]/@href').getall()
        
        # If not enough links found, try alternative selectors
        if len(article_links) < 5:
            article_links.extend(response.xpath('//div[contains(@class, "news-item")]//a/@href').getall())
            article_links.extend(response.xpath('//div[contains(@class, "news-content")]//a/@href').getall())
            article_links.extend(response.xpath('//a[contains(@href, "12")]/@href').getall())
        
        # Filter and clean links
        filtered_links = []
        for link in article_links:
            if link and not any(nav in link for nav in ['javascript:', '#', 'mailto:', 'tel:']):
                if link.startswith('/n/'):
                    link = 'https://www.360.cn' + link
                filtered_links.append(link)
        
        # Remove duplicates while preserving order
        unique_links = []
        for link in filtered_links:
            if link not in unique_links and '/n/' in link:
                unique_links.append(link)
        
        return unique_links

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response, entry=None) -> str:
        # Try multiple XPath patterns for title
        title_xpaths = [
            '//h1/text()',
            '//div[@class="title"]/text()',
            '//div[contains(@class, "title")]/text()',
            '//title/text()'
        ]
        
        for xpath in title_xpaths:
            title = response.xpath(xpath).get()
            if title and title.strip():
                return title.strip()
        
        return "No title available"

    def get_body(self, response, entry=None) -> str:
        # Try multiple XPath patterns for body content
        body_xpaths = [
            '//div[contains(@class, "news-content")]//text()',
            '//div[contains(@class, "article-content")]//text()',
            '//div[@class="content"]//text()',
            '//div[@id="content"]//text()',
            '//div[contains(@class, "content")]//text()'
        ]
        
        for xpath in body_xpaths:
            body_parts = response.xpath(xpath).getall()
            if body_parts and ''.join(body_parts).strip():
                return body_normalization(body_parts)
        
        return "No content available"

    def get_images(self, response, entry=None) -> list:
        # Try multiple XPath patterns for images
        img_xpaths = [
            '//div[contains(@class, "news-content")]//img/@src',
            '//div[contains(@class, "article-content")]//img/@src',
            '//div[@class="content"]//img/@src',
            '//div[contains(@class, "content")]//img/@src',
            '//img/@src'
        ]
        
        for xpath in img_xpaths:
            img_urls = response.xpath(xpath).getall()
            if img_urls:
                return [urljoin(response.url, img) for img in img_urls if not img.startswith('data:')]
        
        return []

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response, entry=None) -> str:
        # Try multiple XPath patterns for date
        date_xpaths = [
            '//div[contains(@class, "date")]/text()',
            '//span[contains(@class, "date")]/text()',
            '//div[contains(@class, "time")]/text()',
            '//span[contains(@class, "time")]/text()'
        ]
        
        for xpath in date_xpaths:
            date_str = response.xpath(xpath).get()
            if date_str and date_str.strip():
                # Try to extract date from the string
                date_pattern = r'(\d{4})[/\-年](\d{1,2})[/\-月](\d{1,2})'
                match = re.search(date_pattern, date_str)
                if match:
                    year, month, day = match.groups()
                    return f"{year}-{int(month):02d}-{int(day):02d}"
        
        # If no date found, use current date
        return datetime.now(pytz.timezone(self.timezone)).strftime(self.date_format())

    def get_authors(self, response, entry=None) -> list:
        # Try multiple XPath patterns for authors
        author_xpaths = [
            '//div[contains(@class, "author")]/text()',
            '//span[contains(@class, "author")]/text()',
            '//div[contains(@class, "source")]/text()',
            '//span[contains(@class, "source")]/text()'
        ]
        
        for xpath in author_xpaths:
            author = response.xpath(xpath).get()
            if author and author.strip():
                # Clean up the author string
                author = re.sub(r'作者[：:]\s*|来源[：:]\s*', '', author.strip())
                if author:
                    return [author]
        
        # Default author if none found
        return ["360安全中心"]

    def get_document_urls(self, response, entry=None) -> list:
        # Look for document links in the page
        doc_patterns = [
            '//a[contains(@href, ".pdf")]/@href',
            '//a[contains(@href, ".doc")]/@href',
            '//a[contains(@href, ".docx")]/@href',
            '//a[contains(@href, ".xls")]/@href',
            '//a[contains(@href, ".xlsx")]/@href',
            '//a[contains(@href, "download")]/@href',
            '//a[contains(text(), "下载")]/@href'
        ]
        
        doc_urls = []
        for pattern in doc_patterns:
            urls = response.xpath(pattern).getall()
            if urls:
                doc_urls.extend(urls)
        
        # Remove duplicates and convert to absolute URLs
        return [urljoin(response.url, url) for url in set(doc_urls)]

    def get_next_page(self, response) -> str:
        # Check for pagination links
        next_page_xpaths = [
            '//a[contains(text(), "下一页")]/@href',
            '//a[contains(text(), "Next")]/@href',
            '//a[@class="next"]/@href',
            '//a[contains(@class, "next")]/@href',
            '//li[contains(@class, "next")]/a/@href'
        ]
        
        for xpath in next_page_xpaths:
            next_page = response.xpath(xpath).get()
            if next_page:
                return urljoin(response.url, next_page)
        
        return None

    def get_page_flag(self, response=None) -> bool:
        if response is None:
            return True
        # Only parse article pages, not the main listing page
        if response.url == "https://www.360.cn/news.html":
            return True
        if "/n/" in response.url:
            return False
        return True
