from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import scrapy

class RohngshengPetrochemical(OCSpider):
    name = "RohngshengPetrochemical"

    start_urls_names = {
        "https://www.cnrspc.com/xwdt": "荣盛石化",   # This start_url also contains child articles of different websites
        "http://www.cninfo.com.cn/new/disclosure/stock?stockCode=002493&orgId=9900015502#latestAnnouncement" : "荣盛石化" # unable to crawl article_urls
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "private_enterprise"
    
    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    custom_settings = { 
		"DOWNLOADER_MIDDLEWARES" : {
			'scraper.middlewares.HeadlessBrowserProxy': 350,
		},
		"DOWNLOAD_DELAY" : 2,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter'
	}
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="w-list xn-resize"]//a/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:  
        return response.xpath("//h1[@class='w-title']//text()").get()
    
    def get_body(self,response) -> str:
        return body_normalization(response.xpath("//div[@class='w-detail']//p//text()").getall())
    
    def date_format(self) -> str:
        return '%Y-%m-%d'
    
    def get_date(self, response) -> str:
        return response.xpath("//div[@class='w-createtime']/span[2]//text()").get()
    
    def get_images(self, response) -> list:
        return []
    
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        current_page_number = response.xpath('//*[@id="autopage"]//span/text()').get()
        if current_page_number:
            current_page_index = int(current_page_number)
            next_page_xpath = f'//*[@id="autopage"]/center/a[{current_page_index}]/@href'
            next_page = response.xpath(next_page_xpath).get()
            if next_page:
                return next_page
        return None