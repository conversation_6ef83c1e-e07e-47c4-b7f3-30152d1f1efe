from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime 

class ParliamentOfEastJava(OCSpider):
    name = "ParliamentOfEastJava"
    
    country = "Indonesia"
    
    start_urls_names = {
        "https://dprd.jatimprov.go.id/berita": "News",
    }

    

    charset = "utf-8"

   

    @property
    def language(self): 
        return "Indonesian"
     
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="news3col__col"]/a[@class="news3col__img"]/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="detailhead"]/h1[@class="detail-judul"]/text()').get()
        
      
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="detailcontent"]/p/text()').getall())

    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return"%m-%d-%Y"
    
    def get_date(self, response) -> Optional[str]:
        return response.xpath('//div[@class="repshare__col"]/span[@class="repshare__date"]/text()').re_first(r'\d{1,2} \w+ \d{4}')

       
    def get_authors(self, response):
        return  response.xpath('//div[@class="repshare__col"]/span[@class="repshare__nama"]/text()').get()
    
    def get_page_flag(self) -> bool:
        return True
    
    def get_next_page(self, response) -> Optional[str]:
        next_page =  response.xpath('//div[@class="paging"]//li[@class="active"]/following-sibling::li[a]/a[1]/@href').get()
        if next_page:
            return next_page
        return None