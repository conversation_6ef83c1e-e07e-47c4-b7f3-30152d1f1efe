import json
import re
from scraper.OCSpider import OCSpider
from scraper.middlewares import HeadlessBrowserProxy
import scrapy
from typing import List
from dotenv import load_dotenv
from scraper.utils.helper import body_normalization
from typing import Optional
import logging
load_dotenv()

class ChinaChamberofTourism(OCSpider):
    name = "ChinaChamberofTourism"

    start_urls_names = {
        "https://www.tcc.org.cn/shyw" :"商会活动-全联旅游业商会",                          
        "https://www.tcc.org.cn/hydt" :"行业动态",
        "https://www.tcc.org.cn/cytz" : "重要通知-全联旅游业商会",
        "https://www.tcc.org.cn/djgz":"商会党建活动"
    }

    api_start_urls = {
        "https://www.tcc.org.cn/shyw": {
            "url": "https://www.tcc.org.cn/Designer/Common/GetData",
            "payload": {
                "dataType": "news",
                "pageIndex": "0",
                "pageSize": "6",
                "selectCategory": "150265,165617,165848,150264,165661",
                "selectId": "",
                "dateFormater": "yyyy-MM-dd",
                "orderByField": "createtime",
                "orderByType": "desc",
                "templateId": "0",
                "postData": "",
                "es": "false",
                "setTop": "true",
                "__RequestVerificationToken": "1XwjqV_eCIm5-Wki-PdiwwANvB-ej3HOPrRoPpxYWdGzBjvJI5W7wKFUTQCQ473AX-1Znsy2zBhM20VoUu5OU1_-gkE2r1C7oylUDv39p0k1"
            },
        },
        "https://www.tcc.org.cn/hydt": {
            "url": "https://www.tcc.org.cn/Designer/Common/GetData",
            "payload": {
                "dataType": "news",
                "pageIndex": "0",
                "pageSize": "6",
                "selectCategory": "150265,165849",
                "selectId": "",
                "dateFormater": "yyyy-MM-dd",
                "orderByField": "createtime",
                "orderByType": "desc",
                "templateId": "0",
                "postData": "",
                "es": "false",
                "setTop": "true",
                "__RequestVerificationToken": "nylH5KIRkh3PN9p9-06vitXITalqTo429TDbLc569rhP0rqJBUOjs205ktPq8R5qvD_8hPQDQWh9HnzsCtgUJB4NW2f0OMUF2983DzxtxQw1"
            },
        },
        "https://www.tcc.org.cn/cytz": {
            "url": "https://www.tcc.org.cn/Designer/Common/GetData",
            "payload": {
                "dataType": "news",
                "pageIndex": "0",
                "pageSize": "6",
                "selectCategory": "167991",
                "selectId": "",
                "dateFormater": "yyyy-MM-dd",
                "orderByField": "createtime",
                "orderByType": "desc",
                "templateId": "0",
                "postData": "",
                "es": "false",
                "setTop": "true",
                "__RequestVerificationToken": "-cSv_r7lW5ZGwGSVguCQmhlwjlEtzyVOcvsEN8dpDpknZNEyCktCOHZ48fenLArZyO6hMh7ZxAeiEBcwmdMnr4devNxZ12MBZLGbzVFv0rs1"
            },
        },
        "https://www.tcc.org.cn/djgz": {
            "url": "https://www.tcc.org.cn/Designer/Common/GetData",
            "payload": {
                "dataType": "news",
                "pageIndex": "0",
                "pageSize": "5",
                "selectCategory": "165603",
                "selectId": "",
                "dateFormater": "yyyy-MM-dd",
                "orderByField": "createtime",
                "orderByType": "desc",
                "templateId": "0",
                "postData": "",
                "es": "false",
                "setTop": "true",
                "__RequestVerificationToken": "WNHYqqTk8zrjiz0rh5ssBtU4S63M8iHJe_FJF7hdIb9HdgPoE6Y_9-0N8BJkHVOXZFHsj3IiPgCepjqAAc1PLDLaKH3S8_BWW-QX_3i_vyA1"
            },
        },
    }
    
    custom_settings = {
        "DOWNLOAD_DELAY": 4,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_data=self.api_start_urls.get(start_url)
        if not api_data:
            self.logger.error(f"No API configuration found for start_url: {start_url}")
            return None
        api_url = api_data["url"]
        payload = api_data["payload"]
        payload["pageIndex"] = payload.get("pageIndex")
        yield scrapy.FormRequest(
            url = api_url,
            method = "POST",
            headers={
                "Content-Type": "application/x-www-form-urlencoded;",
            },
            dont_filter=True,
            formdata = payload,
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_data["url"],
                "payload": payload,
                "current_page": payload["pageIndex"]
            },
        )
    
    @property
    def source_type(self) -> str:
        return 'IndustryAssociation'

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_articles(self, response) -> list:
        data = json.loads(response.text)
        article_urls=[]
        hbp = HeadlessBrowserProxy()
        for item in data.get("Data", []):
            LinkUrl = item.get('LinkUrl')   
            url=hbp.get_proxy(f"https://www.tcc.org.cn{LinkUrl}", timeout=30000)
            article_urls.append(url)
        return article_urls
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="w-detail"]//p/text()').getall())
        
    def get_href(self, entry) -> str:
        return entry

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_title(self, response) -> str:
        return response.xpath("//h1[@class='w-title']/text()").get()

    def get_images(self, response, entry=None) -> List[str]:
        return response.xpath('//div[@class="w-detail"]//img//@src').getall()
    
    def get_authors(self, response, entry=None) -> list[str]:
        return []

    def get_date(self, response) -> str:
        date_text = response.xpath('//span[@class="w-createtime-item w-createtime-date"]//text()').get()
        date_match = re.search(r'(\d{4})年(\d{1,2})月(\d{1,2})日', date_text)
        if date_match:
            year, month, day = date_match.groups()
            formatted_date = f"{year}-{int(month):02d}-{int(day):02d}"
            return formatted_date
        return None

    def get_next_page(self, response,current_page ):
        max_pages = response.json().get('TotalPages', 1)
        current_page = int(response.meta.get('current_page'))+1
        if current_page < max_pages:
            print("THE CURRENT PAGE IS :", current_page)
            return str(current_page) 
        else: 
            return None
    
    def get_page_flag(self) -> bool:
        return False

    def go_to_next_page(self, response, start_url, current_page: Optional[str] = 1):
        api_url = response.meta.get("api_url")
        if not api_url:
            logging.error("API URL not found in meta data.")
            return None
        next_page = self.get_next_page(response, current_page)
        if next_page:
            payload = response.meta.get('payload')
            payload['pageIndex'] = next_page
            yield scrapy.FormRequest(
            url = api_url,
            method = "POST",
            headers={
                "Content-Type": "application/x-www-form-urlencoded;",
            },
            dont_filter=True,
            formdata = payload,
            callback=self.parse_intermediate,
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "payload": payload,
                "current_page": payload["pageIndex"]
            },
        )
        else:
            self.logger.info(f"No more pages to crawl for start_url: {start_url}")