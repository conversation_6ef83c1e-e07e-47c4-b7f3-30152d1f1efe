import json
import scrapy
from scraper.OCSpider import OCSpider
from dotenv import load_dotenv
from scraper.utils.helper import body_normalization
from typing import Optional
import logging
load_dotenv()
from scraper.middlewares import HeadlessBrowserProxy


class ChinaAssociationOfPerformingArts(OCSpider):
    name = "ChinaAssociationOfPerformingArts"

    custom_settings = {
        "DOWNLOAD_DELAY": 2,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    start_urls_names = {
        'https://www.capa.com.cn/#/index/DynamicList': 'DynamicList',
        'https://www.capa.com.cn/#/index/PartyBuildingList?activeName=%E5%85%9A%E5%BB%BA%E6%B4%BB%E5%8A%A8&id=1592826663357104130&contentType=%E7%AE%80%E5%8D%95%E5%88%97%E8%A1%A8': 'PartyBuildingList',
        'https://www.capa.com.cn/#/index/ResearchList?activeName=%E8%A1%8C%E4%B8%9A%E5%B9%B4%E6%8A%A5&id=1552554168751767554&contentType=%E7%AE%80%E5%8D%95%E5%88%97%E8%A1%A8': 'ResearchList'
    }

    charset = "utf-8"

    # To match the encoded article url from the proxy with the domain "www.capa.com.cn"
    include_rules = [r'.*https%3A%2F%2Fwww\.capa\.com\.cn%2F.*']

    # Start urls mapping with corresponding APIs and their payloads
    api_start_urls = {
        'https://www.capa.com.cn/#/index/DynamicList': {
            "url": "https://capa.com.cn/api/client/news/list",
            "payload": {
                "dictId": "1552487425756987393",
                "name": "",
                "page": 1,
                "pageSize": 7,
                "status": "2",
            },
        },
        'https://www.capa.com.cn/#/index/PartyBuildingList?activeName=%E5%85%9A%E5%BB%BA%E6%B4%BB%E5%8A%A8&id=1592826663357104130&contentType=%E7%AE%80%E5%8D%95%E5%88%97%E8%A1%A8': {
            "url": "https://capa.com.cn/api/client/news/list",
            "payload": {
                "dictId": "1592826663357104130",
                "name": "",
                "page": 1,
                "pageSize": 18,
                "status": "2",
            },
        },
        'https://www.capa.com.cn/#/index/ResearchList?activeName=%E8%A1%8C%E4%B8%9A%E5%B9%B4%E6%8A%A5&id=1552554168751767554&contentType=%E7%AE%80%E5%8D%95%E5%88%97%E8%A1%A8': {
            "url": "https://capa.com.cn/api/client/news/list",
            "payload": {
                "dictId": "1552554168751767554",
                "name": "",
                "page": 1,
                "pageSize": 18,
                "status": "2",
            },
        },
    }

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            self.logger.error(f"No API configuration found for start_url: {start_url}")
            return
        api_url = api_data["url"]
        payload = api_data["payload"]
        payload["page"] = payload.get("page")
        yield scrapy.Request(
            url = api_url,
            method = "POST",
            headers = {
                "Content-Type": "application/json;charset=UTF-8"
            },
            body = json.dumps(payload),
            callback = self.parse,    # callback parse
            meta={
                "start_url": start_url,
                "api_url": api_data["url"],
                "payload": payload,
                "current_page": payload["page"]
            },
        )
    
    @property
    def source_type(self) -> str:
        return "IndustryAssociation"
        
    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        try:
            data = json.loads(response.text)
            articles = data.get("data", {}).get("records", [])
            article_urls = [
                self.construct_article_url(article)
                for article in articles
                if article
            ]
            return self.get_proxy_articles(article_urls)
        except json.JSONDecodeError as e:
            self.logger.error(f"Error decoding JSON: {e}")
            return []
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response):
        return response.xpath('//div[@id="app"]//div[@class="del-main"]//text()').get()
    
    def get_body(self, response):
        return body_normalization(response.xpath('//div[@id="app"]//div[@class="cont"]//p//text()').getall()) 
    
    def get_images(self, response):
        return response.xpath('//div[@id="app"]//div[@class="cont"]/p//img/@src').getall()
    
    def date_format(self) -> str:
        return '%Y-%m-%d'
    
    def get_date(self, response):
        return response.xpath('//div[@id="app"]//div[@class="msg"]//span//text()').re_first(r"\d{4}-\d{2}-\d{2}")
    
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response, current_page):
        max_pages = response.json().get('data', {}).get('pages', 1)
        next_page = current_page + 1 if current_page < max_pages else None
        return next_page
    
    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        api_url = response.meta.get("api_url")
        if not api_url:
            logging.error("API URL not found in meta data.")
            return
        next_page = self.get_next_page(response, current_page)
        if next_page:
            payload = response.meta.get('payload')
            payload['page'] = next_page
            # API request for the next page
            yield scrapy.Request(
                url=api_url,
                method='POST',
                body=json.dumps(payload),
                headers={
                    "Content-Type": "application/json;charset=UTF-8",
                },
                callback=self.parse_intermediate,  # callback parse_intermediate
                meta={'current_page': next_page, 'start_url': start_url}
            )
        else:
            logging.info("No more pages to fetch.")
            yield None
    
    def construct_article_url(self, article):
        # Construct article URLs using the available fields of API response,
        # It may directly exists in either "url" field or "fileUrl" field
        # If "url", "fileUrl" are empty, Articl URL is constructed using "id" and "dictName"
        url = article.get('url')
        file_url = article.get('fileUrl')
        article_id = article.get('id')
        dict_name = article.get('dictName')
        if url:
            return url    # Eg: "article url": "https://mp.weixin.qq.com/s/pyiQdga6OrNQIsmRb9rnfw", 
        elif file_url:
            return file_url    # Eg: "article url": "http://perform.capa.com.cn/1720150249121_国有文艺院团线上演艺发展报告.pdf",
        elif article_id and dict_name:
            return f"https://www.capa.com.cn/#/index/NewsDetail?activeName={dict_name}&id={article_id}"    # Eg: "article url": "https://www.capa.com.cn/#/index/NewsDetail?activeName=%E8%A1%8C%E4%B8%9A%E5%B9%B4%E6%8A%A5&id=1859123183003656193"
        return None
    
    def get_proxy_articles(self, articles):
        try:
            hbp = HeadlessBrowserProxy()
            # Article URLs to include the proxy
            proxy_urls = [hbp.get_proxy(url, timeout = 50000) for url in articles]
            return proxy_urls
        except Exception as e:
            self.logger.error(f"Failed to fetch proxy articles: {e}")
            return []