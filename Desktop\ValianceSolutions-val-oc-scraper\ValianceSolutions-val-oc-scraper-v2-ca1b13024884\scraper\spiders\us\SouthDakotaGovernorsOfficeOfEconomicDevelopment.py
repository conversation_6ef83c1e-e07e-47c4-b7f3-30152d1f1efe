from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class SouthDakotaGovernorsOfficeOfEconomicDevelopment(OCSpider):
    name = "SouthDakotaGovernorsOfficeOfEconomicDevelopment"

    country = "US"
    
    charset="utf-8"

    start_urls_names = {
        "https://sdgoed.com/media-center/press-releases/" : "Press Releases"
    }

    @property
    def language(self) -> str:
        return "English"
    
    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self) -> str:
        return "US/Eastern"
    
    def get_articles(self, response) :
        return response.xpath('//div[@class="title"]/a/@href').getall()
    
    def get_href(self, entry: str) -> str:
        return entry

    def get_title(self, response) :
        return response.xpath('//div[@class="overview-content"]/h1/text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="overview-content"]//p//text()').getall())

    def get_images(self, response) :
        return []

    def date_format(self) -> str:
        return "%B %d, %Y"

    def get_date(self, response) -> str:
        return response.xpath('//div[@class="date"]//text()').get().strip()
    
    def get_authors(self, response) :
        return []
    
    def get_document_urls(self, response, entry=None):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return response.xpath('//a[@class="next page-numbers page-link"]/@href').get()

    
    
    