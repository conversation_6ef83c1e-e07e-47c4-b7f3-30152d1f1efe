from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ParliamentOfIslandsTidoreCity(OCSpider):
    name = "ParliamentOfIslandsTidoreCity"
    
    country = "ID"

    start_urls_names = {
        "https://dprdkotatidorekepulauan.com/category/news/" : "Resources", #Found only 3 articles
    }
    
    charset = "utf-8"

    article_data_map ={}
    
    @property
    def language(self): 
        return "Indonesian"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Jayapura"
    
    def get_articles(self, response) -> list:
        articles = []
        for article in response.xpath("//div[@class='fl-post-feed']"):
            url = article.xpath(".//div[@class='fl-post-text col-md-8']//a/@href").get()
            title = article.xpath(".//div[@class='fl-post-text col-md-8']//a//text()").get()
            date = article.xpath("//div[@class='fl-post-meta text-muted']//text()").get()
            if url and title and date:
                full_url = url
                title = title.strip()
                clean_date = date.strip()
                self.article_data_map[full_url] = {"title": title, "date": clean_date}
                articles.append(full_url) 
        return articles

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@id='wrapper-content']//p//text()").getall()) 
        
    def get_images(self, response) -> list:
        return response.xpath("//div[@id='wrapper-content']//img//@src").getall()   
     
    def date_format(self) -> str:
        return "%B %d, %Y"
    
    def get_date(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
     
    def get_next_page(self, response): 
        return None