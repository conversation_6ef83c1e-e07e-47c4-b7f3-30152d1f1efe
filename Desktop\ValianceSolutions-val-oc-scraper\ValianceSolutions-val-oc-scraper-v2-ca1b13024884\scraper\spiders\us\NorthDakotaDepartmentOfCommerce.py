from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import re

class NorthDakotaDepartmentOfCommerce(OCSpider):
    name = "NorthDakotaDepartmentOfCommerce"

    country = "US"
    
    charset="utf-8"

    start_urls_names = {
        "https://www.commerce.nd.gov/news" : "Press"
    }

    @property
    def language(self) -> str:
        return "English"
    
    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self) -> str:
        return "US/Central"
    
    def get_articles(self, response) :
        return response.xpath('//div[@class="media-heading"]/h2/a/@href').getall()
    
    def get_href(self, entry: str) -> str:
        return entry

    def get_title(self, response) :
        return response.xpath('//div[@class="page-header"]//span/text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="content-body"]//p/text()').getall())

    def get_images(self, response) :
        return []

    def date_format(self) :
        return "%A, %B %d, %Y" 

    def get_date(self, response) -> str:
        #contains 2 different pattern for date eg Thursday, March 20, 2025 - 03:10 pm and Wednesday January 14, 2025 -09:18am
        date = response.xpath('//span[@class="news-date"]//text()').get().strip()
        date_part = date.split(' - ')[0]
        return date_part
    
    def get_authors(self, response) :
        return []
    
    def get_document_urls(self, response, entry=None):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return response.xpath('//a[@title="Go to next page"]/@href').get()

    
    
    