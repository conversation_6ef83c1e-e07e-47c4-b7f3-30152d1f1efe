import json
import re
from scraper.OCSpider import <PERSON><PERSON><PERSON><PERSON>
from typing import List
from dotenv import load_dotenv
from scraper.utils.helper import body_normalization
load_dotenv()

class ChinaCivilAirportsAssociation2(OCSpider):
    name = "ChinaCivilAirportsAssociation2"

    start_urls_names = {
        "https://www.chinaairports.org.cn/spl/4ZTIOa-252B22.html":"支部建设",
    }
 
    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350,
        },
        "DOWNLOAD_DELAY": 10, 
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }
    
    HEADLESS_BROWSER_WAIT_TIME = 20000

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return 'IndustryAssociation'

    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        try:
            article_urls = response.xpath("//div[@class='content3']//a[not(ancestor::div[@class='fy'])]/@href").getall()
            return article_urls
        except json.JSONDecodeError as e:
            self.logger.error(f"Error decoding JSON: {e}")
            return []
        
    def get_href(self, entry) -> str:
        return entry   

    def get_title(self, response) -> str:
        return response.xpath("//div[@class='wz_div_title wz_div_title_h2']//h2//text()").get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[contains(@class, 'wz_div_show')]//p//text()").getall())

    def get_images(self, response, entry=None) -> List[str]:
        return [response.urljoin(i) for i in response.xpath("//div[contains(@class, 'wz_div_show')]//img/@src").getall()]

    def get_authors(self, response, entry=None) -> list[str]:
        return [] 
    
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date = response.xpath("//div[@class='wz_div_time']//p//text()").get()
        return re.search(r"\d{4}-\d{2}-\d{2}", date).group()
    
    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        return None