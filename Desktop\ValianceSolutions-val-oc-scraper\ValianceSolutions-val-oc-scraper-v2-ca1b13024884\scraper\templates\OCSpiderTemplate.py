from typing import List, Optional
from urllib.parse import urljoin
from scraper.OCSpider import <PERSON><PERSON><PERSON><PERSON>
from scraper.utils.helper import body_normalization
import re
from datetime import datetime

class OCSpiderTemplate(OCSpider):
    """
    Template for creating a new OCSpider.
    
    This template includes all the required methods that need to be implemented
    when creating a new OCSpider. Customize the XPath selectors and other logic
    based on the specific website structure.
    
    Required methods:
    - source_type
    - timezone
    - get_articles
    - get_href
    - get_title
    - get_body
    - get_images
    - date_format
    - get_date
    - get_authors
    - get_document_urls
    - get_next_page
    - get_page_flag
    """
    
    # Spider name (should be unique)
    name = "OCSpiderTemplate"
    
    # Country code
    country = "US"  # Change as needed
    
    # Start URLs with their names
    start_urls_names = {
        "https://example.com/news": "News",
        # Add more URLs as needed
    }
    
    # Character set for the website
    charset = "utf-8"
    
    # If geo-targeted proxy is needed
    # proxy_country = "us"  # Uncomment and set as needed
    
    # Custom settings for the spider
    # custom_settings = {
    #     "DOWNLOADER_MIDDLEWARES": {
    #         'scraper.middlewares.GeoProxyMiddleware': 350,
    #     },
    #     "DOWNLOAD_DELAY": 2,
    # }
    
    # If headless browser is needed for JavaScript rendering
    # HEADLESS_BROWSER_WAIT_TIME = 5000  # 5 seconds
    # custom_settings = {
    #     "DOWNLOADER_MIDDLEWARES": {
    #         'scraper.middlewares.HeadlessBrowserProxy': 350,
    #     },
    #     "DOWNLOAD_DELAY": 3,
    # }
    
    @property
    def language(self):
        return "English"  # Change as needed
    
    @property
    def source_type(self) -> str:
        """
        Define the type of source.
        Examples: "ministry", "newspaper", "IndustryAssociation", etc.
        """
        return "ministry"  # Change as needed
    
    @property
    def timezone(self) -> str:
        """
        Define the timezone of the source.
        Examples: "US/Eastern", "Asia/Shanghai", "Europe/London", etc.
        """
        return "US/Eastern"  # Change as needed
    
    def get_page_flag(self) -> bool:
        """
        Return True if pagination is available, False otherwise.
        """
        return True  # Change as needed
    
    def get_articles(self, response) -> list:
        """
        Extract article URLs from the page.
        Adjust the XPath selector based on the actual website structure.
        """
        articles = response.xpath('//div[@class="article-list"]//a/@href').getall()
        return [response.urljoin(link) for link in articles]
    
    def get_href(self, entry) -> str:
        """
        Return the article URL.
        """
        return entry
    
    def get_title(self, response, entry=None) -> str:
        """
        Extract the article title.
        Adjust the XPath selector based on the actual website structure.
        """
        return response.xpath('//h1[@class="article-title"]/text()').get().strip()
    
    def get_body(self, response, entry=None) -> str:
        """
        Extract the article body.
        Adjust the XPath selector based on the actual website structure.
        """
        body_parts = response.xpath('//div[@class="article-content"]//text()').getall()
        return body_normalization(body_parts)
    
    def date_format(self) -> str:
        """
        Define the date format used on the website.
        Examples: "%Y-%m-%d", "%d/%m/%Y", "%B %d, %Y", etc.
        """
        return "%Y-%m-%d"  # Change as needed
    
    def get_date(self, response, entry=None) -> int:
        """
        Extract the article date and convert to timestamp.
        Adjust the XPath selector based on the actual website structure.
        """
        date_str = response.xpath('//div[@class="article-date"]/text()').get()
        if date_str:
            date_str = date_str.strip()
            # Extract date using regex if needed
            date_match = re.search(r'(\d{4}-\d{2}-\d{2})', date_str)
            if date_match:
                date_str = date_match.group(1)
                date_obj = datetime.strptime(date_str, self.date_format())
                return int(date_obj.timestamp())
        # Return current time if date not found
        return int(datetime.now().timestamp())
    
    def get_authors(self, response, entry=None) -> List[str]:
        """
        Extract the article authors.
        Adjust the XPath selector based on the actual website structure.
        """
        author = response.xpath('//div[@class="article-author"]/text()').get()
        if author:
            author = author.strip()
            return [author] if author else []
        return []
    
    def get_images(self, response, entry=None) -> List[str]:
        """
        Extract image URLs from the article.
        Adjust the XPath selector based on the actual website structure.
        """
        images = response.xpath('//div[@class="article-content"]//img/@src').getall()
        return [response.urljoin(img) for img in images]
    
    def get_next_page(self, response) -> List[str]:
        """
        Extract the next page URL for pagination.
        Adjust the XPath selector based on the actual website structure.
        """
        next_page = response.xpath('//a[@class="next-page"]/@href').get()
        if next_page:
            return [response.urljoin(next_page)]
        return []
    
    def get_document_urls(self, response, entry=None) -> list:
        """
        Extract document URLs (PDFs, etc.) from the article.
        Adjust the XPath selector based on the actual website structure.
        """
        docs = response.xpath('//div[@class="article-content"]//a[contains(@href, ".pdf") or contains(@href, ".doc") or contains(@href, ".docx")]/@href').getall()
        return [response.urljoin(doc) for doc in docs]
    
    def get_meta(self, response) -> list:
        """
        Extract any additional metadata.
        """
        return []
    
    def get_pdf(self, response, entry=None):
        """
        Extract PDF content if needed.
        """
        return None
    
    def get_subhead(self, response) -> str:
        """
        Extract article subheading if available.
        """
        subhead = response.xpath('//div[@class="article-subhead"]/text()').get()
        return subhead.strip() if subhead else ""
    
    # Optional: Define parse_intermediate for headless mode
    # def parse_intermediate(self, response):
    #     """
    #     This method is used when we need to use a headless browser to render JavaScript
    #     before parsing the content.
    #     """
    #     articles = self.get_articles(response)
    #     for article_url in articles:
    #         request = response.follow(article_url, callback=self.parse_article)
    #         request.meta['start_url'] = response.meta['start_url']
    #         yield request
    #     
    #     # Check for next page
    #     next_pages = self.get_next_page(response)
    #     for next_page in next_pages:
    #         request = response.follow(next_page, callback=self.parse_intermediate)
    #         request.meta['start_url'] = response.meta['start_url']
    #         yield request
    
    # Optional: Define go_to_next_page for headless mode
    # def go_to_next_page(self, response):
    #     """
    #     This method is used in conjunction with parse_intermediate
    #     to navigate to the next page in headless mode.
    #     """
    #     next_pages = self.get_next_page(response)
    #     for next_page in next_pages:
    #         request = response.follow(next_page, callback=self.parse_intermediate)
    #         request.meta['start_url'] = response.meta['start_url']
    #         yield request
