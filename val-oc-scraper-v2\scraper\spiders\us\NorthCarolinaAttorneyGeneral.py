from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class NorthCarolinaAttorneyGeneral(OCSpider):
    name = "NorthCarolinaAttorneyGeneral"
    
    country = "US"

    proxy_country = "us"

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES":
            {
                # for using geo targeted proxy, add this middleware
                'scraper.middlewares.GeoProxyMiddleware': 350,
            },
    }

    start_urls_names = {
        'https://ncdoj.gov/press-releases/' : "Press Releases"
    }

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Eastern"

    article_to_date_mapping = {}

    def get_articles(self, response) -> list:
        mapping = {}
        article_urls = []
        entries = response.xpath('//tbody//tr')
        for items in entries :
            url = items.xpath('.//a/@href').get()
            article_urls.append(url)
            date = items.xpath('.//td//text()').get()
            if url and date:
                mapping[url] = date
        self.article_to_date_mapping.update(mapping)
        return article_urls
   
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//div[@class="title"]/h1/text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="content"]//p/text()').extract())
    
    def get_images(self, response) -> list:
        return response.xpath('//div[@class="content"]/img//@src').getall()

    def date_format(self) -> str:
        return '%m/%d/%y'

    def get_date(self, response) -> str:
        article_url = response.url
        article_date = self.article_to_date_mapping.get(article_url, None)
        if article_date:
            return article_date

    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
     
    def get_next_page(self, response) -> str:
        return response.xpath('//div[@class="nav-previous w3_bg"]//a/@href').get()