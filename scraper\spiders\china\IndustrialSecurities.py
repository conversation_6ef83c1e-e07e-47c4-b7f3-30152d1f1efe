from typing import List, Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import re

class IndustrialSecurities(OCSpider):
    name = "IndustrialSecurities"

    start_urls_names = {
        'https://www.xyzq.com.cn/xysec/aboutus/11097':"兴业证券",  # no article
        'https://www.xyzq.com.cn/xysec/aboutus/11098':"兴业证券",  # no article
        'https://www.xyzq.com.cn/xysec/aboutus/11105':"兴业证券", #no dates and its pdf
        'https://www.xyzq.com.cn/xysec/aboutus/11106':"兴业证券",#no dates and its pdf
        'https://www.xyzq.com.cn/xysec/aboutus/11100':"兴业证券",  # no article
        'https://www.xyzq.com.cn/xysec/aboutus/11080?word=&curr=1':"兴业证券"   # normal
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "SOE"
    
    @property
    def timezone(self) -> str:
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        if response.url.endswith("11105") or response.url.endswith("11106"):
            return
        else:
            article_hrefs = response.xpath('//div[@class="dync_state"]//a//@href').getall()
            articles = []
            for href in article_hrefs:
                match = re.search(r"info/article/', '([^']+)'", href)
                if match:
                    article_id = match.group(1)
                    url = f"https://www.xyzq.com.cn/xysec/info/article/{article_id}"
                    articles.append(url)
                else:
                    return
            return articles
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response, entry=None) -> str:
        return response.xpath('//h3//text()').get().strip()
    
    def get_body(self, response, entry=None) -> str:
        return body_normalization(response.xpath('//div[@class="newshxgd"]//p//text()').getall())
    
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date = response.xpath('//span[@class="mydate"]//text()').get()
        date = date.replace("时间:","")
        return date.strip()
    
    def get_authors(self, response, entry=None) -> List[str]:
        return []
    
    def get_document_urls(self, response, entry=None) -> list:
        return []
    
    def get_images(self, response, entry=None) -> List[str]:
        return response.xpath('//div[@class="newshxgd"]//img//@src').getall()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        if response.status == 404:
            return None
        current_url = response.url
        if "curr=" in current_url:
            match = re.search(r"curr=(\d+)", current_url)
            if match:
                current_page_num = int(match.group(1))
                next_page_num = current_page_num + 1
                next_page_url = current_url.replace(
                    f"curr={current_page_num}",
                    f"curr={next_page_num}"
                )
                return next_page_url
        return None