import scrapy
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import json
import logging
from typing import List, Union

class ChinaAssociationPromotionHealthScienceTechnology(OCSpider):
    name = 'ChinaAssociationPromotionHealthScienceTechnology'
    
    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350,
        },
        "DOWNLOAD_DELAY": 4,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }
    
    HEADLESS_BROWSER_WAIT_TIME = 50000   # On setting the wait time below 50secs not able to scrape the articles 
    
    start_urls_names = {
        'https://www.chstpa.com.cn/association/managementSystemList/?id=21&title=%E5%85%AC%E7%A4%BA%E6%96%B0%E9%97%BB':'公示新闻',
        'https://www.chstpa.com.cn/partyConstruction/list?id=23&title=%E5%85%9A%E5%BB%BA%E5%8A%A8%E6%80%81':'党建动态',
        'https://www.chstpa.com.cn/organization/list?id=28&title=%E9%80%9A%E7%9F%A5%E5%85%AC%E5%91%8A':'通知公告' ,
        'https://www.chstpa.com.cn/organization/list?id=29&title=%E6%96%B0%E9%97%BB%E5%8A%A8%E6%80%81':'新闻动态',
        'https://www.chstpa.com.cn/educating/list?id=45&title=%E5%B7%A5%E4%BD%9C%E5%8A%A8%E6%80%81':'工作动态'
    }
    
    api_start_url = {
        'https://www.chstpa.com.cn/association/managementSystemList/?id=21&title=%E5%85%AC%E7%A4%BA%E6%96%B0%E9%97%BB':'https://www.chstpa.cn/chstpa/article/getArticleLists?navigateId=21&pageNo={pageNo}&pageNum=10',
        'https://www.chstpa.com.cn/partyConstruction/list?id=23&title=%E5%85%9A%E5%BB%BA%E5%8A%A8%E6%80%81':'https://www.chstpa.cn/chstpa/article/getArticleLists?navigateId=23&pageNo={pageNo}&pageNum=10',
        'https://www.chstpa.com.cn/organization/list?id=28&title=%E9%80%9A%E7%9F%A5%E5%85%AC%E5%91%8A': 'https://www.chstpa.cn/chstpa/article/getArticleLists?navigateId=28&pageNo={pageNo}&pageNum=10',
        'https://www.chstpa.com.cn/organization/list?id=29&title=%E6%96%B0%E9%97%BB%E5%8A%A8%E6%80%81': 'https://www.chstpa.cn/chstpa/article/getArticleLists?navigateId=29&pageNo={pageNo}&pageNum=10',
        'https://www.chstpa.com.cn/educating/list?id=45&title=%E5%B7%A5%E4%BD%9C%E5%8A%A8%E6%80%81': 'https://www.chstpa.cn/chstpa/article/getArticleLists?navigateId=45&pageNo={pageNo}&pageNum=10'
    }
    
    def parse_intermediate(self, response):
        start_url = response.meta.get('start_url')
        api_base_url = self.api_start_url[start_url]
        current_page = response.meta.get("current_page", 1)
        api_url = api_base_url.format(pageNo = current_page)
        if not api_base_url:
            self.logger.error(f"No API configuration found for start_url: {start_url}")
        else:
            self.logger.info(f"Fetching API URL: {api_url}")
        yield scrapy.Request(
            url = api_url,
            callback = self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_base_url,
                "current_page": current_page,
            },
        )
    
    @property
    def source_type(self) -> str:
        return "IndustryAssociation"
    
    @property
    def timezone(self): 
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        try:
            data = response.json()
            article_list = data.get("data", {}).get("data", [])
            articles = [
                f"https://www.chstpa.com.cn/association/managementSystemList/detail/?id={article['id']}"
                for article in article_list if "id" in article
            ]
            return articles
        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to decode JSON: {e}")
            return []

    def get_href(self, entry) -> str:
        return entry

    def get_title(self,response) -> str:
        return response.xpath("//div[@class='title3']//text()").get()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//p//span//text()").getall())
    
    def get_images(self, response) -> List[str]:
        return response.xpath("//p//img//@src").extract()
        
    def date_format(self) -> str:
        return '%Y-%m-%d'

    def get_date(self, response) -> str:
        return response.xpath("//div[@class='t1']//span//text()").re_first(r"\d{4}-\d{2}-\d{2}")
        
    def get_authors(self, response):
        return response.xpath("//div[@class='t1']//span//text()").get()
    
    def get_next_page(self, response, current_page) -> Union[None, str]:
        data = response.json()
        article_list = data.get("data", {}).get("data", [])
        if not article_list:
            return None
        else:
            return current_page + 1
        
    def get_page_flag(self) -> bool:
        return False

    def go_to_next_page(self, response, start_url, current_page = 1):
        api_url = response.meta.get("api_url")
        if not api_url: 
            logging.info("API URL not found in meta data.")
            return
        next_page = self.get_next_page(response, current_page)
        if next_page:
            yield scrapy.Request(
                url=api_url,
                headers={
                    "Content-Type": "application/json;charset=UTF-8",
                },
                callback=self.parse_intermediate,
                meta={'current_page': next_page, 'start_url': start_url}
            )
        else:
            logging.info("No more pages to fetch.")
            yield None