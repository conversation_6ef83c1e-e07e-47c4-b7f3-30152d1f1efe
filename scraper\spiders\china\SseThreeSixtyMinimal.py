from scraper.OCSpider import <PERSON><PERSON>pider
from scraper.utils.helper import body_normalization
from urllib.parse import urljoin
import re
from datetime import datetime
import pytz

class SseThreeSixtyMinimal(OCSpider):
    name = "SseThreeSixtyMinimal"
    country = "CN"
    start_urls_names = {
        "http://www.sse.com.cn/assortment/stock/list/info/announcement/index.shtml?productId=601360": "SSE Announcements",
        "https://www.360.cn/news.html": "360 News",
    }
    charset = "utf-8"
    handle_httpstatus_list = [200, 301, 302, 404, 500]
    custom_settings = {
        "DOWNLOAD_DELAY": 0.5, "CONCURRENT_REQUESTS_PER_DOMAIN": 4,
        "DOWNLOAD_FAIL_ON_DATALOSS": False, "DOWNLOAD_TIMEOUT": 60,
        "RETRY_TIMES": 3, "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
        "COOKIES_ENABLED": False, "REDIRECT_ENABLED": True,
    }

    @property
    def source_type(self) -> str: return "Corporate"
    @property
    def language(self) -> str: return "Chinese"
    @property
    def timezone(self): return "Asia/Shanghai"

    def get_articles(self, response) -> list:
        if "sse.com.cn" in response.url:
            links = response.xpath('//table[@class="table search_result_table"]//td/a/@href | //div[@class="content-list"]//a/@href | //a[contains(@href, ".pdf")]/@href').getall()
        elif "360.cn" in response.url:
            links = response.xpath('//div[contains(@class, "news-list")]//a/@href | //a[contains(@href, "/n/")]/@href | //div[contains(@class, "news_list")]//a/@href | //div[contains(@class, "news-item")]//a/@href').getall()
        else:
            links = response.xpath('//a[contains(@href, "detail") or contains(@href, "/n/")]/@href').getall()

        filtered_links = [link for link in links or [] if link and not any(nav in link for nav in ['javascript:', '#', 'mailto:', 'tel:'])]
        unique_links = []
        for link in filtered_links:
            if link not in unique_links:
                unique_links.append(link)
        return [response.urljoin(link) for link in unique_links]

    def get_href(self, entry) -> str: return entry

    def get_title(self, response, entry=None) -> str:
        title_xpaths = ['//div[@class="content-title"]/text()', '//h1/text()',
                        '//div[@class="title"]/text()', '//div[contains(@class, "title")]/text()',
                        '//title/text()']

        for xpath in title_xpaths:
            title = response.xpath(xpath).get()
            if title and title.strip():
                return title.strip()

        if response.url.lower().endswith('.pdf'):
            return response.url.split('/')[-1]
        return "No title available"

    def get_body(self, response, entry=None) -> str:
        if response.url.lower().endswith('.pdf'):
            return "PDF document - please refer to the document URL"

        body_xpaths = ['//div[@class="content-text"]//text()', '//div[@class="content"]//text()',
                      '//div[@id="content"]//text()', '//div[contains(@class, "content")]//text()',
                      '//div[contains(@class, "news-content")]//text()', '//article//text()']

        for xpath in body_xpaths:
            body_parts = response.xpath(xpath).getall()
            if body_parts and ''.join(body_parts).strip():
                return body_normalization(body_parts)
        return "No content available"

    def get_images(self, response, entry=None) -> list:
        if response.url.lower().endswith('.pdf'):
            return []

        img_xpaths = ['//div[@class="content-text"]//img/@src', '//div[@class="content"]//img/@src',
                     '//div[contains(@class, "content")]//img/@src', '//article//img/@src',
                     '//img/@src']

        for xpath in img_xpaths:
            img_urls = response.xpath(xpath).getall()
            if img_urls:
                return [urljoin(response.url, img) for img in img_urls]
        return []

    def date_format(self) -> str: return "%Y-%m-%d"

    def get_date(self, response, entry=None) -> str:
        if response.url.lower().endswith('.pdf'):
            date_pattern = r'(\d{4})[_-]?(\d{2})[_-]?(\d{2})'
            match = re.search(date_pattern, response.url)
            if match:
                year, month, day = match.groups()
                return f"{year}-{month}-{day}"

        date_xpaths = ['//div[@class="content-time"]/text()', '//div[contains(@class, "date")]/text()',
                      '//span[contains(@class, "date")]/text()', '//div[contains(@class, "time")]/text()']

        for xpath in date_xpaths:
            date_str = response.xpath(xpath).get()
            if date_str and date_str.strip():
                date_pattern = r'(\d{4})[/\-年](\d{1,2})[/\-月](\d{1,2})'
                match = re.search(date_pattern, date_str)
                if match:
                    year, month, day = match.groups()
                    return f"{year}-{int(month):02d}-{int(day):02d}"

        return datetime.now(pytz.timezone(self.timezone)).strftime(self.date_format())

    def get_authors(self, response, entry=None) -> list:
        if "sse.com.cn" in response.url:
            return ["SSE"]

        author_xpaths = ['//div[contains(@class, "author")]/text()', '//span[contains(@class, "author")]/text()',
                        '//div[contains(@class, "source")]/text()', '//span[contains(@class, "source")]/text()']

        for xpath in author_xpaths:
            author = response.xpath(xpath).get()
            if author and author.strip():
                author = re.sub(r'作者[：:]\s*|来源[：:]\s*', '', author.strip())
                if author:
                    return [author]
        return ["360"]

    def get_document_urls(self, response, entry=None) -> list:
        doc_patterns = ['//a[contains(@href, ".pdf")]/@href', '//a[contains(@href, ".doc")]/@href',
                       '//a[contains(@href, ".docx")]/@href', '//a[contains(@href, ".xls")]/@href',
                       '//a[contains(@href, "download")]/@href', '//a[contains(text(), "下载")]/@href']

        doc_urls = []
        for pattern in doc_patterns:
            urls = response.xpath(pattern).getall()
            if urls:
                doc_urls.extend(urls)

        if "sse.com.cn" in response.url and response.url.lower().endswith('.pdf'):
            doc_urls.append(response.url)

        return [urljoin(response.url, url) for url in set(doc_urls)]

    def get_next_page(self, response) -> str:
        next_page_xpaths = ['//a[contains(text(), "下一页")]/@href', '//a[contains(text(), "Next")]/@href',
                           '//a[@class="next"]/@href', '//a[contains(@class, "next")]/@href']

        for xpath in next_page_xpaths:
            next_page = response.xpath(xpath).get()
            if next_page:
                return response.urljoin(next_page)

        if "page=" in response.url:
            current_page_match = re.search(r'page=(\d+)', response.url)
            if current_page_match:
                current_page = int(current_page_match.group(1))
                next_url = re.sub(r'page=\d+', f'page={current_page + 1}', response.url)
                return next_url
        return None

    def get_page_flag(self) -> bool:
        return False