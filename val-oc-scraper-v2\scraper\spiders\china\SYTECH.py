from typing import Op<PERSON>
from datetime import datetime
import logging
import re

from scraper.OCSpider import <PERSON><PERSON>pid<PERSON>
from scraper.utils.helper import body_normalization


class SYTECH(OCSpider):
    name = "<PERSON>Y<PERSON><PERSON>"

    start_urls_names = {
        "https://www.syst.com.cn/cn/xwsj/list_32.aspx?page=1": "",
    }

    charset = "utf-8"
    article_data_map = {}

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_articles(self, response) -> list:
        self.extract_articles_with_dates(response)
        relative_urls = response.xpath('//div[@class="newsList"]//section[@class="newsEven"]/a/@href').getall()
        return [response.urljoin(url) for url in relative_urls]

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//div[@class="pgTop"]//hgroup[@class="pgTitle new_title_centerd"]/h3/text()').get()

    def get_body(self, response) -> str:
        return " "

    def get_images(self, response) -> list:
        return response.xpath('//div[@class="contactInfo"]//img/@src').getall()

    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> Optional[str]:
        next_page = response.xpath('//div[@class="pagenavi"]/a[@class="a_next"]/@href').get()
        return response.urljoin(next_page) if next_page else None

    def get_itemid_from_url(self, url: str) -> Optional[str]:
        match = re.search(r"itemid=(\d+)", url)
        return match.group(1) if match else None

    def get_date(self, response) -> str:
        itemid = self.get_itemid_from_url(response.url)
        if itemid and itemid in self.article_data_map:
            return self.article_data_map[itemid].get("date", "").strip()

        logging.warning(f"⚠️ Date not found in mapping for URL: {response.url}")
        return ""

    def extract_articles_with_dates(self, response):
        articles = response.xpath('//div[@class="newsList"]/section[@class="newsEven"]')

        for article in articles:
            url = article.xpath(".//a/@href").get()
            url = response.urljoin(url) if url else None
            itemid = self.get_itemid_from_url(url) if url else None

            day = article.xpath(".//div[@class='date']/time/i/text()").get()
            month_year = article.xpath(".//div[@class='date']/time/text()[normalize-space()]").get()

            day = day.strip() if day else ""
            month_year = month_year.strip() if month_year else ""

            date = ""
            if day and month_year:
                try:
                    date_str = f"{month_year}-{day.zfill(2)}"
                    date = datetime.strptime(date_str, "%Y-%m-%d").strftime(self.date_format())
                except Exception as e:
                    logging.warning(f"⚠️ Failed to parse date for URL {url}: {day} {month_year} — {e}")
            else:
                logging.warning(f"⚠️ Incomplete date info for URL {url}")

            if itemid and date:
                self.article_data_map[itemid] = {"date": date}
            elif itemid:
                logging.warning(f"⚠️ Missing or invalid date for itemid {itemid} (URL: {url})")

        return self.article_data_map
