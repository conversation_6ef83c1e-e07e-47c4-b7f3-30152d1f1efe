from typing import List
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import re

class ChinaYangtzePowerCoLtd(OCSpider):
    name = "ChinaYangtzePowerCoLtd"

    start_urls_names = {
        "https://www.cypc.com.cn/cypc/tzzgx43/gdhd/tzzhd/82ba6783-1.html": "长江电力", #pdfs some are articles
        "https://www.cypc.com.cn/cypc/tzzgx43/gsgg15/dqgg/dzyybg/index.html": "长江电力", #pdf, No pagination
        "https://www.cypc.com.cn/cypc/tzzgx43/gsgg15/dqgg/jdbg/fee7a02c-1.html": "长江电力", #pdf 
        "https://www.cypc.com.cn/cypc/tzzgx43/gsgg15/lsgg24/0c3c2729-1.html": "长江电力", #pdf
        "https://www.cypc.com.cn/cypc/tzzgx43/gsgg15/dqgg/ndbg92/3f0c9a0e-1.html": "长江电力", #pdf
        "https://www.cypc.com.cn/cypc/zxgg99/xwzx29/ssxw/1c5ebbda-1.html":"长江电力" 
    }

    article_data_map ={}

    charset = "iso-8859-1"

    @property
    def source_type(self) -> str:
        return "SOE"
    
    @property
    def timezone(self) -> str:
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
            articles = []
            for article in response.xpath("//div[@class='li_con']"):
                url = article.xpath(".//a/@href ").get()
                title = article.xpath(".//a//text()").get()
                if url and title:
                    full_url = url
                    title = title.strip()
                    if '.pdf' in full_url.lower() or '.doc' in full_url.lower():
                        pdf = f'https://www.cypc.com.cn{full_url}'
                    else:
                        pdf = "None"
                    self.article_data_map[full_url] = {"title": title, "pdf": pdf}
                    articles.append(full_url) 
            article_list= set(articles)
            return list(article_list)
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response, entry=None) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response, entry=None) -> str:
        if ".pdf" in response.url.lower() or '.doc' in response.url.lower():
            return ""
        return body_normalization(response.xpath("//div[@class='wid_76']//p//text()").getall())
        
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response, entry=None) -> int:
        if '.pdf' in response.url.lower()  or '.doc' in response.url.lower():
            match1 = re.search(r"/(\d{4})/(\d{1,2})/(\d{1,2})", response.url)
            match2 = re.search(r"/(\d{4})(\d{2})(\d{2})", response.url)
            match3 = re.search(r"/(\d{4})/(\d{1})/(\d{1})", response.url)
            match4 = re.search(r"/(\d{4})/(\d{1,2})/UF(\d{1,2})", response.url)
            if match1:
                year, month, day = match1.groups()
                return f"{year}-{month}-{day}"
            elif match2:
                year, month, day = match2.groups()
                return f"{year}-{month}-{day}"
            elif match3:
                year, month, day = match3.groups()
                return f"{year}-{month}-{day}" 
            elif match4:
                year, month, day = match4.groups()
                return f"{year}-{month}-{day}" 
            return None
        return response.xpath("//div[@class='time_date']//span//text()").get()
        
    def get_authors(self, response, entry=None) -> List[str]:
        if ".pdf" in response.url.lower() or '.doc' in response.url.lower():
            return ""
        return [response.xpath("//div[@class='time_date']//span[@class='authorSpan']//text()").get()]
        
    def get_document_urls(self, response, entry=None) -> list:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("pdf", "")
    
    def get_images(self, response, entry=None) -> List[str]:
        if ".pdf" in response.url.lower() or '.doc' in response.url.lower():
            return ""
        return response.xpath("//div[@class='wid_76']//img//@src").getall()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> List[str]:
        if response.status == 404:
            return None
        current_url = response.url
        match = re.search(r"-(\d+)\.html", current_url)
        if match:
                current_page_num = int(match.group(1))
                next_page_num = current_page_num + 1
                next_page_url = current_url.replace(
                    f"-{current_page_num}.html",
                    f"-{next_page_num}.html"
                )
        else:
                return None
        return next_page_url