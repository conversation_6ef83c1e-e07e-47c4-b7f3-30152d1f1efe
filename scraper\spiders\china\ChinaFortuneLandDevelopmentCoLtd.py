from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import urllib.parse
import scrapy
import json
from scrapy import Selector
from urllib.parse import urlparse

class ChinaFortuneLandDevelopmentCoLtd(OCSpider):
    name = "ChinaFortuneLandDevelopmentCoLtd"

    start_urls_names = {
        "https://www.cfldcn.com/News.aspx?type=13": "华夏幸福",
        "https://www.cfldcn.com/News.aspx?type=18": "华夏幸福",
    }
    
    api_start_urls = {
        "https://www.cfldcn.com/News.aspx?type=13": {
            "url": "https://www.cfldcn.com/ajax/More.ashx",
            "payload" : {
                "type": "GetNewsList",
                "pageindex": "1",
                "pagesize": "5",
                "Coid": "49",
                "types" : "13",
                "newsid" : "845"
            },
        }, 
        "https://www.cfldcn.com/News.aspx?type=18": {
            "url": "https://www.cfldcn.com/ajax/More.ashx",
            "payload" : {
                "type": "GetNewsList",
                "pageindex": "1",
                "pagesize": "5",
                "Coid": "49",
                "types" : "18",
                "newsid" : "845"
            }
        }
    }
    charset = "utf-8"

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36"
    }

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            return
        payload = response.meta.get("payload", api_data["payload"].copy())
        api_url = api_data["url"]
        full_api_url = f"{api_url}?{urllib.parse.urlencode(payload)}"
        yield scrapy.Request(
            url=full_api_url,
            method="POST",
            headers=self.headers,
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "payload": payload,
                "current_page": payload["pageindex"]
            }
        )

    @property
    def source_type(self) -> str:
        return "private_enterprise"

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_articles(self, response):
        try:
            data = json.loads(response.text)
        except json.JSONDecodeError:
            return []
        html_content = data.get('list', '')
        if not html_content:
            return []
        sel = Selector(text=html_content)
        relative_links = sel.xpath('//a/@href').getall()
        base_url = "https://www.cfldcn.com/"
        full_links = []
        for href in relative_links:
            if href.startswith('/ajax/News.aspx'):
                href = href.replace('/ajax/', '/')
            full_url = urllib.parse.urljoin(base_url, href)
            parsed = urlparse(full_url)
            if (
                parsed.netloc == "www.cfldcn.com"
                and parsed.path == "/News.aspx"
                and "id=" in parsed.query
                and ("type=13" in parsed.query or "type=18" in parsed.query)
            ):
                full_links.append(full_url)
        return full_links

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return "".join(response.xpath('(//div[@class="title"])[3]//text()').getall()).strip()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="details"]//text()').getall()) 
        
    def get_images(self, response) -> list:
        return response.xpath('//div[@class="details"]//img/@src').getall()

    def date_format(self) -> str:
        return "%Y.%m.%d"
    
    def get_date(self, response) -> str:
        return response.xpath('//div[@class="time"]/text()').get().strip()
        
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response, current_page: Optional[int] = 1) -> Optional[int]:
        next_page = int(current_page) + 1
        return next_page
    
    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        api_data = self.api_start_urls.get(start_url)
        api_url = api_data["url"]
        payload = response.meta.get("payload", {}).copy()
        next_page = self.get_next_page(response, current_page)
        payload["pageindex"] = next_page  
        full_api_url = f"{api_url}?page={payload['pageindex']}"
        yield scrapy.Request(
            url=full_api_url,
            method="POST",
            headers=self.headers,
            callback=self.parse_intermediate,
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "payload": payload,
                "current_page": next_page
            },
        )