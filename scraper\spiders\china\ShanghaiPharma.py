from typing import List, Optional
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
import re

class ShanghaiPharma(OCSpider):
    name = "ShanghaiPharma"

    start_urls_names = {
        ' https://www.sphchina.com/news_center/news_release.html',#normal
       'https://www.sphchina.com/news_center/media_coverage.html',#normal
       'https://www.sphchina.com/investor_relations/financial_report.html',#pdf
       'https://www.sphchina.com/investor_relations/announcement&circular.html',#pdf
       ' https://www.sphchina.com/investor_relations/powerpoint.html'#pdf

    }

    article_data_map = {}
    charset = "iso-8859-1"

    @property
    def source_type(self) -> str:
        return "SOE"
    
    @property
    def timezone(self) -> str:
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        articles = []
        for article in response.xpath('//div[@class="news_pane"]//a//@href'):
            url = article.xpath(".//a/@href").get()
            title = article.xpath(".//a//text()").get()
            date = article.xpath(".//span//text()").get()
            if url and title and date:
                full_url = response.urljoin(url)
                title = title.strip()
                pdf = full_url if '.pdf' in full_url.lower() else "None"
                clean_date = date.strip()
                self.article_data_map[full_url] = {
                    "title": title,
                    "date": clean_date,
                    "pdf": pdf
                }
                articles.append(full_url)
        return articles
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response, entry=None) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response, entry=None) -> str:
        if ".pdf" in response.url.lower():
            return ""
        body = body_normalization(response.xpath('//div[@class="content_padding_right"]//p//text()').getall())
        return body

        
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response, entry=None) -> str:
        date_from_map = self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        if date_from_map:
            return date_from_map
        xpath_date = response.xpath('//div[@class="col-xs-6"]/h6[@id="currentDate"]/text()').get()
        return xpath_date.strip() if xpath_date else ""
    
    def get_authors(self, response, entry=None) -> List[str]:
        return []
    
    def get_document_urls(self, response, entry=None) -> list:
        pdf_url = self.article_data_map.get(response.request.meta.get('entry'), {}).get("pdf")
        if pdf_url and pdf_url != "None":
            return [pdf_url]  
        else:
            pdf= response.xpath('//div[@class="inner-container"]//p//a//@href').getall()
            for i in pdf:
                f'{response.url}{i}'

    def get_images(self, response, entry=None) -> List[str]:
        if ".pdf" in response.url.lower():
            return []
        return response.xpath('//div[@class="news_image_pane hovereffect"]//img//@src').getall()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        if response.status == 404:
            return None
        current_url = response.url
        if "index.html" in current_url:
            next_page_url = current_url.replace("index.html", "index_1.html")
        else:
            match = re.search(r"index_(\d+)\.html", current_url)
            if match:
                current_page_num = int(match.group(1))
                next_page_num = current_page_num + 1
                next_page_url = current_url.replace(
                    f"index_{current_page_num}.html",
                    f"index_{next_page_num}.html"
                )
            else:
                return None
        return next_page_url