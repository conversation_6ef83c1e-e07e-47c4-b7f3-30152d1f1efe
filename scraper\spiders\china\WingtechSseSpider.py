from scraper.OCSpider import <PERSON><PERSON>pider
from scraper.utils.helper import body_normalization
from urllib.parse import urljoin
import re
from datetime import datetime
import pytz
import json
import scrapy
from scrapy.http import Request
import logging

class WingtechSseSpider(OCSpider):
    name = "WingtechSseSpider"
    country = "CN"

    start_urls_names = {
        "http://www.wingtech.com/cn/toWTNEWS/21/1": "Wingtech News",
        "https://www.sse.com.cn/assortment/stock/list/info/announcement/index.shtml?productId=600745": "SSE Announcements",
    }
    charset = "utf-8"

    # Custom settings for better performance
    custom_settings = {
        "DOWNLOAD_DELAY": 1,
        "CONCURRENT_REQUESTS_PER_DOMAIN": 2,
    }

    @property
    def source_type(self) -> str:
        return "Corporate"

    @property
    def language(self) -> str:
        return "Chinese"

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def parse_intermediate(self, response):
        """Optional method for headless mode if needed"""
        return self.parse(response)

    def get_articles(self, response) -> list:
        if "wingtech.com" in response.url:
            # For Wingtech website
            # The website structure shows news items in a list
            # First, try to find news items in the main content area
            news_items = response.xpath('//div[@class="news_list"]/ul/li')
            if not news_items:
                # Try alternative XPath
                news_items = response.xpath('//ul[@class="news_list"]/li')

            links = []
            for item in news_items:
                link = item.xpath('./a/@href').get()
                if link:
                    links.append(link)

            # If no links found with the above methods, try a more general approach
            if not links:
                # Look for links that might be news articles
                all_links = response.xpath('//a/@href').getall()
                for link in all_links:
                    # Filter links that look like news articles
                    if '/cn/news/' in link or '/cn/detail/' in link or '/cn/toWTNEWS/' in link:
                        links.append(link)
                    # Also include links that might be relative paths to articles
                    elif link.startswith('/cn/') and not link.startswith('//') and not link.startswith('/#'):
                        links.append(link)

            # Remove duplicates and filter out navigation links
            filtered_links = []
            for link in links:
                if link not in filtered_links and not any(nav in link for nav in ['javascript:', '#', 'mailto:', 'tel:']):
                    filtered_links.append(link)

            return [response.urljoin(link) for link in filtered_links]
        else:
            # For SSE website
            # SSE website uses JavaScript to load content, so we need to extract from the HTML structure
            links = response.xpath('//table[@class="table search_result_table"]//td/a/@href').getall()
            if not links:
                # Try alternative XPath
                links = response.xpath('//div[@class="content-list"]//a/@href').getall()

            # If still no links, try a more general approach
            if not links:
                # Look for links that might be announcements
                all_links = response.xpath('//a/@href').getall()
                links = [link for link in all_links if '.pdf' in link or '.doc' in link or '.docx' in link or 'announcement' in link]

            return [response.urljoin(link) for link in links if link]

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response, entry=None) -> str:
        if "wingtech.com" in response.url:
            # Try multiple XPath patterns for Wingtech titles
            title_xpaths = [
                '//div[@class="news_title"]/text()',
                '//h1[@class="title"]/text()',
                '//h1/text()',
                '//div[@class="title"]/text()',
                '//div[contains(@class, "title")]/text()',
                '//title/text()'
            ]

            for xpath in title_xpaths:
                title = response.xpath(xpath).get()
                if title and title.strip():
                    return title.strip()
        else:
            # Try multiple XPath patterns for SSE titles
            title_xpaths = [
                '//div[@class="content-title"]/text()',
                '//h1/text()',
                '//div[@class="title"]/text()',
                '//div[contains(@class, "title")]/text()',
                '//title/text()'
            ]

            for xpath in title_xpaths:
                title = response.xpath(xpath).get()
                if title and title.strip():
                    return title.strip()

        # If no title found, use the filename from URL as fallback
        url_parts = response.url.split('/')
        if url_parts and url_parts[-1]:
            return f"Article: {url_parts[-1]}"

        return "No Title"

    def get_body(self, response, entry=None) -> str:
        if "wingtech.com" in response.url:
            # Try multiple XPath patterns for Wingtech body content
            body_xpaths = [
                '//div[@class="news_content"]//text()',
                '//div[@class="content"]//text()',
                '//div[@id="content"]//text()',
                '//div[contains(@class, "content")]//text()',
                '//div[@class="article-content"]//text()',
                '//article//text()'
            ]

            for xpath in body_xpaths:
                body_parts = response.xpath(xpath).getall()
                if body_parts and ''.join(body_parts).strip():
                    return body_normalization(body_parts)
        else:
            # Try multiple XPath patterns for SSE body content
            body_xpaths = [
                '//div[@class="content-text"]//text()',
                '//div[@class="content"]//text()',
                '//div[@id="content"]//text()',
                '//div[contains(@class, "content")]//text()',
                '//div[@class="article-content"]//text()',
                '//article//text()'
            ]

            for xpath in body_xpaths:
                body_parts = response.xpath(xpath).getall()
                if body_parts and ''.join(body_parts).strip():
                    return body_normalization(body_parts)

        return "No content available"

    def get_images(self, response, entry=None) -> list:
        if "wingtech.com" in response.url:
            # Try multiple XPath patterns for Wingtech images
            img_xpaths = [
                '//div[@class="news_content"]//img/@src',
                '//div[@class="content"]//img/@src',
                '//div[@id="content"]//img/@src',
                '//div[contains(@class, "content")]//img/@src',
                '//article//img/@src',
                '//img/@src'  # Fallback to all images if needed
            ]

            for xpath in img_xpaths:
                img_urls = response.xpath(xpath).getall()
                if img_urls:
                    return [urljoin(response.url, img) for img in img_urls]
        else:
            # Try multiple XPath patterns for SSE images
            img_xpaths = [
                '//div[@class="content-text"]//img/@src',
                '//div[@class="content"]//img/@src',
                '//div[@id="content"]//img/@src',
                '//div[contains(@class, "content")]//img/@src',
                '//article//img/@src',
                '//img/@src'  # Fallback to all images if needed
            ]

            for xpath in img_xpaths:
                img_urls = response.xpath(xpath).getall()
                if img_urls:
                    return [urljoin(response.url, img) for img in img_urls]

        return []

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response, entry=None) -> str:
        if "wingtech.com" in response.url:
            # Try multiple XPath patterns for Wingtech dates
            date_xpaths = [
                '//div[@class="news_date"]/text()',
                '//span[@class="date"]/text()',
                '//div[contains(@class, "date")]/text()',
                '//span[contains(@class, "date")]/text()',
                '//div[@class="time"]/text()',
                '//span[@class="time"]/text()',
                '//div[contains(@class, "time")]/text()',
                '//span[contains(@class, "time")]/text()',
                '//div[@class="info"]/text()',
                '//p[@class="info"]/text()'
            ]

            for xpath in date_xpaths:
                date_str = response.xpath(xpath).get()
                if date_str:
                    # Try different date formats
                    date_patterns = [
                        r'(\d{4}-\d{2}-\d{2})',  # YYYY-MM-DD
                        r'(\d{4}/\d{2}/\d{2})',  # YYYY/MM/DD
                        r'(\d{4}\.\d{2}\.\d{2})',  # YYYY.MM.DD
                        r'(\d{4}年\d{2}月\d{2}日)'  # YYYY年MM月DD日
                    ]

                    for pattern in date_patterns:
                        date_match = re.search(pattern, date_str)
                        if date_match:
                            date_found = date_match.group(1)
                            # Convert to standard format if needed
                            if '/' in date_found:
                                date_found = date_found.replace('/', '-')
                            elif '.' in date_found:
                                date_found = date_found.replace('.', '-')
                            elif '年' in date_found:
                                date_found = date_found.replace('年', '-').replace('月', '-').replace('日', '')
                            return date_found
        else:
            # Try multiple XPath patterns for SSE dates
            date_xpaths = [
                '//div[@class="content-time"]/text()',
                '//span[@class="time"]/text()',
                '//div[contains(@class, "date")]/text()',
                '//span[contains(@class, "date")]/text()',
                '//div[@class="time"]/text()',
                '//div[contains(@class, "time")]/text()',
                '//span[contains(@class, "time")]/text()',
                '//div[@class="info"]/text()',
                '//p[@class="info"]/text()'
            ]

            for xpath in date_xpaths:
                date_str = response.xpath(xpath).get()
                if date_str:
                    # Try different date formats
                    date_patterns = [
                        r'(\d{4}-\d{2}-\d{2})',  # YYYY-MM-DD
                        r'(\d{4}/\d{2}/\d{2})',  # YYYY/MM/DD
                        r'(\d{4}\.\d{2}\.\d{2})',  # YYYY.MM.DD
                        r'(\d{4}年\d{2}月\d{2}日)'  # YYYY年MM月DD日
                    ]

                    for pattern in date_patterns:
                        date_match = re.search(pattern, date_str)
                        if date_match:
                            date_found = date_match.group(1)
                            # Convert to standard format if needed
                            if '/' in date_found:
                                date_found = date_found.replace('/', '-')
                            elif '.' in date_found:
                                date_found = date_found.replace('.', '-')
                            elif '年' in date_found:
                                date_found = date_found.replace('年', '-').replace('月', '-').replace('日', '')
                            return date_found

        # Return current date if no date found
        return datetime.now(pytz.timezone(self.timezone)).strftime(self.date_format())

    def get_authors(self, response, entry=None) -> list:
        if "wingtech.com" in response.url:
            # Try multiple XPath patterns for Wingtech authors
            author_xpaths = [
                '//div[@class="news_author"]/text()',
                '//span[@class="author"]/text()',
                '//div[contains(@class, "author")]/text()',
                '//span[contains(@class, "author")]/text()',
                '//div[@class="source"]/text()',
                '//span[@class="source"]/text()',
                '//div[contains(@class, "source")]/text()',
                '//span[contains(@class, "source")]/text()'
            ]

            for xpath in author_xpaths:
                author = response.xpath(xpath).get()
                if author and author.strip():
                    # Clean up author name
                    author = author.strip()
                    # Remove common prefixes like "作者：" or "来源："
                    author = re.sub(r'^(作者[：:]\s*|来源[：:]\s*|编辑[：:]\s*|记者[：:]\s*)', '', author)
                    return [author]

            # Default author if none found
            return ["Wingtech"]
        else:
            # Try multiple XPath patterns for SSE authors
            author_xpaths = [
                '//div[@class="content-author"]/text()',
                '//span[@class="author"]/text()',
                '//div[contains(@class, "author")]/text()',
                '//span[contains(@class, "author")]/text()',
                '//div[@class="source"]/text()',
                '//span[@class="source"]/text()',
                '//div[contains(@class, "source")]/text()',
                '//span[contains(@class, "source")]/text()'
            ]

            for xpath in author_xpaths:
                author = response.xpath(xpath).get()
                if author and author.strip():
                    # Clean up author name
                    author = author.strip()
                    # Remove common prefixes like "作者：" or "来源："
                    author = re.sub(r'^(作者[：:]\s*|来源[：:]\s*|编辑[：:]\s*|记者[：:]\s*)', '', author)
                    return [author]

            # Default author if none found
            return ["SSE"]

    def get_document_urls(self, response, entry=None) -> list:
        # Look for document links in the page
        doc_patterns = [
            '//a[contains(@href, ".pdf")]/@href',
            '//a[contains(@href, ".doc")]/@href',
            '//a[contains(@href, ".docx")]/@href',
            '//a[contains(@href, ".xls")]/@href',
            '//a[contains(@href, ".xlsx")]/@href',
            '//a[contains(@href, ".ppt")]/@href',
            '//a[contains(@href, ".pptx")]/@href',
            '//a[contains(@href, ".zip")]/@href',
            '//a[contains(@href, ".rar")]/@href',
            '//a[contains(@href, "download")]/@href',
            '//a[contains(text(), "下载")]/@href',
            '//a[contains(text(), "附件")]/@href'
        ]

        doc_urls = []
        for pattern in doc_patterns:
            urls = response.xpath(pattern).getall()
            if urls:
                doc_urls.extend(urls)

        # Remove duplicates and convert to absolute URLs
        return [urljoin(response.url, doc) for doc in set(doc_urls)]

    def get_next_page(self, response) -> list:
        next_pages = []

        if "wingtech.com" in response.url:
            # Try to extract current page number from URL
            current_page_match = re.search(r'/(\d+)$', response.url)
            if current_page_match:
                current_page = int(current_page_match.group(1))
                next_page = current_page + 1
                # Limit to a reasonable number of pages to avoid infinite loops
                if next_page <= 10:  # Assuming max 10 pages
                    next_url = re.sub(r'/\d+$', f'/{next_page}', response.url)
                    next_pages.append(next_url)

            # Try different XPath patterns for pagination links
            next_page_xpaths = [
                '//a[contains(text(), "下一页")]/@href',
                '//a[contains(text(), "Next")]/@href',
                '//a[@class="next"]/@href',
                '//a[contains(@class, "next")]/@href',
                '//a[@rel="next"]/@href',
                '//li[@class="next"]/a/@href',
                '//div[@class="pagination"]/a[last()]/@href'
            ]

            for xpath in next_page_xpaths:
                next_page_url = response.xpath(xpath).get()
                if next_page_url:
                    next_pages.append(response.urljoin(next_page_url))
        else:
            # For SSE website, try different XPath patterns for pagination
            next_page_xpaths = [
                '//a[contains(text(), "下一页")]/@href',
                '//a[contains(text(), "Next")]/@href',
                '//a[@class="next"]/@href',
                '//a[contains(@class, "next")]/@href',
                '//a[@rel="next"]/@href',
                '//li[@class="next"]/a/@href',
                '//div[@class="pagination"]/a[last()]/@href'
            ]

            for xpath in next_page_xpaths:
                next_page_url = response.xpath(xpath).get()
                if next_page_url:
                    next_pages.append(response.urljoin(next_page_url))

        return next_pages

    def get_page_flag(self) -> bool:
        return True

    def get_meta(self, response, entry=None) -> list:
        return []

    def get_pdf(self, response, entry=None):
        return None
