from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime

class AlabamaDepartmentOfCommerce(OCSpider):
    name = "AlabamaDepartmentOfCommerce"

    country = "US"

    start_urls_names = {
        "https://www.madeinalabama.com/news/page/2/": "News",
    }

    charset = "utf-8"

    article_date_map = {} # Mapping article links and dates from start_url

    @property
    def language(self): 
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "America/Chicago"
    
    def get_articles(self, response) -> list:
        self.extract_articles_with_dates(response)  
        return response.xpath('//h2[@class="alignwide has-large-font-size wp-block-post-title"]/a/@href').getall()
       
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="alignwide is-style-fw-h2 fw-hero-news__title wp-block-post-title"]/text()').get()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@id="container_main"]//text()').getall()) 
        
    def get_images(self, response) -> list:
        return response.xpath('//figure[@class="wp-block-image size-large"]/img/@src | //figure[@class="alignwide wp-block-post-featured-image"]/img/@src')
    
    def date_format(self) -> str:
        return "%m-%d-%Y"  
    
    def get_date(self, response) -> str:
        url = response.url
        raw_date = AlabamaDepartmentOfCommerce.article_date_map[url]
        raw_date = raw_date.split("T")[0]  
        date_obj = datetime.strptime(raw_date, "%Y-%m-%d")
        return date_obj.strftime(self.date_format())  

    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        next_page = response.xpath('//a[@class="wp-block-button__link wp-element-button" and text()="See older news"]/@href |  //a[@class="wp-block-query-pagination-next"]/@href').get()
        return next_page
    
    def extract_articles_with_dates(self, response):
        article_selector = '//li[contains(@class, "wp-block-post")]'
        url_xpath = './/h2[contains(@class, "wp-block-post-title")]/a/@href'
        date_xpath = './/div[contains(@class, "wp-block-post-date")]//time/@datetime'
        for article in response.xpath(article_selector):
            url = article.xpath(url_xpath).get()
            date = article.xpath(date_xpath).get()
            full_url = response.urljoin(url)
            AlabamaDepartmentOfCommerce.article_date_map[full_url] = date.strip()
        return AlabamaDepartmentOfCommerce.article_date_map