from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class StateAdministrationOfTaxation(OCSpider):

    name = 'StateAdministrationOfTaxation'

    
    HEADLESS_BROWSER_WAIT_TIME = 10000 # 10 seconds

    custom_settings = { 
		"DOWNLOADER_MIDDLEWARES" : {
			# this middleware uses a headless browser to fetch the content
			'scraper.middlewares.HeadlessBrowserProxy': 350,
		},
		"DOWNLOAD_DELAY" : 2,            
	}

    start_urls_names = {
        'https://www.chinatax.gov.cn/chinatax/manuscriptList/n810724?_isAgg=false&_pageSize=20&_template=index&_channelName=&_keyWH=wenhao&page=1': '税务新闻',
        'https://www.chinatax.gov.cn/chinatax/manuscriptList/n810780?_isAgg=false&_pageSize=20&_template=index&_channelName=&_keyWH=wenhao&page=1':'媒体视点',
        'https://www.chinatax.gov.cn/chinatax/manuscriptList/n810739?_isAgg=false&_pageSize=20&_template=index&_channelName=&_keyWH=wenhao&page=1':'各地动态'
    }

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_articles(self, response) -> list:
        return response.xpath("//ul[@class='list ']/li//a/@href").getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return  " ".join(response.xpath("//div[@class='biaoti ']//div[@class='sv_texth1 ']//text()").getall()).strip()

    def get_body(self, response) -> str:
        return body_normalization( response.xpath("//div[@id='fontzoom']//p//text()").getall())

    def date_format(self) -> str:
        return '%Y-%m-%d'

    def get_date(self, response) -> str:
        return response.xpath("//publishtime/text()").get()
        
    def get_images(self, response) -> list:
        return [
            response.urljoin(url)
            for url in response.xpath("//div[@id='fontzoom']//img/@src").getall()
        ]

    def get_authors(self, response):
        return None


    def get_next_page(self, response) -> str:
        current_page = int(response.url.split('page=')[1])
        
        page_list = response.xpath("//div[@class='page_num ']//ul/li/a/@href").getall()
        
        next_page_url = response.url.split('page=')[0] + f'page={current_page+1}'
        
        if next_page_url in page_list:
            return next_page_url
        else:
            return None
