class UnhandledCaseError(Exception):
    """Exception raised when an unexpected case is encountered in logic flow."""

    def __init__(self, message="An unhandled case encountered."):
        super().__init__(message)

class LogicalInconsistencyError(Exception):
    """Exception raised when a logical inconsistency is detected in the data."""

    def __init__(self, message="A logical inconsistency was detected."):
        super().__init__(message)