from typing import Optional
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
from datetime import datetime

class GeorgiaDepartmentOfEconomicDevelopment(OCSpider):
    name = "GeorgiaDepartmentOfEconomicDevelopment"
    
    country = "US"

    start_urls_names = {
        "https://www.georgia.org/newsroom": "Press Releases",
    }

    charset = "utf-8"
    
    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350
        },
        "DOWNLOAD_DELAY": 5,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }
    
    article_date_map = {}  

    @property
    def language(self):
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self):
        return "America/New_York"

    def get_articles(self, response) -> list:
        self.extract_articles_with_dates(response) 
        links = response.xpath('//div[@class="view-content"]//article//h3/a/@href').getall()
        return [response.urljoin(link) for link in links]

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//div[@class="content"]/h1/text() | //div[@class="content"]/h2/text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="field field--name-field-main-content-body field--type-text-with-summary field--label-hidden field__item"]//p//text()').getall())

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%m-%d-%Y"

    def get_date(self, response) -> str:
        date_raw = self.article_date_map.get(response.url)
        if date_raw:
            try:
                date_obj = datetime.strptime(date_raw.strip(), "%B %d, %Y")
                return date_obj.strftime(self.date_format())
            except ValueError:
                return date_raw.strip()
        return None
        
    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> Optional[str]:
        next_page = response.xpath('//li[@class="pager__item pager__item--next"]/a/@href').get()
        return response.urljoin(next_page) 

    def extract_articles_with_dates(self, response):
        for article in response.xpath('//div[@class="views-row"]'):
            url = article.xpath('.//h3/a/@href').get()
            date_raw = article.xpath('.//h3/a/span[@class="date"]/text()').get()
            if url and date_raw:
                try:
                    date_obj = datetime.strptime(date_raw.strip(), "%b %d, %Y")
                    formatted_date = date_obj.strftime(self.date_format())
                except ValueError as ve:
                    formatted_date = date_raw.strip()
                full_url = response.urljoin(url)
                self.article_date_map[full_url] = formatted_date
        return self.article_date_map