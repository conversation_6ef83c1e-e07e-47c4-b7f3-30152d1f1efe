from typing import List, Optional
from scraper.OCSpider import <PERSON><PERSON>pider
from scraper.utils.helper import body_normalization
import re
from datetime import datetime
from urllib.parse import urljoin

class WustecCninfoScraper(OCSpider):
    name = " WustecCninfoScraper"

    # country = "CN"

    # Start URLs for the websites
    start_urls_names = {
        "http://www.wustec.com/news.php": "Wustec News",
        "https://irm.cninfo.com.cn/ircs/company/companyDetail?stockcode=002463&orgId=9900013929": "CNINFO Company Detail",
    }

    custom_settings = {
        "DOWNLOAD_DELAY": 2,
        "DOWNLOAD_TIMEOUT": 60,
        "USER_AGENT": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "Corporate"

    @property
    def language(self) -> str:
        return "Chinese"

    @property
    def timezone(self) -> str:
        return "Asia/Shanghai"

    def get_page_flag(self) -> bool:
        return True

    def get_articles(self, response) -> list:
        articles = []
        # Extract article links based on website
        if "wustec.com" in response.url:
            links = response.xpath('//div[contains(@class, "news")]//a/@href').getall() or \
                   response.xpath('//a[contains(@href, "newsdetail.php")]/@href').getall()
        elif "cninfo.com.cn" in response.url:
            links = response.xpath('//div[contains(@class, "announcement-list")]//a/@href').getall() or \
                   response.xpath('//a[contains(@href, "announcement") or contains(@href, "detail")]/@href').getall()

        # Filter and join URLs
        for link in links or []:
            if link and not link.startswith(('#', 'javascript:')):
                articles.append(response.urljoin(link))
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response, entry=None) -> str:
        # Extract title based on website
        if "wustec.com" in response.url:
            title = response.xpath('//div[@class="news_title"]/text()').get() or \
                   response.xpath('//h1/text()').get()
        elif "cninfo.com.cn" in response.url:
            title = response.xpath('//div[@class="detail-header"]/h1/text()').get() or \
                   response.xpath('//div[contains(@class, "announcement-title")]/text()').get()
        else:
            title = response.xpath('//title/text()').get()
        return title.strip() if title else ""

    def get_body(self, response, entry=None) -> str:
        # Extract body based on website
        if "wustec.com" in response.url:
            body_parts = response.xpath('//div[@class="news_content"]//text()').getall() or \
                        response.xpath('//div[@class="content"]//text()').getall()
        elif "cninfo.com.cn" in response.url:
            body_parts = response.xpath('//div[@class="detail-content"]//text()').getall() or \
                        response.xpath('//div[contains(@class, "announcement-content")]//text()').getall()
        else:
            body_parts = response.xpath('//body//text()').getall()
        return body_normalization(body_parts)

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response, entry=None) -> str:
        # Extract date based on website
        if "wustec.com" in response.url:
            date_str = response.xpath('//div[@class="news_date"]/text()').get() or \
                      response.xpath('//span[contains(@class, "date")]/text()').get()
        elif "cninfo.com.cn" in response.url:
            date_str = response.xpath('//div[@class="detail-header"]//span[contains(text(), "日期")]/following-sibling::text()').get() or \
                      response.xpath('//span[contains(@class, "announcement-date")]/text()').get()

        if date_str:
            date_str = date_str.strip()
            date_match = re.search(r'(\d{4}[-/年]\d{1,2}[-/月]\d{1,2})', date_str)
            if date_match:
                date_str = date_match.group(1).replace('年', '-').replace('月', '-').replace('日', '').replace('/', '-')
                return date_str
        return datetime.now().strftime(self.date_format())

    def get_images(self, response, entry=None) -> List[str]:
        # Extract images based on website
        if "wustec.com" in response.url:
            img_urls = response.xpath('//div[@class="news_content"]//img/@src').getall() or \
                      response.xpath('//div[@class="content"]//img/@src').getall()
        elif "cninfo.com.cn" in response.url:
            img_urls = response.xpath('//div[@class="detail-content"]//img/@src').getall() or \
                      response.xpath('//div[contains(@class, "announcement-content")]//img/@src').getall()
        else:
            img_urls = response.xpath('//img/@src').getall()
        return [urljoin(response.url, img) for img in img_urls or []]

    def get_next_page(self, response) -> List[str]:
        # Extract next page URL based on website
        if "wustec.com" in response.url:
            next_page = response.xpath('//a[contains(text(), "下一页") or contains(@class, "next")]/@href').get()
        elif "cninfo.com.cn" in response.url:
            next_page = response.xpath('//a[contains(text(), "下一页") or contains(@class, "next-page")]/@href').get()
        return [response.urljoin(next_page)] if next_page else []

    def get_authors(self, response, entry=None) -> List[str]:
        return ["Unknown"]

    def get_document_urls(self, response, entry=None) -> list:
        # Extract document URLs
        doc_urls = response.xpath('//a[contains(@href, ".pdf") or contains(@href, ".doc") or contains(@href, ".docx")]/@href').getall()
        return [urljoin(response.url, doc) for doc in doc_urls or []]

    def get_meta(self, response, entry=None) -> list:
        return []

    def get_pdf(self, response, entry=None):
        return None