from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime
import scrapy

class VirginiaDepartmentOfTheTreasury(OCSpider):
    name = "VirginiaDepartmentOfTheTreasury"

    start_urls_names = {
        "https://www.tax.virginia.gov/newsroom": "Latest News & Resources"
    }

    charset = "utf-8"
    
    article_date_map = {}  # Mapping articles with dates

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self): 
        return "English"

    @property
    def timezone(self):
        return "America/New_York"

    def get_articles(self, response) -> list:
        self.extract_articles_with_dates(response)
        article_urls = response.xpath("//div[@class='article-container']/a/@href").getall()
        return [response.urljoin(link) for link in article_urls]

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//div[@class="col-xs-12"]//h1/span/text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="col-xs-12 col-lg-9 push-lg-3"]//p/text()').getall())

    def get_images(self, response) -> list:
        return response.xpath('//div[@id="content"]//img/@src').getall()

    def date_format(self) -> str:
        return "%m-%d-%Y"

    def get_date(self, response) -> str:
        raw_date = self.article_date_map.get(response.url)
        return datetime.strptime(raw_date, "%B %d, %Y").strftime("%m-%d-%Y")

    def get_authors(self, response):
        return []

    def get_next_page(self, response) -> Optional[str]:
        current_page = response.meta.get("current_page", 1)
        total_pages = response.xpath('//li[@class="pager__item pagerer-suffix"]/span/text()').get()
        if total_pages:
            total_pages = int(total_pages.split("of")[-1].strip())  # Extract number from "of XX"
        if current_page >= total_pages:
            return None
        next_page = response.xpath(
            '//div[@class="pagerer-center-pane"]//nav[@class="pager pagerer-pager-mini"]//ul[@class="pager__items js-pager__items"]'
            '//li[@class="pager__item pager__item--next"]//a/@href').get()
        return response.urljoin(next_page) if next_page else None
    
    def get_page_flag(self) -> bool:
        return False

    def go_to_next_page(self, response, *args, **kwargs):
        current_page = response.meta.get("current_page", 1)
        start_url = response.meta.get("start_url", response.url)
        next_page = self.get_next_page(response)
        if next_page:
            yield scrapy.Request(next_page, callback=self.parse,
                                  meta={'start_url': start_url, 'current_page': current_page + 1})
            
    def extract_articles_with_dates(self, response):
        paths = [
           
            ('//div[contains(@class, "col-md-4 col-sm-6 col-xs-12 article-col")]', #Example: https://www.tax.virginia.gov/news/individual-income-tax-filing-season-underway-2025
             './/a[@hreflang="en"]/@href')  #Example: https://www.tax.virginia.gov/news/virginia-elective-pass-through-entity-tax
        ]
        for container, url_path in paths:
            for article in response.xpath(container):
                url = article.xpath(url_path).get()
                date = article.xpath('.//div[contains(@class, "article-post-date")]/span[1]/text()').get()
                if url and date:
                    self.article_date_map[response.urljoin(url)] = date.strip()
        return self.article_date_map