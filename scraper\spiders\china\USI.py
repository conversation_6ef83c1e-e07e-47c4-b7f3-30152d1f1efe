from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class USI(OCSpider):
    name = "USI"

    start_urls_names = {
        "https://www.usiglobal.com/cn/press-center": "",
       
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"
    
    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        return response.xpath('//ul[@class="list noneStyle"]//li//a[contains(@href, "/cn/news/")]/@href').getall()
        
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h3[@class="in-title eng f36"]/text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath(  '//div[@class="editor"]').getall())
    
    def get_images(self, response) -> list:
        return response.xpath('//div[@class="banner"]/img/@src').getall()
       
    def date_format(self) -> str:
        return"%m/%d/%Y"
    
    def get_date(self, response) -> str:
        return response.xpath('//li[@class="date"]/text()').get().strip()
    
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]: 
        next_page = response.xpath('//li[@class="next"]/a/@href').get()
        if next_page:
            return next_page
        return None