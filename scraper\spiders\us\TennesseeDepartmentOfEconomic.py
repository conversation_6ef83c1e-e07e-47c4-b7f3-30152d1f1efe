from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime
from typing import Optional

class TennesseeDepartmentOfEconomic(OCSpider):
    name = "TennesseeDepartmentOfEconomic"

    country = "US"

    charset="utf-8"

    start_urls_names = {
        "https://tnecd.com/media/newsroom/" : "Newsroom"
    }

    @property
    def language(self) -> str:
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self) -> str:
        return "US/Eastern"

    article_date_map = {}  # Mapping child articles with date from start URL

    def get_articles(self, response) -> list:
        self.extract_articles_with_dates(response)
        return [response.urljoin(link) for link in response.xpath('//a[@class="newsCardLink"]/@href').getall()]

    def get_href(self, entry: str) -> str:
        return entry

    def get_title(self, response) :
        return response.xpath('//div[@class="large-10 medium-10 cell text-center"]/h1/text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="grid-container"]//p//text()').getall())

    def get_images(self, response) :
        return []

    def date_format(self) -> str:
        return "%Y/%m/%d" 

    def get_date(self, response) -> Optional[str]:
        article_url = response.url
        raw_date = self.article_date_map.get(article_url)
        if not raw_date:
            self.logger.warning(f"Date not found for article: {article_url}")
            return None
        for fmt in ("%B %d, %Y", "%m.%d.%Y"):
            try:
                date_obj = datetime.strptime(raw_date, fmt)
                return date_obj.strftime("%Y/%m/%d")
            except ValueError:
                continue
        self.logger.error(f"Error parsing date '{raw_date}' for {article_url}")
        return None

    def get_authors(self, response):
        author_text = response.xpath('//p[@class="blog-author"]/text() | //p[@class="author"]/text()[2]').get()
        if author_text:
            return author_text.strip().replace("By:", "").strip()
        return None

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response):
        next_page_link = response.xpath('//a[@class="nextpostslink"]/@href').get()
        if next_page_link:
            return response.urljoin(next_page_link)
        else:
            return None

    def extract_articles_with_dates(self, response):
        for article in response.xpath('//a[@class="newsCardLink"]'):
            url = article.xpath('./@href').get()
            date = article.xpath('.//div[@class="newsDate"]/text()').get()
            if url and date:
                full_url = response.urljoin(url)
                self.article_date_map[full_url] = date.strip()