from typing import List, Optional
from scraper.OCSpider import <PERSON><PERSON>pider
from scraper.utils.helper import body_normalization
import re

class KansasDepartmentOfTransportation(OCSpider):
    name = "KansasDepartmentOfTransportation"
    
    country  = "US"
    
    start_urls_names = {
         'https://www.ksdot.org/about/advanced-components/list-detail-pages/news-list':'News'
    }
     
    proxy_country = "us"
    
    custom_settings = {
        "DOWNLOADER_MIDDLEWARES":
            {
                # Using Geopproxy
                'scraper.middlewares.GeoProxyMiddleware': 350,
            },
        "DOWNLOAD_DELAY": 1,
    }
    
    charset = "utf-8"
    
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"
    @property
    def timezone(self):
        return "US/Central"
    
    def get_articles(self, response) -> List[str]:
        return response.xpath('//ul[@class= "vi-news-tiles-list"]//li[@class= "vi-news-tiles-item "]/a/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> Optional[str]:
        script_text = response.xpath('//script[contains(text(), "document.title")]/text()').get()
        title_match = re.search(r"document\.title\s*=\s*\$.htmlDecode\('(.*?)'\);", script_text)
        return title_match.group(1)
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class= "detail-content"]/p/text()').getall())
    
    def get_images(self, response) -> List[str]:
        return []
    
    def date_format(self) -> str:
        return "%m/%d/%Y"
    
    def get_date(self, response) -> Optional[str]:
        date_time = response.xpath('//span[@class="detail-list-value"]/text()').get() 
        date_str = date_time.split()[0]
        return date_str
    
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        next_page = response.xpath('//a[contains(@class, "pg-next-button")]/@href').get()
        if next_page:
            return response.urljoin(next_page) 
        else:
            return None