from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ParliamentofBandaAcehCity(OCSpider):
    name = "ParliamentofPadangCity"
    
    country = "ID"

    start_urls_names = {
        "https://dprk.bandaacehkota.go.id/category/pimpinan-2/" : "Articles"
    }
    
    charset = "utf-8"

    @property
    def language(self): 
        return "Indonesian"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    def get_articles(self, response) -> list:
        return response.xpath("//h2[@class='entry-title']//a//@href").getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//div[@class='post-title-wrapper']//h1//text()").get().strip()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='entry-content clearfix']//p//text()").getall()) 
        
    def get_images(self, response) -> list:
        return response.xpath("//div[@class='entry-content clearfix']//p//img//@src").getall()
    
    def date_format(self) -> str:
        return "%d %B %Y"
    
    def get_date(self, response) -> str:
        return response.xpath("//div[@class='entry-meta clearfix']//span[@class='date']//text()").get().strip()
    
    def get_authors(self, response):
        return response.xpath("//div[@class='entry-meta clearfix']//span[@class='by-author author vcard']//text()").getall()
    
    def get_page_flag(self) -> bool:
        return False
     
    def get_next_page(self, response): 
        return response.xpath("//li[@class='previous']//a//@href").get()