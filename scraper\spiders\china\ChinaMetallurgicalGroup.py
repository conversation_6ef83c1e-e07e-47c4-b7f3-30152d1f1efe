from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from typing import List
import scrapy
import logging
from datetime import datetime

class ChinaMetallurgicalGroup(OCSpider):
    name = "ChinaMetallurgicalGroup"
    country = "China"
    
    start_urls_names = {
        "http://www.mcc.com.cn/xwzx_7388/lddt/": "Leadership Activities",
        "http://www.mcc.com.cn/tzzgx_7555/yjtj/": "Research Reports"
    }
    
    charset = "utf-8"
    
    @property
    def language(self):
        return "Chinese"
    
    @property
    def source_type(self) -> str:
        return "SOE"
    
    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        articles = response.xpath("//div[@class='list_con']//li/a/@href").getall()
        return [response.urljoin(url) for url in articles if url]
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        title = response.xpath("//div[@class='detail_title']/text()").get()
        return title.strip() if title else ""
    
    def get_body(self, response) -> str:
        body = response.xpath("//div[@class='detail_con']//p/text()").getall()
        if not body:
            body = response.xpath("//div[@class='TRS_Editor']//p/text()").getall()
        return body_normalization(body)
    
    def get_images(self, response) -> List[str]:
        images = response.xpath("//div[@class='detail_con']//img/@src").getall()
        return [response.urljoin(img) for img in images if img]
    
    def get_document_urls(self, response) -> List[str]:
        docs = response.xpath("//div[@class='detail_con']//a[contains(@href, '.pdf') or contains(@href, '.doc')]/@href").getall()
        return [response.urljoin(doc) for doc in docs if doc]
    
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date_str = response.xpath("//div[@class='detail_ly']/span[1]/text()").get()
        if date_str:
            # Extract date from format like "发布时间：2023-05-16"
            date_parts = date_str.split("：")
            if len(date_parts) > 1:
                return date_parts[1].strip()
        return ""
    
    def get_authors(self, response) -> List[str]:
        author = response.xpath("//div[@class='detail_ly']/span[contains(text(), '来源')]/text()").get()
        if author:
            # Extract author from format like "来源：中冶集团"
            author_parts = author.split("：")
            if len(author_parts) > 1:
                return [author_parts[1].strip()]
        return []
    
    def get_page_flag(self) -> bool:
        return True
    
    def get_next_page(self, response) -> str:
        next_page = response.xpath("//a[contains(text(), '下一页')]/@href").get()
        if next_page:
            return response.urljoin(next_page)
        return None