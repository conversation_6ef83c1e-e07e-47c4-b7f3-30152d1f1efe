from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime

class ArkansasDepartmentOfTransportationl(OCSpider):
    name = "ArkansasDepartmentOfTransportation"

    country = "US"

    start_urls_names = {
        "https://ardot.gov/news-feed/": "News Feed",
    }
        
    charset = "utf-8"

    @property
    def language(self): 
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "America/Chicago"
    
    def get_articles(self, response) -> list:
        return response.xpath('//article//h3[@class="elementor-post__title"]/a/@href').getall()
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="elementor-widget-container"]/h2/text()').get()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="elementor-widget-container"]/p/text()').getall()) 
        
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return"%m-%d-%Y"
    
    def get_date(self, response) -> str:
        date_str = (response.xpath('//div[@class="elementor-widget-container"]//li[@itemprop="datePublished"]/span/time/text()').get()).strip()
        return datetime.strptime(date_str, "%B %d, %Y").strftime("%m-%d-%Y")
            
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        next_page = response.xpath('//nav[@class="elementor-pagination"]//a[@class="page-numbers next"]/@href').get()
        if not next_page:
           return None
        else:
            return next_page