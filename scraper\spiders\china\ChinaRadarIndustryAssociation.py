from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ChinaRadarIndustryAssociation(OCSpider):
    name = "ChinaRadarIndustryAssociation"

    start_urls_names = {
        "http://www.chinaradar.org.cn/dynamic/industry/1": "industry",
        "http://www.chinaradar.org.cn/dynamic/association/1": "association",
        "http://www.chinaradar.org.cn/policy/policeRules/1": "政策新闻"
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"
    
    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        # Article links are stored in the "onclick" attributes with the format onclick="location.href='URL'"
        articles = response.xpath('//div[@class="dynamic_text lt"]//div[@class="item_title"]/@onclick').re(r"location\.href='([^']+)'")
        return articles
    
    def get_href(self, entry) -> str:
        return entry
    
    def date_format(self) -> str:
        return '%Y-%m-%d'
    
    def get_date(self, response) -> str:
        # Dates are present in two different formats, such as '%Y-%m-%dT%H:%M:%S' or '%Y-%m-%dT%H:%M'. 
        # Eg: 1. http://www.chinaradar.org.cn/dynamic/asct_info/673161738e574e2aa59479e002c65913
        # 2. http://www.chinaradar.org.cn/dynamic/asct_info/f9a5363ec8824fb9b20455e47e62fa65
        # So, standardized to the '%Y-%m-%d' format.
        date = response.xpath('//div[@class="contTitle"]//div[@class="time"]/text()').re_first(r'^\d{4}-\d{2}-\d{2}')
        return date
    
    def get_title(self, response) -> str:
        title = response.xpath('//div[@class="contTitle"]//div[@class="tit"]//text()').get()
        return title
    
    def get_authors(self, response):
        return []
    
    def get_body(self,response) -> str:
        body = response.xpath("//div[@class='contBody']//p//text()").getall()
        return body_normalization(body)
    
    def get_images(self, response) -> list:
        return response.xpath("//div[@class='contBody']//p//img/@src").getall()
    
    def get_document_urls(self, response, entry=None) -> list:
        documents = response.xpath("//div[@class='contBody']//p//a/@href").getall()
        return documents
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        next_page = response.xpath('//div[@class="pageNum"]//li/a[contains(text(), "»")]/@href').get()
        if next_page:
            next_page_url = response.urljoin(next_page)
            return next_page_url
        return None