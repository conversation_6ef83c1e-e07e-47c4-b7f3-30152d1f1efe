import logging
from typing import List, Union
import scrapy
from scraper.OCSpider import <PERSON><PERSON>pid<PERSON>
from scraper.utils.helper import body_normalization
from bs4 import BeautifulSoup
import re
from datetime import datetime

class seazen(OCSpider):
    name = "seazen"

    start_urls_names = {
     "https://www.seazen.com.cn/news/group.html": "",
       "https://www.seazen.com.cn/investor/bulletin.html": ""
    }
    
    custom_settings = {
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350,
    },
    }
    
    HEADLESS_BROWSER_WAIT_TIME = 5000
    api_start_url = {
        'https://www.seazen.com.cn/news/group.html': {
            'url': 'https://www.seazen.com.cn/news/index.aspx',
            'payload': {
                "pageid": "1",
                "pagecount": "8",
                "isCount" : "true",
                "id": "0",
                "date": "",
                "text": ""
            },
        },
        'https://www.seazen.com.cn/investor/bulletin.html': {
            'url': 'https://www.seazen.com.cn/investor/bulletin.aspx',
            'payload': {
                "pageid": "1",
                "pagecount": "8",
                "isCount" : "false",
                "id": "0",
            },
        },
    }
    
    def parse_intermediate(self, response):
        start_url = response.meta.get('start_url')
        api_data = self.api_start_url[start_url]
        api_url = api_data["url"]
        if not api_url:
            self.logger.error(f"No API configuration found for start_url: {start_url}")
        else:
            self.logger.info(f"Fetching API URL: {api_url}")
        current_page = response.meta.get("current_page", 1)
        api_data["payload"]["pageid"] = str(current_page)
        payload = api_data["payload"]
        headers = {
            "Content-Type": "application/x-www-form-urlencoded"
        }
        yield scrapy.FormRequest(
            url=api_url,
            method="POST",
            headers=headers,
            dont_filter=True,
            formdata=payload,
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_data["url"],
                "payload": payload,
                "current_page": current_page
            },
        )
        
    article_data_map ={}

    charset = "iso-8859-1"

    @property
    def source_type(self) -> str:
        return "SOE"
    
    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        articles = response.xpath('//div[@id="ui-news-list"]/a/@href').getall()
        if articles:
            return articles
        else:
            articles = []
            for article in response.xpath('//ul[@class="inv-listcon"]/li'):
                url = article.xpath('.//a/@href').get()
                title = article.xpath('.//a/text()').get()
                date_day = article.xpath('.//div[@class="inv-listdate fleft"]/h3/text()').get()
                date_month = article.xpath('.//div[@class="inv-listdate fleft"]/p/text()').get()
                date = f"{date_month}{date_day}日" if date_day and date_month else None
                if url and title and date:
                    full_url = url
                    title = title.strip()
                    if '.pdf' in full_url.lower():
                        pdf = full_url
                    else:
                        pdf = "None"
                    clean_date = date.strip()
                    self.article_data_map[full_url] = {"title": title, "date": clean_date, "pdf": pdf}
                    articles.append(full_url) 
            return articles

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        if ".pdf" in response.url.lower():
            return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
        return response.xpath('//dd[@class="width100 fright"]/h1/text()').get()  
        
    def get_body(self, response) -> str:
        if ".pdf" in response.url.lower():
            return ""
        return body_normalization( response.xpath('//div[@class="nd-conright area_80 mo_margintop20"]//p//text()').getall())
    
    def get_images(self, response) -> List[str]:
        if ".pdf" in response.url.lower():
            return ""
        return response.xpath('//section[@class="container paddingtop50 paddingbottom50 wow fadeInUp animated"]//div[@class="nd-conright area_80 mo_margintop20"]//img/@src').getall()
       
    
    def date_format(self) -> str:
        return '%Y-%m-%d'

    def get_date(self, response) -> str:
        if ".pdf" in response.url.lower():
            # Get from article_data_map
            date_str = self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
            date_str = date_str.strip().replace("\n", "").replace(" ", "")

            # Match "2025年05月01日"
            match = re.match(r"(\d{4})年(\d{1,2})月(\d{1,2})日", date_str)
            if match:
                year, month, day = match.groups()
                return f"{year}-{int(month):02d}-{int(day):02d}"

            # Fallback for earlier structure: "30\n2025年04月"
            match_alt = re.match(r"(\d{1,2})(\d{4})年(\d{1,2})月", date_str)
            if match_alt:
                day, year, month = match_alt.groups()
                return f"{year}-{int(month):02d}-{int(day):02d}"

            self.logger.warning(f"Unrecognized PDF date format: '{date_str}' for {response.url}")
            return ""
        
        else:
            # Non-PDF structure from DOM
            day = response.xpath("//div[@class='news-date']/p[@class='news-day']/text()").get()
            year_month = response.xpath("//div[@class='news-date']/p[@class='news-month']/text()").get()
            if day and year_month:
                match = re.match(r"(\d{4})/(\d{1,2})", year_month.strip())
                if match:
                    year, month = match.groups()
                    return f"{year}-{int(month):02d}-{int(day):02d}"

            self.logger.warning(f"Missing or invalid non-PDF date at {response.url}")
            return ""


            
    def get_authors(self, response):
        return []
    
    def get_document_urls(self, response, entry=None) -> list:
        pdf_url = self.article_data_map.get(response.request.meta.get('entry'), {}).get("pdf")
        return [pdf_url] if pdf_url and pdf_url != "None" else []
    
    def get_next_page(self, response, current_page) -> Union[None, str]:
        
        
        data = response.text
        soup = BeautifulSoup(data, 'html.parser')
        hrefs = [a['href'] for a in soup.find_all('a', href=True)]
        if hrefs:
            return current_page + 1
        else:
            return None
        
    def get_page_flag(self) -> bool:
        return True

    def go_to_next_page(self, response, start_url, current_page = 1):
        api_url = response.meta.get("api_url")
        if not api_url: 
            logging.info("API URL not found in meta data.")
            return
        next_page = self.get_next_page(response, current_page)
        print("THE VALUE OF NEXT PAGE IS:", next_page)
        if next_page:
            yield scrapy.Request(
                url=api_url,
                callback=self.parse_intermediate,
                meta={'current_page': next_page, 'start_url': start_url, 'api_url': api_url}
            )
        else:
            logging.info("No more pages to fetch.")
            yield None
    