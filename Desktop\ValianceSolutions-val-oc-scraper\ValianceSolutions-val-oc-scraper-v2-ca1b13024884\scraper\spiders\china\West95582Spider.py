from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
from urllib.parse import urljoin
import re
from datetime import datetime
import pytz

class West95582Spider(OCSpider):
    name = "West95582Spider"
    country = "CN"

    start_urls_names = {
        "https://www.west95582.com/jdw/public/infoList.jsp?classid=0001000200270001": "West95582 Company Announcements",
        "https://www.west95582.com/jdw/public/infoList.jsp?classid=0001000200270002": "West95582 Investor Relations",
        "https://www.west95582.com/jdw/public/infoList.jsp?classid=0001000200260001": "West95582 Company Overview",
    }
    charset = "utf-8"

    # Custom settings for better performance
    custom_settings = {
        "DOWNLOAD_DELAY": 1,
        "CONCURRENT_REQUESTS_PER_DOMAIN": 2,
    }

    @property
    def source_type(self) -> str:
        return "Corporate"

    @property
    def language(self) -> str:
        return "Chinese"

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def parse_intermediate(self, response):
        """Optional method for headless mode if needed"""
        return self.parse(response)

    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="txtmain"]').getall()
  

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response, entry=None) -> str:
        # Try multiple XPath patterns for titles
        title_xpaths = ('//p[@class="MsoNormal"]','//title/text()')

    def get_body(self, response, entry=None) -> str:
        # Try multiple XPath patterns for body content
        body_xpaths = ('//div[@class="aboutusdis"]')

    def get_images(self, response) -> list:
        images = response.xpath("//img/@src").getall()
        return [response.urljoin(img) for img in images] if images else []

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response, entry=None) -> str:
        # Try multiple XPath patterns for dates
        date_xpaths = ( '//div[@class="timeStamp"]')

    def get_document_urls(self, response, entry=None) -> list:
        # Look for document links in the page
        doc_patterns = [
            '//a[contains(@href, ".pdf")]/@href',
            '//a[contains(@href, ".doc")]/@href',
            '//a[contains(@href, ".docx")]/@href',
            '//a[contains(@href, ".xls")]/@href',
            '//a[contains(@href, ".xlsx")]/@href',
            '//a[contains(@href, ".ppt")]/@href',
            '//a[contains(@href, ".pptx")]/@href',
            '//a[contains(@href, ".zip")]/@href',
            '//a[contains(@href, ".rar")]/@href',
            '//a[contains(@href, "download")]/@href',
            '//a[contains(text(), "下载")]/@href',
            '//a[contains(text(), "附件")]/@href'
        ]

        doc_urls = []
        for pattern in doc_patterns:
            urls = response.xpath(pattern).getall()
            if urls:
                doc_urls.extend(urls)

        # Remove duplicates and convert to absolute URLs
        return [urljoin(response.url, doc) for doc in set(doc_urls)]
    
    def get_next_page(self, response) -> str:
        next_page = response.xpath('//div[@class="pageturning"]//ul//li').get()
        if next_page:
            next_page_url = response.urljoin(next_page)
            return next_page_url
        return None
    

    def get_page_flag(self) -> bool:
        return True

    def get_meta(self, response, entry=None) -> list:
        return []

    def get_pdf(self, response, entry=None):
        return None
