from scraper.OCSpider import OCSpider

class ArizonaDepartmentOfEconomicSecurity(OCSpider):
    name = 'ArizonaDepartmentOfEconomicSecurity'
    
    country = "US"

    start_urls_names = {
        'https://des.az.gov/news-release': 'News'
    }
    
    custom_settings = {
        "DOWNLOADER_MIDDLEWARES" :
        {
            'scraper.middlewares.HeadlessBrowserProxy': 350,
        },
        "DOWNLOAD_DELAY" : 3
    }
    
    HEADLESS_BROWSER_WAIT_TIME = 30000 # 30 Seconds wait time
    
    charset = "iso-8859-1"
    
    article_data_map = {}  # Mapping title, date and PDf url with respective child articles

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Central"
    
    def get_articles(self, response) -> list:
        # Extract articles from function
        self.extract_articles_with_dates(response)
        return [response.urljoin(link) for link in response.xpath('//tbody/tr//td[@class= "views-field views-field-body"]/p/a/@href').getall()]
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map[response.request.meta.get('entry')].get("title")
    
    def get_body(self, response) -> str:
        # Only PDF's are there to scrape
        return ""
    
    def get_images(self, response) -> list[str]:
        # Only PDF's are there to scrape
        return []
    
    def date_format(self) -> str:
        return "%B %d, %Y"
    
    def get_date(self, response) -> str:
        return self.article_data_map[response.request.meta.get('entry')].get("date")
    
    def get_authors(self, response):
        return []
    
    def get_document_urls(self, response, entry=None):
        return self.article_data_map[response.request.meta.get('entry')].get("pdf")

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        return ""
    
    def extract_articles_with_dates(self, response):
        # Function to extract dates of respective articles from start URL
        mapping = {}
        for article in response.xpath('//tbody/tr'):
            url = article.xpath('.//td[@class= "views-field views-field-body"]/p/a/@href').get()
            title = article.xpath('.//td[@class= "views-field views-field-body"]/p/a/text()').get()
            date = article.xpath('./td/span/text()').get()
            if url and title and date:
                full_url = response.urljoin(url.strip())
                clean_date=date.strip()
                mapping[full_url] = {"title": title.strip(), "date": clean_date, "pdf": [full_url]}
            self.article_data_map.update(mapping)
        return self.article_data_map