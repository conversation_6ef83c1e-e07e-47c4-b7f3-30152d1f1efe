from typing import List, Optional
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
from datetime import datetime

class FloridaDepartmentOfEconomicOpportunity(OCSpider):
    name = "FloridaDepartmentOfEconomicOpportunity"
    
    country  = "US"

    start_urls_names = {
        "https://www.floridajobs.org/news-center/DEO-Press": "News Releases",
    }
    
    custom_settings = {
		"DOWNLOADER_MIDDLEWARES" : {
			'scraper.middlewares.HeadlessBrowserProxy': 350,
		},
		"DOWNLOAD_DELAY" : 5
	}
    
    HEADLESS_BROWSER_WAIT_TIME = 40000  # 40 Seconds wait time
    
    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Central"
    
    def get_articles(self, response) -> List[str]:
        return response.xpath('//div[contains(@class, "rightNewsBox")]//h2[@class="sfnewsTitle"]/a/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//h1[contains(@class, 'sfnewsTitle')]/text()").get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='sfnewsContent sfcontent']").getall())
    
    def get_images(self, response) -> List[str]:
        return []
    
    def date_format(self) -> str:
        return "%m/%d/%Y"
    
    def get_date(self, response) -> Optional[str]:
        date_texts = response.xpath('//div[@class="sfnewsAuthorAndDate sfmetainfo"]/text()').getall()
        date_texts = [text.strip() for text in date_texts if text.strip()]
        if date_texts:
            date_time = date_texts[0]
            try:
                parsed_date = datetime.strptime(date_time, "%b %d, %Y")
                return parsed_date.strftime(self.date_format())
            except ValueError:
                return None
        return None
                    
    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        next_page = response.xpath('//div[contains(@class, "sf_pagerNumeric")]/a[contains(@class, "sf_PagerNextGroup")]/@href').get()
        if next_page:
            return response.urljoin(next_page)
        else:
            return None