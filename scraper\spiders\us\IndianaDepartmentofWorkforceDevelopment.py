from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
from datetime import datetime
import scrapy
import re
from typing import Optional

class IndianaDepartmentOfWorkforceDevelopment(OCSpider):
    name = "IndianaDepartmentOfWorkforceDevelopment"
    country = "US"

    custom_settings = {
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter'
    }

    start_urls_names = {
        "https://www.in.gov/dwd/newsroom/news-and-articles/": "Newsroom",
    }
    
    def parse_intermediate(self, response):
        all_articles = list(set(response.xpath("//div[contains(@class, 'columns')]/p/a/@href").getall()))
        total_articles = len(all_articles)
        articles_per_page = 100
        start_url = response.meta.get("start_url", response.url)
        for start_idx in range(0, total_articles, articles_per_page):
            yield scrapy.Request(
                url=start_url,
                callback=self.parse,
                meta={
                    'start_idx': start_idx, 
                    'articles': all_articles, 
                    'start_url': start_url
                },
                dont_filter=False
            )
    
    charset = "utf-8"

    @property
    def language(self):
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self):
        return "America/Chicago"

    def get_articles(self, response):
        all_articles = response.meta.get('articles', [])
        start_idx = response.meta.get('start_idx', 0)
        end_idx = start_idx + 100
        return all_articles[start_idx:end_idx]
        
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//h1//text()').get().strip()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//section//p//text()').getall())

    def get_images(self, response) -> list:
        return []

    def get_authors(self, response):
        return []

    def get_date(self, response) -> Optional[str]:
        paragraphs = response.xpath('//p//text()').getall()
        text = " ".join(paragraphs).strip()
        date_match = re.search(r'\b([A-Z][a-z]+\.? \d{1,2},? \d{4})\b', text)
        if date_match:
            date_str = date_match.group(1).replace(".", "").replace(",", "")
            try:
                return datetime.strptime(date_str, "%B %d %Y").strftime("%m-%d-%Y")
            except ValueError:
                try:
                    month_abbrs = {
                        "Jan": "Jan", "Feb": "Feb", "Mar": "Mar", "Apr": "Apr",
                        "May": "May", "Jun": "Jun", "Jul": "Jul", "Aug": "Aug",
                        "Sept": "Sep", "Sep": "Sep", "Oct": "Oct", "Nov": "Nov", "Dec": "Dec"
                    }
                    for wrong, correct in month_abbrs.items():
                        if date_str.startswith(wrong):
                            date_str = date_str.replace(wrong, correct, 1)
                            break
                    return datetime.strptime(date_str, "%b %d %Y").strftime("%m-%d-%Y")
                except ValueError:
                    return None
        return None

    def date_format(self) -> str:
        return "%m-%d-%Y"

    def get_authors(self, response):
        return []
    
    def get_document_urls(self, response, entry=None):
        return response.xpath("//section//p//a[contains(@href,'.pdf')]//@href").getall()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        return None