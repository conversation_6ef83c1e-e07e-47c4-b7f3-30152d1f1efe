import re
from scraper.OfficialLineSpider import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, configure_ol_spider



class cctv(OfficialLineSpider):
    
    name = "cctv"

    source = "央视网" #cctv

    custom_settings = configure_ol_spider()

    start_urls_names = {
        "http://tv.cctv.com/lm/xwlb/day/%s.shtml":"央视网"
    }

    def __init__(self, name=None, **kwargs):
        super().__init__(name, **kwargs)
        self.date_flag = True

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_start_url(self, url, date):
        return url % (date.strftime('%Y%m%d'))   

    def get_articles(self, response) -> list:
        return response.xpath("/html/body/li/a/@href").extract()

    def get_href(self, entry) -> str:
        if "news.cntv.cn" in entry:
                entry = entry.replace("news.cntv.cn","tv.cctv.com")
        return entry

    def get_title(self, response) -> str:
        return " ".join(response.xpath("//title//text()").extract()).strip()

    def get_subhead(self, response) ->str:
        return " ".join(response.xpath("//div[@class='con']//li[1]/p//text()").extract()).strip()

    def get_body(self, response) -> str:
        return "\n".join(response.xpath("//div[@class='content_area']/p//text()").extract()).replace("\t", "").replace("\r", "")

    def date_format(self) -> str:
        return '%Y-%m-%d %H:%M'

    def get_date(self, response) -> str:
        date = response.xpath('//span[@class="time"]//text()').get()
        return date

    def get_images(self, response) -> list:
        pic_l = []
        return pic_l

    def get_authors(self, response):
        author = response.xpath("//span[@id='zb']//text()").get()
        return "".join(re.findall("：(.*)", author))

    