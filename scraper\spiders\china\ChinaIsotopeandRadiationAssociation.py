from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization

class ChinaIsotopeAndRadiationAssociation(OCSpider):
    name = "ChinaIsotopeAndRadiationAssociation"

    proxy_country = "cn"

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES":
            {
                #  For using geo targeted proxy, add this middleware
                'scraper.middlewares.GeoProxyMiddleware': 350,
            },
        "DOWNLOAD_DELAY": 2,
    }

    start_urls_names = {
        'https://www.cira.net.cn/news-246.html': '通知公告',
        'https://www.cira.net.cn/news-242.html': '协会动态',
        'https://www.cira.net.cn/news-247.html': '行业资讯',
        'https://www.cira.net.cn/party-1.html': '党建强会'
    }

    @property
    def source_type(self) -> str:
        return 'IndustryAssociation'

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_articles(self, response) -> list:
        return response.xpath("//*[@class='newslist' or contains(@class, 'notice')]//a/@href").getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//*[@class='news-detail-title']//text()").get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//*[@class="news-detail-cont"]/p//text()').getall())

    def get_images(self, response) -> list:
        return [response.urljoin(url) for url in response.xpath('//*[@class="news-detail-cont"]//img/@src').getall()]
    
    def get_document_urls(self, response, entry=None):
        return [response.urljoin(url) for url in response.xpath('//*[@class="news-detail-cont"]//a/@href').getall()]

    def get_authors(self, response):
        return []

    def date_format(self) -> str:
        return '%Y-%m-%d'

    def get_date(self, response) -> str:
        return response.xpath('//*[@class="news-detail-desc"]/p[@class="time"]/text()').re_first(r"\d{4}-\d{2}-\d{2}")

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        try:
            next_page = response.xpath("//*[contains(@class,'page')]//a[contains(text(),'下一页')]/@href").get()
            if next_page:
                next_page_url = response.urljoin(next_page)
                if next_page_url == response.url:
                    return None
                else:
                    return next_page_url
            else:
                return None
        except Exception as e:
            self.logger.error(f"Error extracting next page link from page {response.url}: {e}")
            return None