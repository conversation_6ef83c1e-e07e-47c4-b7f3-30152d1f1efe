from typing import Optional
from scraper.OCSpider import <PERSON><PERSON>pider
from scraper.utils.helper import body_normalization
from datetime import datetime
import urllib.parse
import scrapy
import json
from urllib.parse import urlencode

class EnterpriseFlorida(OCSpider):
    name = "EnterpriseFlorida"

    country = "US"

    start_urls_names = {
        "https://selectflorida.org/news-resources/": "News and Media",
        }
    
    api_start_urls = {
        "https://selectflorida.org/news-resources/": {
            "url": "https://selectflorida.org/wp-json/wp/v2/posts?per_page=12&page=1",
            "payload": {
                "per_page":12,
                "page": 1                
}
        }
    }

    custom_settings = {
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36"
    }

    charset = "utf-8"

    @property
    def language(self): 
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "America/New_York"
    
    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            return
        payload = response.meta.get("payload", api_data["payload"].copy())
        api_url = api_data["url"]
        full_api_url = f"{api_url}?{urllib.parse.urlencode(payload)}"
        
        yield scrapy.Request(
            url=full_api_url,
            method="GET",
            headers=self.headers,
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "payload": payload,
                "current_page": payload["page"]
            }
        )

    def get_articles(self, response) -> list:
            data = json.loads(response.text)
            article_urls = [article.get("link") for article in data if "link" in article]
            return article_urls
      
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h2[@class="mb-3 leading-none text-1.75 md:text-2 xl:text-2.5"]/text()').get().strip()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="overview-content news-content"]//p//text()').getall()) 
        
    def get_images(self, response) -> list:
        return response.xpath('//img[@class="w-full h-auto my-4"]/@src').get()
        
    def date_format(self) -> str:
        return"%m-%d-%Y"
    
    def get_date(self, response) -> str:
        date_str = response.xpath('//span[@class="mb-1 text-sm uppercase font-bold font-antonio text-blue-500 tracking-wider md:text-base xl:text-1.375 xl:mb-2"]/text()').get().strip()
        return datetime.strptime(date_str, "%B %d, %Y").strftime("%m-%d-%Y") 
        
    def get_authors(self, response):
        return []
    
    def get_next_page(self, response, current_page: Optional[int] = 1) -> Optional[int]:
        data = json.loads(response.text)
        if len(data) == 0:
            return None  
        return current_page + 1
        
    def get_page_flag(self) -> bool:
        return False
    
    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        api_data = self.api_start_urls.get(start_url)
        api_url = api_data["url"]
        payload = response.meta.get("payload", {}).copy()
        next_page = self.get_next_page(response, current_page)
        if not next_page:
            return
        payload["page"] = next_page
        query_string = urlencode(payload)
        full_api_url = f"{api_url}?{query_string}"
        yield scrapy.Request(
            url=full_api_url,
            method="GET",
            headers=self.headers,
            callback=self.parse_intermediate,
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "payload": payload,
                "current_page": next_page
            },
        )

