from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime
from urllib.parse import urljoin
import scrapy

class ArizonaCommerceAuthority(OCSpider):
    name = "ArizonaCommerceAuthority"
    country = "US"

    start_urls_names = {
        "https://www.azcommerce.com/news-events/news/?categories=": "News Feed",
    }

    charset = "utf-8"
    article_date_map = {}

    custom_settings = {
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    @property
    def language(self):
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self):
        return "America/Phoenix"

    def parse_intermediate(self, response):
        base_url = response.url
        articles = response.xpath('//div[contains(@class, "news__wrapper")]')
        all_articles = []

        for article in articles:
            date = article.xpath('.//div[contains(@class, "date")]/text()').get()
            href = article.xpath('.//a[contains(@class, "black")]/@href').get()

            if href:
                full_url = urljoin(base_url, href.strip()).rstrip('/')
                all_articles.append(full_url)

                if date:
                    self.article_date_map[full_url] = date.strip()

        all_articles = list(set(all_articles))  # Remove duplicates
        total_articles = len(all_articles)
        articles_per_page = 100
        start_url = response.meta.get("start_url", response.url)

        for start_idx in range(0, total_articles, articles_per_page):
            chunk = all_articles[start_idx:start_idx + articles_per_page]

            for article_url in chunk:
                self.crawler.stats.inc_value("articles_crawled") 
                yield scrapy.Request(
                    url=article_url,
                    callback=self.parse_article,
                    meta={
                        'start_url': start_url,
                        'articles': all_articles,
                        'start_idx': start_idx
                    },
                    dont_filter=True
                )

    def get_articles(self, response) -> list:
        all_articles = response.meta.get('articles') or []
        start_idx = response.meta.get('start_idx', 0)
        end_idx = start_idx + 100
        return all_articles[start_idx:end_idx]

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//div[@class="contain text-left blue moveTitle"]//h1/span/text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="mainContent col-xs-12 center"]//p//text()').getall())

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%B %d, %Y"

    def get_date(self, response):
        article_url = response.url.rstrip('/')
        return self.article_date_map.get(article_url)

    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> Optional[str]:
        return None
