from datetime import datetime
from typing import Optional
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization

class ChinaLongsheng(OCSpider):
    name = "ChinaLongsheng"

    start_urls_names = {
        "https://www.longsheng.com/news": "News",
        "https://www.longsheng.com/notice": "Notice",
        "https://www.longsheng.com/periodic": "Periodic"
        
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"
    
    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        articles =  response.xpath('//div[@class="contentCon newsCon"]//ul/li/a/@href').getall()
        if articles:
            return articles
        else:
            return response.xpath('//div[@class="contentCon noticeCon"]//ul/li/a/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        title = response.xpath('//div[@class="contentCon newsCon"]//h4//text()').get()
        if title:
            return title
        else:
            return response.xpath('//div[@class="contentCon newsCon"]//table//tbody//tr/td//a//text()').get() 
    
    def get_body(self, response) -> str:
        body =  body_normalization(response.xpath('//div[@class="contentCon newsCon"]//p//text()').getall())
        if body:
            return body
        else: 
            return ""
    
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return '%Y.%m.%d'
    
    def get_date(self, response) -> str:
        date_str = response.xpath('//div[@class="contentCon newsCon"]//h4//em/text()').get()
        if not date_str:
            date_str = response.xpath('//div[@class="contentCon newsCon"]//table//tbody//tr[2]/td[2]//text()').get()
        date_str = date_str.strip()
        formats_to_try = [
            '%Y.%m.%d',
            '%Y-%m-%d',  
            '%d.%m.%Y',  
            '%d-%m-%Y',  
            '%Y/%m/%d', 
        ]
        for fmt in formats_to_try:
            try:
                date_obj = datetime.strptime(date_str, fmt)
                return date_obj.strftime('%Y.%m.%d')
            except ValueError:
                continue
        return date_str

    def get_authors(self, response):
        return []
    
    def get_document_urls(self, response, entry=None) -> list:
        return response.xpath('//div[@class="contentCon newsCon"]//table//tbody//tr/td//a/@href').get()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]: 
        next_page = response.xpath('//div[@class="pagination"]/a[contains(text(), "下一页")]/@href').get()
        if next_page:
            return response.urljoin(next_page)
        return None