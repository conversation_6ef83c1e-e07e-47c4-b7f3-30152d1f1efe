import re
from scraper.OCSpider import <PERSON><PERSON>pider
from scraper.utils.helper import body_normalization
from typing import List
import scrapy

class MarylandDepartmentofCommerce(OCSpider):
    name = 'MarylandDepartmentofCommerce'

    country = "US"
    
    start_urls_names = {
        'https://commerce.maryland.gov/media/press-room': "Press",
        'https://commerce.maryland.gov/media/press-archives':"Press"
    }

    HEADLESS_BROWSER_WAIT_TIME = 30000  # 30 Seconds wait time
    
    custom_settings = {
		"DOWNLOADER_MIDDLEWARES" : {
			'scraper.middlewares.HeadlessBrowserProxy': 350,
		},
		"DOWNLOAD_DELAY" : 5,
	}

    def parse_intermediate(self, response):
        all_articles = list(set(response.xpath("//div[@class='ms-rte-embedcode ms-rte-embedwp']//tr//td//a//@href").getall()))
        total_articles = len(all_articles)
        articles_per_page = 100
        start_url = response.meta.get("start_url", response.url)
        for start_idx in range(0, total_articles, articles_per_page):
            yield scrapy.Request(
                url=start_url,
                callback=self.parse,
                meta={
                    'start_idx': start_idx, 
                    'articles': all_articles, 
                    'start_url': start_url
                },
                dont_filter=True
            )
            
    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Central"

    def get_articles(self, response) -> list:
        all_articles = response.meta.get('articles', [])
        start_idx = response.meta.get('start_idx', 0)
        end_idx = start_idx + 100
        return all_articles[start_idx:end_idx]

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//div[@data-name='Page Field: Title']//text()").get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@data-name='Page Field: Page Content']//text()").getall())
    
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return '%B %d, %Y'

    def get_date(self, response) -> str:
        date = response.xpath("//div[@data-name='Page Field: Page Content']//div//strong//text()").get()
        match = re.search(r"\((.*?)\)", date)
        if match:
            extracted_text = match.group(1)  # Extracted content
            return extracted_text
    
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None) -> List[str]:
        return ""

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        # No more pages to scrape
        return None