import logging
from typing import Optional
import scrapy
from scraper.OCSpider import <PERSON><PERSON>pid<PERSON>
from scraper.utils.helper import body_normalization
from datetime import datetime

class CenterForClimateAndEnergySolutions(OCSpider):
    name = 'CenterForClimateAndEnergySolutions'

    country = "US"

    start_urls_names = {
        'https://www.c2es.org/newsroom/': 'News'
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"
    
    @property
    def timezone(self):
        return "US/Central"
    
    article_date_map = {}

    def get_articles(self, response) -> list:
        self.extract_articles_with_dates(response)
        return [response.urljoin(link) for link in response.xpath('//div[@class= "post-content"]//h5/a/@href').getall()]
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self,response) -> str:
        return response.xpath('//div[@class= "slide-text"]/h2/text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="section-text"]//p//text()').getall())
    
    def get_images(self, response) -> list[str]:
        return []
    
    def date_format(self) -> str:
        return "%B %d, %Y"
    
    def get_date(self, response) -> Optional[str]:
        article_url = response.url
        raw_date = self.article_date_map.get(article_url)
        if raw_date:
            try:
                date_obj = datetime.strptime(raw_date.strip(), self.date_format())
                return date_obj.strftime("%B %d, %Y")
            except Exception as e:
                self.logger.error(f"Error parsing date {raw_date}: {e}")
        return None
            
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response, current_page):
        if response.status != 200:
            return None
        else:
            return str(int(current_page) + 1)
    
    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        next_page = self.get_next_page(response, current_page)
        url=f"https://www.c2es.org/newsroom/?show_page={next_page}"
        if next_page:
            yield scrapy.Request(
                url=url,
                method='GET',
                callback=self.parse,
                dont_filter=True,
                 meta={
                        'current_page': next_page,
                        'start_url': start_url,
                    }
            )
        else:
            logging.info("No more pages to fetch.")
            yield None

    def extract_articles_with_dates(self, response):
        # Mapping child articles with dates from start URL
        for article in response.xpath('//div[@class="post-content"]'):
            url = response.urljoin(article.xpath('.//h5/a/@href').get())
            date = article.xpath('.//p[@class="post-date"]/span/text()').get()
            if url and date:
                self.article_date_map[url] = date