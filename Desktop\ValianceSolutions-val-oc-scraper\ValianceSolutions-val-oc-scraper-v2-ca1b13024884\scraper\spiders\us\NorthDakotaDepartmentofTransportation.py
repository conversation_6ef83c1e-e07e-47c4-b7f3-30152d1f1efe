from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime

class NorthDakotaDepartmentOfTransportation(OCSpider):
    name = 'NorthDakotaDepartmentOfTransportation'

    country = "US"
    
    start_urls_names = {
        'https://www.dot.nd.gov/news': "News"
    }
            
    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Central"

    def get_articles(self, response) -> list:
        return response.xpath("//h2//a//@href").getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//h1//text()").get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='content-body']//p//text()").getall())
    
    def get_images(self, response) -> list:
        return ""
    
    def date_format(self) -> str:
        return '%Y-%m-%d %H:%M:%S'

    def get_date(self, response) -> str:
        date_string = response.xpath("//div[@class='content']//span[@class='news-date']//text()").get(default="").strip()
        if date_string:
            possible_formats = [
                "%A, %B %d, %Y - %I:%M%p",  
                "%A, %B %d, %Y - %I:%M %p",
                "%A, %B %d, %Y"
            ]
            for fmt in possible_formats:
                try:
                    parsed_date = datetime.strptime(date_string, fmt)
                    return parsed_date.strftime('%Y-%m-%d %H:%M:%S')  
                except ValueError:
                    continue
        return None
    
    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        next_page = response.xpath("//li[@class='page-item pager__item--next']//a//@href").get()
        if next_page:
            return next_page
        else:
            return None