from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class WilsonCenter(OCSpider):
    name = "WilsonCenter"
    
    country = "US"

    start_urls_names = {
        "https://www.wilsoncenter.org/events?_page=1&keywords=&_tab=past-events&_limit=10": "Press Releases",
    }

    charset = "utf-8"

    @property
    def language(self):
        return "English"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "US/Eastern"

    def get_articles(self, response) -> list:
        return response.xpath("//div[@class='main']//h2//a//@href").getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//div[@class='hero-event-detail-main']//h1//text()").get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='wysiwyg-content -full']//text()").getall())

    def get_images(self, response, entry=None):
        return response.xpath("//div[@class='figureCaption-media']//img//@src").getall()

    def get_authors(self, response):
        return ""

    def date_format(self) -> str:
        return "%B %d, %Y"

    def get_date(self, response) -> str:
        return response.xpath("//time[@class='item-date']//text()").get()

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        url = response.url
        current_page = int(url.split("_page=")[1].split("&")[0])
        next_page = current_page + 1
        if not self.get_articles(response):
            return None
        next_url = url.replace(f"_page={current_page}", f"_page={next_page}")
        return next_url