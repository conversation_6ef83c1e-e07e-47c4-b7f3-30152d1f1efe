from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ChinaAssociationForBarCodeTechnologyAndApplication(OCSpider):
    name = 'ChinaAssociationForBarCodeTechnologyAndApplication'

    custom_settings = { 
		"DOWNLOADER_MIDDLEWARES" : {
			'scraper.middlewares.HeadlessBrowserProxy': 350,
		},
		"DOWNLOAD_DELAY" : 2,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter'
	}

    HEADLESS_BROWSER_WAIT_TIME = 30000   # 30 seconds

    start_urls_names = {
        "http://cabc.net.cn/News/Association.shtml": "协会新闻",
        "http://cabc.net.cn/News/Member.shtml": "会员新闻",   # No pagination
        "http://cabc.net.cn/News/Notice.shtml": "通知公告",
        "http://cabc.net.cn/Standards/Release.shtml": "协会发布",   # No pagination   
    }

    charset = "utf-8"
    
    @property
    def source_type(self) -> str:
        return 'IndustryAssociation'

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_articles(self, response) -> list:
        articles = response.xpath('//div[@class="list_3"]//li/a/@href').getall()   # For articles under 2nd start url (Example: http://cabc.net.cn/News/Association.shtml)
        if not articles:
            articles = response.xpath('//div[@class="list_2"]//li/a/@href').getall()   # For articles under 2nd start url (Example: http://cabc.net.cn/News/Member.shtml)
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//div[@class="context_1"]/h1/text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="con"]/p//text()').getall())

    def date_format(self) -> str:
        return '%Y年%m月%d'

    def get_date(self, response) -> str:
        return response.xpath('//div[@class="des"]/div[@class="txt"]//text()').re_first(r'(\d{4}年\d{2}月\d{2})')
        
    def get_images(self, response) -> list:
        return response.xpath('//div[@class="con"]//img/@src').getall()
 
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        next_page = response.xpath('//ul[@class="pagination"]//li/a[contains(text(), ">")]/@href').get()
        if next_page:
            return response.urljoin(next_page)
        else:
            return None