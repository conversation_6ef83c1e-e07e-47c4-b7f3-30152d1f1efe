from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime 

class UtahAttorneyGeneral(OCSpider):
    name = "UtahAttorneyGeneral"
    
    country = "US"

    start_urls_names = {
        "https://attorneygeneral.utah.gov/news": "News Room",
    }

    charset = "utf-8"
    
    article_date_map = {}  # Mapping articles and date from start URL

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "America/Denver"
    
    def get_articles(self, response) -> list:
        self.extract_articles_with_dates(response)  # Extracting articles and date mapped
        return [response.urljoin(link) for link in response.xpath("//div[@class='news-posts']/a/@href").getall()]
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="the-post"]/h1/text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="entry"]//p//text()').getall())

    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return"%m/%d/%Y"
    
    def get_date(self, response) -> str:
        date_str = self.article_date_map.get(response.url)
        date_obj = datetime.strptime(date_str, "%B %d, %Y")  
        return date_obj.strftime("%m/%d/%Y") 
        
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]: 
        next_page = response.xpath('//div[@class="next-posts"]/a/@href').get()
        if next_page:
            return response.urljoin(next_page)
        else: 
            return None 
    
    def extract_articles_with_dates(self, response):
        for article in response.xpath("//div[@class='news-posts']/a"):
            url = article.xpath("./@href").get()
            date = article.xpath(".//span[@class='postDate']/text()").get()  
            if url and date:
                self.article_date_map[response.urljoin(url)] = date.strip()
        return self.article_date_map 