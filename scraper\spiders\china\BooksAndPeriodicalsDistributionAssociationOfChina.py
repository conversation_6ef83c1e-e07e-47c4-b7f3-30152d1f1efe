from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class BooksAndPeriodicalsDistributionAssociationOfChina(OCSpider):
    name = "BooksAndPeriodicalsDistributionAssociationOfChina"

    start_urls_names = {
        "http://cn-faxie.org.cn/notice.html?id=17": "通知公告",
        "http://cn-faxie.org.cn/list.html?id=18": "协会活动",
        "http://cn-faxie.org.cn/list.html?id=19": "行业资讯",
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"
    
    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        return response.xpath('//ul[@class="textNews"]//li/a/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//div[@class='tit']//h4/text()").get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="detailBox"]//p//text()').getall())
    
    def get_images(self, response) -> list:
        return [
            response.urljoin(url)
            for url in
            response.xpath('//div[@class="detailBox"]//img/@src').getall()
        ]
    
    def get_document_urls(self, response, entry=None) -> list:
        return [
            response.urljoin(url)
            for url in
            response.xpath('//div[@class="detailBox"]//p//a/@href').getall()
        ]
    
    def date_format(self) -> str:
        return"%Y-%m-%d"
    
    def get_date(self, response) -> str:
        return response.xpath('//div[@class="tit"]//span[1]/text()').re_first(r"\d{4}-\d{2}-\d{2}")
    
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]: 
        next_page = response.xpath('//a[@class="downPage"]/@href').get()
        if next_page:
            return response.urljoin(next_page)
        return None