from google.cloud import bigquery

default_schema = [
        bigquery.SchemaField("id", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("source", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("source_name", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("source_type", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("url", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("title", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("body", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("images", "STRING", mode="REPEATED"),
        bigquery.SchemaField("authors", "STRING", mode="REPEATED"),
        bigquery.SchemaField("raw_html", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("date", "TIMESTAMP", mode="REQUIRED"),
        bigquery.SchemaField("insert_timestamp", "TIMESTAMP", mode="REQUIRED"),
        bigquery.SchemaField(
            "oc",
            "RECORD",
            mode="REQUIRED",
            fields=[
                bigquery.SchemaField(
                    "segments",
                    "RECORD",
                    mode="REPEATED",
                    fields=[
                        bigquery.SchemaField("segment", "STRING", mode="NULLABLE"),
                        bigquery.SchemaField("pos", "STRING", mode="NULLABLE")
                    ]),
                bigquery.SchemaField(
                    "sentiment_avg",
                    "RECORD",
                    mode="REQUIRED",
                    fields=[
                        bigquery.SchemaField("positive_prob", "NUMERIC", mode="NULLABLE"),
                        bigquery.SchemaField("negative_prob", "NUMERIC", mode="NULLABLE"),
                        bigquery.SchemaField("error_msg", "STRING", mode="NULLABLE")
                    ]),
                bigquery.SchemaField(
                    "tags",
                    "RECORD",
                    mode="REPEATED",
                    fields=[
                        bigquery.SchemaField("score", "NUMERIC", mode="NULLABLE"),
                        bigquery.SchemaField("tag", "STRING", mode="NULLABLE"),
                        bigquery.SchemaField("tag_en", "STRING", mode="NULLABLE"),
                        bigquery.SchemaField("error_msg", "STRING", mode="NULLABLE"),
                        bigquery.SchemaField("error_msg_translation", "STRING", mode="NULLABLE"),
                    ]),
                bigquery.SchemaField(
                    "summary",
                    "RECORD",
                    mode="REQUIRED",
                    fields=[
                        bigquery.SchemaField("summary", "STRING", mode="NULLABLE"),
                        bigquery.SchemaField("summary_en", "STRING", mode="NULLABLE"),
                        bigquery.SchemaField("error_msg", "STRING", mode="NULLABLE"),
                        bigquery.SchemaField("error_msg_translation", "STRING", mode="NULLABLE"),
                    ]),
                bigquery.SchemaField(
                    "translation",
                    "RECORD",
                    fields=[
                        bigquery.SchemaField("title_en", "STRING", mode="NULLABLE"),
                        bigquery.SchemaField("body_en", "STRING", mode="NULLABLE"),
                        bigquery.SchemaField("error_msg_title_en", "STRING", mode="NULLABLE"),
                        bigquery.SchemaField("error_msg_body_en", "STRING", mode="NULLABLE"),
                    ]),
                bigquery.SchemaField(
                    "classification",
                    "RECORD",
                    mode="REQUIRED",
                    fields=[
                        bigquery.SchemaField(
                            "lv1",
                            "RECORD",
                            mode="REPEATED",
                            fields=[
                                bigquery.SchemaField("score", "STRING", mode="NULLABLE"),
                                bigquery.SchemaField("tag", "STRING", mode="NULLABLE")
                            ]),
                        bigquery.SchemaField(
                            "lv2",
                            "RECORD",
                            mode="REPEATED",
                            fields=[
                                bigquery.SchemaField("score", "STRING", mode="NULLABLE"),
                                bigquery.SchemaField("tag", "STRING", mode="NULLABLE")
                            ]),
                        bigquery.SchemaField("error_msg", "STRING", mode="NULLABLE")
                    ]),
            ],
        )

    ]