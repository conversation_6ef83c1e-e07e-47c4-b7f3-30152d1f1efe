import json
import scrapy
from scraper.middlewares import HeadlessBrowserProxy
from scraper.OCSpider import OCSpider
from dotenv import load_dotenv
from scraper.utils.helper import body_normalization
from typing import Optional,List
load_dotenv()

class ChinaSoftwareIndustryAssociation(OCSpider):
    name = "ChinaSoftwareIndustryAssociation"

    hbp = HeadlessBrowserProxy() # Declared headless at class level not used custom setting because some are normal and others are not, mainly last 2 pages use it.

    start_urls_names = {
        "https://www.csia.org.cn/front/branch/9" : "协会要闻",                          
        "https://www.csia.org.cn/front/branch/10" : "通知公告",
        "http://www.csia.org.cn/front/policy.html?type=industry&categoryId=73" : "产业资讯",
        "https://www.csia.org.cn/front/policy.html?type=member&categoryId=75" : "会员动态",
    }

    charset= "utf-8"

    # Last 2 start urls have base urls which should not be scraped 
    include_rules = [r'^https://www\.csia\.org\.cn/.*']

    custom_settings = {
        "DOWNLOAD_DELAY": 6,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    # The first 2 start urls has POST request api call and the later has GET request api call 
    api_start_urls = {
        'https://www.csia.org.cn/front/branch/9': {
            "url": "https://www.csia.org.cn/content/list",
            "payload": {
                "pageNo": "1",
                "categoryId": "9",
            },
        },
        'https://www.csia.org.cn/front/branch/10': {
            "url": "https://www.csia.org.cn/content/list",
            "payload": {
                "pageNo": "1",
                "categoryId": "10",
            },
        },
        "https://www.csia.org.cn/front/policy.html?type=member&categoryId=75": {
            "url": "https://industrymanager.spolicy.cn/api/api/customerApi/policyInfoPageApi/34a1d3de42efa9f654c46803d9e6d285",    
        },
        "http://www.csia.org.cn/front/policy.html?type=industry&categoryId=73": {
            "url": "https://industrymanager.spolicy.cn/api/api/customerApi/policyInfoPageApi/2601c0fc7b0c9808fe148eccaaece5c2",    
        },  
    }

    # Storing the last page value of GET request urls to be called further
    def parse_intermediate(self,response): 
        start_url=response.meta.get('start_url')
        api_data=self.api_start_urls.get(start_url)
        if not api_data:
            self.logger.error(f"No API configuration found for start_url: {start_url}")
            return None
        api_url=api_data["url"]
        payload=api_data.get("payload",None)
        # POST request have Form data stored in payload while no query parameters for GET request api calls
        if payload is not None:
            api_url=api_data["url"]    
            payload["pageNo"] = payload.get("pageNo")
            payload["categoryId"] = payload.get("categoryId")
            yield scrapy.FormRequest(
                url = api_url,
                method = "POST",
                formdata=payload,  
                headers={
                    "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8" 
                },
                callback = self.parse,   
                meta={
                    "start_url": start_url,
                    "api_url": api_data["url"],
                    "payload": payload,
                    "current_page": payload["pageNo"]
                },       
            )
        else:
            # Storing current page value in meta, 1 by default
            current_page = response.meta.get("current_page", 1)
            paginated_api_url = f"{api_url}/{current_page}"
            yield scrapy.Request(
                url=paginated_api_url,
                callback=self.parse,
                meta={
                    "current_page": current_page,
                    "api_url": api_url,
                    "start_url": start_url,
                    "payload":payload,
                },
            )

    @property
    def source_type(self) -> str:
        return 'IndustryAssociation'

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_articles(self, response)->List:
        payload = response.meta.get("payload")
        articles=[]
        if payload is not None:
            json_data = json.loads(response.text)
            if json_data is not None:
                for item in json_data.get('data').get('content'):
                    id=item.get('id')
                    url = f"https://www.csia.org.cn/content/{id}.html"
                    articles.append(url)
        else:
            data=json.loads(response.text) 
            if data is not None:
                for item in data['list']:
                    articles.append(self.hbp.get_proxy(item.get('url'),timeout=20000))
        if articles is not None:
            return articles
        else:
            return None
            
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//section[@class="title"]/h3//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="content"]//text()').getall())
        
    def get_images(self, response) -> List:
        return response.xpath('//div[@class="content"]//img/@src').getall()
    
    def get_authors(self, entry=None) -> list[str]:
        return entry

    def date_format(self) -> str:
        return "%Y-%m-%d" 

    def get_date(self, response) -> str:
        return response.xpath('//div[@class="info text-end"]/span//text()').re_first(r'(\d{4}-\d{2}-\d{2})') 
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response, current_page):
        payload=response.meta.get("payload")
        api_url = response.meta.get('api_url')
        current_page=response.meta.get('current_page')
        if not api_url:
            self.logger.error("API URL not found in response metadata.")
            return None 
        if payload is not None:
            max_pages = response.json().get('data', {}).get('totalPages', 1)
            #  pageNo is a string value '1'
            current_page= str(int(payload["pageNo"]) + 1)
            if int(current_page) > max_pages:
                return None
            return current_page    
        else:
            # Increment current page only if we don't get empty response
            data=response.json().get('list', [])
            if data == []:
                return None
            else:
                next_page = current_page + 1 
                paginated_url = f"{api_url}/{next_page}"
                return paginated_url
        
    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1 ):
        api_url = response.meta.get('api_url')
        if not api_url:
            self.logger.error("API URL not found in meta data.")
            return None
        next_page_url = self.get_next_page(response,current_page)
        payload=response.meta.get("payload")
        if payload is not None:
            payload["pageNo"] = next_page_url
            yield scrapy.FormRequest(
                url=api_url,
                method='POST',
                formdata=payload,
                headers={
                    "Content-Type": "application/x-www-form-urlencoded;",
                },
                callback=self.parse_intermediate,  
                dont_filter=True,
                meta={
                        "api_url": api_url,
                        'start_url': start_url,
                        'payload': payload,
                    }
            )
        elif next_page_url:
            yield scrapy.Request(
                url = next_page_url,
                method = "GET",
                dont_filter=True,
                body = json.dumps(payload),
                callback=self.parse_intermediate,
                meta={
                    "start_url": start_url,
                    "api_url": api_url,
                    "payload": payload,
                    "current_page": current_page+1
                },
        )
        else:
            self.logger.info(f"No more pages to crawl for start_url: {start_url}")     