from scraper.OfficialLineSpider import <PERSON><PERSON>ine<PERSON>pider, configure_ol_spider
from scraper.utils.helper import body_normalization


class Qnb(OfficialLineSpider):

    name = "qnb"

    source = "北京青年报"

    custom_settings = configure_ol_spider()

    start_urls_names = {
        "http://www.why.com.cn/epublish/qnb/html/%s/node_1.htm" : "北京青年报"
    }
    
    HEADLESS_BROWSER_WAIT_TIME = 1000 # 10 seconds

    custom_settings = { 
		"DOWNLOADER_MIDDLEWARES" : {
			# this middleware uses a headless browser to fetch the content
			'scraper.middlewares.HeadlessBrowserProxy': 350,
		}
	}
    
    def __init__(self, name=None, **kwargs):
        super().__init__(name, **kwargs)
        self.date_flag = True

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_start_url(self, url, date):
        return url % (date.strftime('%Y-%m/%d'))

    def get_articles(self, response) -> list:
        return response.xpath("//div[@id='listBox']//ul/li/a/@href").getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//*[@id='maintitle']/text()").get()

    def get_subhead(self, response) ->str:
        return response.xpath("//*[@id='subtitle']/text()").get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@id='articleContent']//p//text()").getall())

    def date_format(self) -> str:
        return '%Y-%m-%d'

    def get_date(self, response) -> str:
        return self.date.strftime('%Y-%m-%d')

    def get_images(self, response) -> list:
        images = response.xpath("//div[@class='info']//img/@src").getall()
        if images:
            return [response.urljoin(url) for url in images]            
        else:
            return []

    def get_authors(self, response):
        return response.xpath("//*[@id='articleAuthor']/text()").get()

    def get_next_page(self, response) -> str:
        next_page = response.xpath("//a[@id='nextPageEd']/@href").get()
        if next_page:
            return response.urljoin(next_page)
        else:
            return None