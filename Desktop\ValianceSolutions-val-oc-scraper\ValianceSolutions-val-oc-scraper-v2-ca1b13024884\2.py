from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from urllib.parse import urljoin
import re
from datetime import datetime

class WustecCninfoMinimal(OCSpider):
    name = "wusteccninfominimal"
    country = "CN"
    start_urls_names = {
        "http://www.wustec.com/news.php": "Wustec News",
        "https://irm.cninfo.com.cn/ircs/company/companyDetail?stockcode=002463&orgId=9900013929": "CNINFO Detail",
    }
    custom_settings = {
        "DOWNLOAD_DELAY": 3,
        "USER_AGENT": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "RETRY_TIMES": 2,
        "HTTPCACHE_ENABLED": True
    }
    charset = "utf-8"

    @property
    def source_type(self) -> str: return "Corporate"
    @property
    def language(self) -> str: return "Chinese"
    @property
    def timezone(self): return "Asia/Shanghai"
    def get_page_flag(self) -> bool: return True
    def get_href(self, entry) -> str: return entry
    def date_format(self) -> str: return "%Y-%m-%d"
    def get_authors(self, response, entry=None) -> list: return ["Unknown"]
    def get_meta(self, response, entry=None) -> list: return []
    def get_pdf(self, response, entry=None): return None

    def get_articles(self, response) -> list:
        if "wustec.com" in response.url:
            links = response.xpath('//div[@class="news_list"]/ul/li/a/@href').getall() or response.xpath('//a[contains(@href, "newsdetail.php")]/@href').getall()
        else:
            links = response.xpath('//table[@class="table-list"]//a/@href').getall() or response.xpath('//div[@class="m-list-item"]//a/@href').getall()
        self.logger.info(f"Found {len(links) if links else 0} potential articles")
        return [response.urljoin(link) for link in (links or []) if link and not link.startswith('#')]

    def get_title(self, response, entry=None) -> str:
        if "wustec.com" in response.url:
            title = response.xpath('//div[@class="news_title"]/text()').get()
        else:
            title = response.xpath('//div[@class="detail-header"]/h1/text()').get()
        if not title:
            title = response.xpath('//h1/text()').get() or response.xpath('//title/text()').get()
        return title.strip() if title else ""

    def get_body(self, response, entry=None) -> str:
        if "wustec.com" in response.url:
            body_parts = response.xpath('//div[@class="news_content"]//text()').getall()
        else:
            body_parts = response.xpath('//div[@class="detail-content"]//text()').getall()
        if not body_parts:
            body_parts = response.xpath('//div[contains(@class, "content")]//text()').getall()
        return body_normalization(body_parts or [])

    def get_date(self, response, entry=None) -> str:
        if "wustec.com" in response.url:
            date_str = response.xpath('//div[@class="news_date"]/text()').get()
        else:
            date_str = response.xpath('//div[@class="detail-header"]//span[contains(text(), "日期")]/following-sibling::text()').get()
        if not date_str:
            date_str = response.xpath('//span[contains(@class, "date")]/text()').get()
        if date_str and (date_match := re.search(r'(\d{4}[-/年]\d{1,2}[-/月]\d{1,2})', date_str)):
            return date_match.group(1).replace('年', '-').replace('月', '-').replace('日', '').replace('/', '-')
        return datetime.now().strftime(self.date_format())

    def get_next_page(self, response) -> list:
        next_page = None
        if "wustec.com" in response.url:
            next_page = response.xpath('//a[contains(text(), "下一页")]/@href').get()
        else:
            next_page = response.xpath('//a[contains(@class, "next-page")]/@href').get()
        return [response.urljoin(next_page)] if next_page else []

    def get_images(self, response, entry=None) -> list:
        if "wustec.com" in response.url:
            img_urls = response.xpath('//div[@class="news_content"]//img/@src').getall()
        else:
            img_urls = response.xpath('//div[@class="detail-content"]//img/@src').getall()
        if not img_urls:
            img_urls = response.xpath('//div[contains(@class, "content")]//img/@src').getall()
        return [urljoin(response.url, img) for img in (img_urls or [])]

    def get_document_urls(self, response, entry=None) -> list:
        doc_urls = response.xpath('//a[contains(@href, ".pdf") or contains(@href, ".doc") or contains(@href, ".docx")]/@href').getall()
        return [urljoin(response.url, doc) for doc in (doc_urls or [])]