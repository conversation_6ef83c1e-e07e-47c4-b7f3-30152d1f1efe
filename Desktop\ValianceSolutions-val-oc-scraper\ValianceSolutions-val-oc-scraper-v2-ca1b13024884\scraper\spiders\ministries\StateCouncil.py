import re
from datetime import datetime
from bs4 import BeautifulSoup
from scraper.utils import helper
from scraper.OCSpider import OCSpider


class StateCouncil(OCSpider):
    name = 'StateCouncil'

    start_urls_names = {
        'http://www.gov.cn/guowuyuan/quantihui.htm': '国务院全体会议',# OK 
    }

    default_article_xpath = "//div[@class='list list_1 list_2']/ul/li/h4/a"
    another_article_xpath = "//div[@class='box-list']//ul//li//div//a"
    article_xpath_3 = "//table[@id='PagerOutline1']//li/div/a"
    article_xpath_4 = "//div[@class='fl list_t']//a"

    website_xpath = {
        'http://www.gov.cn/guowuyuan/quantihui.htm': default_article_xpath,
        'https://www.gov.cn/yaowen/liebiao/home_0.htm': default_article_xpath,
        'https://www.gov.cn/zhengce/jiedu/home_0.htm': default_article_xpath,
        'https://www.gov.cn/zhengce/zuixin/home_0.htm': default_article_xpath,
        # Another design
        'http://www.scio.gov.cn/xwfb/gwyxwbgsxwfbh/fbh/index.html': another_article_xpath,
        'http://www.scio.gov.cn/gwyzclxcfh/index.html': another_article_xpath,
        'http://www.scio.gov.cn/xwfb/rdzxxwfb/rdzxfbh/index.html': article_xpath_3,
        'http://www.scio.gov.cn/xwfb/gfgjxwfb/gfgjfbh/index.html': article_xpath_3,
        'http://www.scio.gov.cn/xwfb/bwxwfb/fbh_13752/home.html': article_xpath_4,
        'http://www.scio.gov.cn/xwfb/dfxwfb/dfxwfbh/home.html': article_xpath_4,
        'http://www.scio.gov.cn/xwfb/qtxwfb/index.html': article_xpath_4,
        'http://www.scio.gov.cn/gxyw/home.html': another_article_xpath,
        'http://www.scio.gov.cn/gxjd/zcjd_27165/index.html': another_article_xpath,
        'http://www.scio.gov.cn/gxzt/zxtj/index.html': another_article_xpath
    }

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_articles(self, response) -> list:
        return response.xpath(self.website_xpath.get(response.meta.get('start_url')))

    regex_id = re.compile(r".*\/(.*)\.")

    def get_href(self, entry) -> str:
        return entry.attrib['href']

    def get_title(self, response) -> str:
        title = response.xpath("//div[@class='tc A_title']//text()").get()
        if title is None:
            title = response.xpath("//div[@class='article oneColumn']/h1[@id='ti']/text()").get()
        if title is None:
            title = response.xpath("//div[@class='mhide']//table//tr//td//b[contains(text(),'标　　题：')]//parent::td//following-sibling::td//text()").get()
        if title is None:
            try:
                title = response.xpath('//div[@class="top fb tc "]//script//text()').get()
                title = re.findall(r'var _docTitle = (.*?);', title)[0]
            except:
                title = response.xpath("//div[@class='top fb tc']//text()").get()
        if title is None:
            try:
                title = response.xpath('//div[@class="bigtitle"]//script//text()').get()
                title = re.findall(r'var _docTitle = (.*?);', title)[0]
            except:
                title = response.xpath('//div[@class="bigtitle"]//text()').get()
        if title is None:
            try:
                title = response.xpath('//div[@id="title_tex"]//script//text()').get()
                title = re.findall(r'var _title = (.*?);', title)[0]
            except:
                title = None
        if title is None:
            title = response.xpath('/html/head/title/text()').get()

        # if title is None:
        #     more = response.xpath("//a[contains(., '[详细内容]')]")
        #     if more is not None:
        #         new_url = more.attrib['href']
        #         yield Request(
        #             url=new_url,
        #             method='GET',
        #             encoding=response.request.encoding,
        #             cookies=response.request.cookies,
        #             headers=response.request.headers,
        #             callback=self.parse_article,
        #             meta={'retry_times': 0},
        #             cb_kwargs={
        #                 '_id': new_url,
        #                 'start_url': response.meta.get('start_url')
        #             }
        #         )
        # else:
        return title.strip()

    def get_body(self, response) -> str:
        body = response.xpath("//div[@class='pages_content' or @id='content']//text()").extract()
        if not body:
            body = response.xpath("//div[@id='UCAP-CONTENT']//p[not(contains(@cmd,'bold'))]//text()").extract()
        if not body:
            body = response.xpath("//div[@class='box']//p//text()").extract()
        if not body:
            body = response.xpath('//div[@id="tex" or @class="article_tex"]//p//text()').extract()
        body = helper.body_normalization(body)
        return body

    def date_format(self) -> str:
        return '%Y-%m-%d %H:%M:%S'

    def get_date(self, response) -> str:
        date = response.xpath('//meta[@name="PubDate"]/@content').get()
        if date is None and response.xpath('//meta[@name="firstpublishedtime"]/@content').get() is not None:
            date = datetime.strptime(
                response.xpath('//meta[@name="firstpublishedtime"]/@content').get(), "%Y-%m-%d-%H:%M:%S"
            ).strftime(self.date_format())
        if date is None:
            try:
                try:
                    date = re.compile("(\d{4}-\d{2}-\d{2})").search(response.xpath("//div[@class='time']//text()").get()).group(1)
                except:
                    date = re.compile("(\d{4}-\d{2}-\d{2})").search(response.xpath("//div[@class='time_tex']//text()").extract()[1]).group(1)
            except:
                date = re.compile("(\d{4}-\d{2}-\d{2})").search(response.xpath("//div[@class='time_tex']//text()").extract()[2]).group(1)
            date = f"{date} 00:00:00"
        return date

    def get_images(self, response) -> list:
        images = []

        for imgTag in BeautifulSoup(self.get_body(response), features="lxml").find_all('img'):
            images.append(response.urljoin(imgTag['src']))
        return images

    author_regex = r"编辑："

    def get_authors(self, response):
        return []  # no author in links articles

    page_count_regex = re.compile('var countPage = (.*)/')
    current_page_regex = re.compile("var currentPage = (\d+)")

    def get_next_page(self, response) -> str:
        total_page = re.compile("return POP_checkInput\('PagerOutline1_input',(\d+),'index','.htm'\)").search(response.text)
        if total_page is None:
            try:
                total_page = re.findall(r"return POP_checkInput\('PagerOutline1_input',(.*?),", response.text)[0]
            except:
                total_page = None
        if 'index.htm' in response.url or 'home.html' in response.url:
            current_page = 0
        else:
            current_page = re.compile("\/index_(\d+).htm").search(response.url)

        if total_page is None:
            total_page = int(re.findall("var nPageCount = (.*?);", response.text)[0])
            current_page = int(re.compile("\/home_(\d+).htm").search(response.url).group(1))
            next_page = current_page + 1

            if next_page >= total_page:
                return None
            else:
                return response.urljoin(f"home_{next_page}.htm")
        else:
            try:
                total_page = int(total_page.group(1))
            except:
                total_page = int(total_page)
            if current_page == 0:
                next_page = current_page + 1
            else:
                next_page = int(current_page.group(1)) + 1
            if next_page >= total_page:
                return None
            else:
                if 'home' in response.url:
                    return response.urljoin(f"home_{next_page}.html")
                else:
                    return response.urljoin(f"index_{next_page}.html")
