from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from urllib.parse import urljoin
from urllib.parse import urlsplit, urlunsplit

class HawaiiDepartmentofBusinessEconomicDevelopmentAndTourism(OCSpider):
    name = 'HawaiiDepartmentofBusinessEconomicDevelopmentAndTourism'

    country = "US"

    start_urls_names = {
        "https://dbedt.hawaii.gov/news/": "News",
    }
    charset = "utf-8"
    charset = "iso-8859-1"
    
    article_date_map = {}
    article_title_map = {}

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Pacific/Honolulu"

    @property
    def language(self):
        return "English"

    def get_articles(self, response) -> list:
        self.extract_articles_with_dates(response)
        articles = response.xpath("//a[contains(@class, 'scp_title')]/@href").getall()
        cleaned_articles = list(set(self.normalize_url(urljoin(response.url, href.strip())) for href in articles))
        return cleaned_articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        article_url = self.normalize_url(response.url)
        title = self.article_title_map.get(article_url)
        if not title:
            title = response.xpath('//div[@class="pagetitle"]/h2/text()').get()
        return title.strip() if title else ""
  
    def get_body(self, response) -> str:
        if response.url.lower().endswith('.pdf'):
            return ""      
        return body_normalization(response.xpath( "//div[@class='primary-content']//p//text() | //div[@class='primary-content']//ul//li//text()").getall())
    
    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return " %B %d, %Y"

    def get_date(self, response):
        normalized_url = self.normalize_url(response.url)
        date = self.article_date_map.get(normalized_url)
        return date
        
    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False
    

    def get_document_urls(self, response, entry=None) -> list:
        link = ''
        if isinstance(entry, str):
            link = entry
        elif isinstance(entry, dict):
            link = entry.get('link', '')
        if link.lower().endswith('.pdf'):
            return [response.urljoin(link)]
        pdf_links = response.xpath('//a[contains(@href, ".pdf")]/@href').getall()
        full_links = list(set(response.urljoin(pdf_link.strip()) for pdf_link in pdf_links if pdf_link.strip()))   
        return full_links

    def get_next_page(self, response) -> str:
        next_page = response.xpath("//div[@class='nav-previous alignleft']//a//@href").get()
        if next_page:
            return next_page
        else:
            return None

    def extract_articles_with_dates(self, response):
        base_url = response.url
        posts = response.xpath('//div[@class="scp_post clearfix"]')
        for index, post in enumerate(posts):
            href = post.xpath('.//a[contains(@class, "scp_title")]/@href').get()
            title = post.xpath('.//a[contains(@class, "scp_title")]/text()').get()
            date_text = post.xpath('.//span[contains(@class, "scp_date")]/text()').get()
            if href and title and date_text:
                full_url = self.normalize_url(urljoin(base_url, href.strip()))
                cleaned_title = title.strip()
                cleaned_date = date_text.replace("Posted on", "").split("in")[0].strip()
                self.article_date_map[full_url] = cleaned_date
                self.article_title_map[full_url] = cleaned_title

    def normalize_url(self, url: str) -> str:
        parts = urlsplit(url)
        return urlunsplit((parts.scheme, parts.netloc, parts.path, parts.query, ''))    