import scrapy
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import json
import re


class NewAmericaFoundation(OCSpider):
    name = "NewAmericaFoundation"

    country = "US"

    start_urls_names = {
        "https://www.newamerica.org/press-releases/": "Press Releases",
    }

    api_start_urls = {
        'https://www.newamerica.org/press-releases/': {
        "url": "https://www.newamerica.org/api/post/?content_type=pressrelease&cursor=cj0xJnA9MjAyNC0xMS0xOC0zNTk1Mg%3D%3D&page_size=8&story_image_rendition=small",
        }
    }

    charset = "utf-8"

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_url = response.meta.get("api_url")
        if not api_url:
            api_data = self.api_start_urls.get(start_url)
            if not api_data:
                return
            api_url = api_data["url"]
        yield scrapy.Request(
            url=api_url,
            method="GET",
            headers={
                "Content-type": "application/json"
            },
            callback=self.parse,
            dont_filter=True,
            meta={
                "start_url": start_url,
                "api_url": api_url,
            },
        )

    @property
    def language(self):
        return "English"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "US/Eastern"

    def get_articles(self, response) -> list:
        data = json.loads(response.text)
        article = [entry["url"] for entry in data.get("results", [])]
        return article

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//h1//text()").get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='rich-text']//p//text()").getall())

    def get_images(self, response, entry=None):
        return response.xpath("//div[@class='post-heading__image__wrapper']//div//@src").getall()

    def get_authors(self, response, entry=None) -> list[str]:
        return []

    def date_format(self) -> str:
        return "%b %d, %Y"

    def get_date(self, response) -> str:
        date=response.xpath("//h6[@class='post-body__date margin-10']//text()").get()
        date = date.replace(".","")
        date = re.sub(r'([A-Za-z]{3})[a-z.]*', r'\1', date)
        return date
    
    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        data = json.loads(response.text)
        next_url= data.get("next","")
        return next_url

    def go_to_next_page(self, response, start_url, current_page= None):
        next_page = self.get_next_page(response)
        if next_page:
            yield scrapy.Request(
                url=next_page,
                method='GET',
                headers={
                    "Content-type": "application/json"
                },
                dont_filter=True,
                callback=self.parse_intermediate,
                meta={
                    "start_url": start_url,
                    "api_url": next_page,
                }
            )
        else:
            return