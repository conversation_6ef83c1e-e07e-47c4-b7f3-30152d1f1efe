from scraper.OCSpider import <PERSON><PERSON>pider
from scraper.utils.helper import body_normalization
import re
from typing import List

class ZhongJinGoldCorporationLimited(OCSpider):
    name = "<PERSON>hong<PERSON>inGoldCorporationLimited"

    HEADLESS_BROWSER_WAIT_TIME = 100 
    
    custom_settings = {
		"DOWNLOADER_MIDDLEWARES" : {
			'scraper.middlewares.HeadlessBrowserProxy': 350,
            'scraper.middlewares.GeoProxyMiddleware': 350,
		},

	}

    start_urls_names = {
    "https://zjgold.chinagoldgroup.com/3190.html": "Company News",
    "https://zjgold.chinagoldgroup.com/3232.html":"Company News",
    "https://zjgold.chinagoldgroup.com/3191.html":"Group News",
    "https://zjgold.chinagoldgroup.com/3209.html" :"Regular announcements",
    "https://zjgold.chinagoldgroup.com/3222.html":"Social Responsibility Report",
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"
    
    @property
    def timezone(self):
        return "Asia/Shanghai"

    article_data_map = {}

    def get_articles(self, response) -> list:
        self.extract_articles_with_dates(response)
        return list(self.article_data_map.keys())

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map[response.request.meta.get('entry')].get("title")
    
    def get_authors(self, response):
        return []
    
    def get_document_urls(self, response, entry=None) -> List[str]:
        return self.article_data_map[response.request.meta.get('entry')].get("pdf")

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//p[@class='indent']//text()").getall())

    def date_format(self) -> str:
        return '%Y-%m-%d'
    
    def get_date(self, response) -> str:
        return self.article_data_map[response.request.meta.get('entry')].get("date")
    
    def get_images(self, response) -> list:
        return []
    
    def get_page_flag(self) -> bool:
        return True
    
    def get_next_page(self, response) -> str:
        next_page = response.xpath('//a[@class="i-pager-next"]/@href').get()
        if next_page:
            full_url = response.urljoin(next_page)
            return full_url
        return None
    
    def extract_articles_with_dates(self, response):
        mapping = {}
        articles = response.xpath("//div[@class='second-news-item']")
        for article in articles:
            a_tag = article.xpath(".//div[@class='second-news-item-title']/a")
            url = a_tag.xpath("./@href").get()
            title = a_tag.xpath("./@title").get()
            date = article.xpath(".//div[@class='second-news-item-date']//text()").get()

            if url and title and date:
                full_url = response.urljoin(url.strip())
                clean_date = date.strip()
                mapping[full_url] = {
                    "title": title.strip(),
                    "date": clean_date,
                    "pdf": [full_url] if url.endswith(".pdf") else []
                }

        self.article_data_map.update(mapping)
        return self.article_data_map