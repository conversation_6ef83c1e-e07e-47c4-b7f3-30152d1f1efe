from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
from scraper.middlewares import HeadlessBrowserProxy

class WingtechTechnolgyCoLtd(OCSpider):
    name = "WingtechTechnolgyCoLtd"

    hbp = HeadlessBrowserProxy()

    start_urls_names = {
        "http://www.wingtech.com/cn/toWTNEWS/21/1": "闻泰科技",
        "https://www.sse.com.cn/assortment/stock/list/info/announcement/index.shtml?productId=600745":"闻泰科技",
    }

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350
        },
        "DOWNLOAD_DELAY": 2,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    HEADLESS_BROWSER_WAIT_TIME = 10000  

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "private_enterprise"
    
    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_articles(self, response) -> list:    
        return response.xpath("//div[@class='title']//a//@href").getall()
                  
    def get_href(self, entry):
        return entry
    
    def get_title(self, response, entry=None) -> str:
        return response.xpath('//h4/text()').get()
        
    def get_body(self, response, entry=None) -> str:
        return body_normalization(response.xpath("//div[@class='content']//p//text()").getall())
               
    def get_images(self, response, entry=None) -> list:
        return response.xpath("//div[@class='content']//p//img//@src").getall()
    
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response, entry=None) -> str:
        return response.xpath("//div[@class='Timmer']//text()").get().strip()
        
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        page_number=2
        last_page = response.xpath("//ul[@class='pagination']//a[contains(text(),'Last')]//text()").get()
        if page_number<=int(last_page):
            next_page = f'http://www.wingtech.com/cn/toWTNEWS/21/{page_number}'
            page_number+=1
            return next_page 