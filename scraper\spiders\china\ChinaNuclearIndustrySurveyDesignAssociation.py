from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ChinaNuclearIndustrySurveyDesignAssociation(OCSpider):
    name = 'ChinaNuclearIndustrySurveyDesignAssociation'
    
    start_urls_names = {
        'https://www.cnida.cn/col.jsp?id=107' : '党建风采',
        'https://www.cnida.cn/col.jsp?id=108' : '通知公告',
        'https://www.cnida.cn/col.jsp?id=110' : '协会动态'
    }

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"

    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response):
        articles = response.xpath("//div[@class='m_news_info']//div[@class='news_title']//a//@href").getall()
        return articles
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self,response) -> str:
        return response.xpath('//h1[@class="title"]//text()').get()
    
    def date_format(self) -> str:
        return '%Y-%m-%d %H:%M'
    
    def get_date(self, response) -> str:
        return response.xpath("//span[@class='newsInfo']//text()").re_first(r"\d{4}-\d{2}-\d{2} \d{2}:\d{2}")
    
    def get_authors(self, response):
        return []
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='jz_fix_ue_img']/section//text()").getall())
    
    def get_images(self, response) -> list[str]:
        images = response.xpath("//p//img/@src").extract()
        return images
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        try:
            next_page = response.xpath("//span[@class='pageNext']//a//@href").get()
            if next_page:
                next_page_url = response.urljoin(next_page)
                if next_page_url == response.url:  # Condition to check if the next page is the same as the current page
                    return None  # Stop crawling
                return next_page_url
            self.logger.info("No next page found.")
            return None
        except Exception as e:
             self.logger.error(f"Error extracting next page link from page {response.url}: {e}")
             return None