from typing import Optional, List
from scraper.OCSpider import <PERSON><PERSON><PERSON>er
from scraper.utils.helper import body_normalization
import scrapy
from datetime import datetime
import re

class Adisseo(OCSpider):
    name = "Adisseo"

    start_year = datetime.now().year
    end_year = 2015

    start_urls_names = {
        f"https://www.adisseo.com.cn/news?year={start_year}": "Press Releases",
        "https://www.adisseo.com.cn/investor-announcements": "announcement",
    }

    start_urls = [
        f"https://www.adisseo.com.cn/news?year={start_year}",
    ]

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350
        },
        "DOWNLOAD_DELAY": 6,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    HEADLESS_BROWSER_WAIT_TIME = 30000
    charset = "utf-8"

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36"
    }

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_articles(self, response) -> list:
        articles = response.xpath('//div[@class="news-content"]//h4/a/@href').getall()
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response, entry=None) -> str:
        return response.xpath('//div[@class="titre_presse_detail_texte"]/h1//text()').get()

    def get_body(self, response, entry=None) -> str:
        return body_normalization(response.xpath('//div[@class="cg-c-article__content"]//text()').getall())

    def get_images(self, response, entry=None) -> List[str]:
        return []

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response, entry=None) -> str:
        return response.xpath('//div[@class="titre_presse_detail_texte"]/span//text()').get()

    def get_authors(self, response, entry=None) -> List[str]:
        return []

    def get_page_flag(self) -> bool:
        return False

    def go_to_next_page(self, response, start_url, current_page: Optional[int] = None):
        current_year = self._extract_year_from_url(response.url)
        next_year = current_year - 1 if current_year else None
        if next_year and next_year >= self.end_year:
            next_url = f"https://www.adisseo.com.cn/news?year={next_year}"
            yield scrapy.Request(
                url=next_url,
                headers=self.headers,
                callback=self.parse,
                meta={"start_url": next_url, "current_page": next_year}
            )

    def _extract_year_from_url(self, url: str) -> Optional[int]:
        match = re.search(r"year=(\d{4})", url)
        return int(match.group(1)) if match else None
