from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from typing import Optional
from datetime import datetime 

class Tongwei(OCSpider):
    name = "Tongwei"

    start_urls_names = {
        "https://www.tongwei.cn/news.html": "青岛啤酒"
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "SOE"
    
    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_articles(self, response) -> list:
        return response.xpath('//a[@class="item"]/@href').getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="container-title"]//h1[@class="text-center f36"]/text()').get()


    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//p//text()').getall()) 
    
    def get_images(self, response) -> list:
        return response.xpath("//div//p//img//@src").getall()
    
    
    def date_format(self) -> str:
        return "%B %d, %Y"  # For "April 16, 2025"

    def get_date(self, response) -> str:
        date_str = response.xpath('//div[@class="flex flex-items-center"]/span/text()').get()
        date_str = date_str.replace('发布时间：', '').strip()
        return datetime.strptime(date_str, "%Y年%m月%d日").strftime(self.date_format())
        
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> Optional[str]:
        next_button_exists = response.xpath("//span[@class='next']/a/@href").get()
        return response.urljoin(next_button_exists)