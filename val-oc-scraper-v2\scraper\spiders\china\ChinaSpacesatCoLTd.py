from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ChinaSpacesatCoLTd(OCSpider):
    name = "ChinaSpacesatCoLTd"

    start_urls_names = {
        "http://www.spacesat.com.cn/templates/content/news.aspx?nodeid=9": "",
        "http://www.spacesat.com.cn/templates/content/index.aspx?nodeid=27": "",
        "http://www.spacesat.com.cn/templates/content/index.aspx?nodeid=31": "",
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"
    
    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        relative_urls = response.xpath('//ul[@class="lmnb_ul"]//a/@href').getall()
        full_urls = [response.urljoin(url) for url in relative_urls]
        return full_urls
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="content_kk"]//h2/text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath(
            '//div[@class="acc_content"]//p//span[normalize-space(text())]/text() | '
            '//div[@class="acc_content"]//section//span[normalize-space(text())]/text() | '
            '//div[@class="acc_content"]//p[normalize-space()]//text()'
        ).getall())
    
    def get_images(self, response) -> list:
        relative_urls = response.xpath('//div[@class="acc_content"]//img/@src').getall()
        full_urls = [response.urljoin(url) for url in relative_urls]
        return full_urls
       
    def date_format(self) -> str:
        return"%Y-%m-%d"
    
    def get_date(self, response) -> str:
        return response.xpath('substring-after(//div[@class="acc_right"]/text(), "发布时间：")').get()
    
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]: 
        next_page = response.xpath('(//div[@class="fy_a"]//a)[last()-1]/@href').get()
        if next_page:
            return response.urljoin(next_page)
        return None