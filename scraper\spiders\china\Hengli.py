from typing import Optional, List
from scraper.OCSpider import <PERSON><PERSON><PERSON>er
from scraper.utils.helper import body_normalization
import scrapy
from datetime import datetime
import re

class Heng<PERSON>(OCSpider):
    name = "Heng<PERSON>"


    start_urls_names = {
        "https://www.henglihydraulics.com/col39/list_1": "NEWS",
        
    }

   

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350
        },
        "DOWNLOAD_DELAY": 6,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    HEADLESS_BROWSER_WAIT_TIME = 30000
    charset = "utf-8"

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36"
    }

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_articles(self, response) -> list:
        articles = response.xpath('//li[contains(@class, "slick-current")]//a[@class="flexaw Scale-img"]/@href | //div[@class="photoNews w1440"]//ul/li/a/@href').getall()
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response, entry=None) -> str:
        return response.xpath('//div[@class="TitInfo tc"]/h1/text()').get()

    def get_body(self, response, entry=None) -> str:
        return body_normalization(response.xpath('//article[@class="SinglePage NewsInfo imgt"]//div[@class="imgt"]').getall())

    def get_images(self, response, entry=None) -> List[str]:
        return []

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response, entry=None) -> str:
        raw_text = response.xpath('//div[@class="other f18"]/time//text()').get()
        if raw_text:
            match = re.search(r"\d{4}-\d{2}-\d{2}", raw_text)
            if match:
                return match.group()
        return ""

    def get_authors(self, response, entry=None) -> List[str]:
        return []

    def get_page_flag(self) -> bool:
        return True

    def go_to_next_page(self, response, start_url, current_page: Optional[int] = None):
        next_page = response.xpath('//em[@class="num"]/a[@class="a_cur"]/following-sibling::a[1]/@href').get()
        if next_page:
            return response.urljoin(next_page)
        print("####NEXT_PAGE:", next_page)
        return None

 