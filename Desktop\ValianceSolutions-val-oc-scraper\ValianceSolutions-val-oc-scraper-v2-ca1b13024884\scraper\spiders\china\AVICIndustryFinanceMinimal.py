# from typing import List
# from scraper.OCSpider import OCSpider
# from scraper.utils.helper import body_normalization
# from urllib.parse import urljoin

# class AVICIndustryFinanceMinimal(OCSpider):
#     name = "AVICIndustryFinanceMinimal"
#     country = "CN"
#     start_urls_names = {
#         "https://www.avicindustry-finance.com/sycd/xwzx/zhcrxw/": "AVIC Industry Finance News",
#     }
#     charset = "utf-8"


#     @property
#     def source_type(self) -> str:
#         return "Corporate"

#     @property
#     def language(self) -> str:
#         return "Chinese"

#     @property
#     def timezone(self) -> str:
#         return "Asia/Shanghai"

#     def get_articles(self, response) -> list:
#         return response.xpath('//li[@class="listTableLi"]/a/@href').getall()

#     def get_href(self, entry) -> str:
#         return entry

#     def get_title(self, response, entry=None) -> str:
#         title = response.xpath('//title/text()').get()
#         return title.strip() if title else ""

#     def get_body(self, response, entry=None) -> str:
#         body_parts = response.xpath('//div[@class="txt"]//text()').getall()
#         return body_normalization(body_parts)

#     def date_format(self) -> str:
#         return "%Y-%m-%d"

#     def get_date(self, response, entry=None) -> int:
#         date = response.xpath("//span[@class='date']//text()").get()
#         return date.replace("发布时间：", "")

#     def get_authors(self, response, entry=None) -> List[str]:
#         return []

#     def get_images(self, response, entry=None) -> List[str]:
#         img_urls = response.xpath('//img/@src').getall()
#         return [urljoin(response.url, img) for img in img_urls]

#     def get_page_flag(self) -> bool:
#         return True

#     def get_next_page(self, response) -> List[str]:
#         current_page = 2
#         if current_page < 46:  # Adjust this depending on how many pages you need
#             next_page = f"https://www.avicindustry-finance.com/sycd/xwzx/zhcrxw/index_{current_page}.shtml?PC=PC"
#             current_page += 1
#             return next_page
#         return None 

#     def get_document_urls(self, response, entry=None) -> list:
#         docs = response.xpath('//a[contains(@href, ".pdf")]/@href').getall()
#         return [urljoin(response.url, doc) for doc in docs]
from typing import List
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from urllib.parse import urljoin

class AVICIndustryFinanceMinimal(OCSpider):
    name = "AVICIndustryFinanceMinimal"
    country = "CN"
    start_urls_names = {
        "https://www.avicindustry-finance.com/sycd/xwzx/zhcrxw/": "AVIC Industry Finance News",
    }
    charset = "utf-8"
    current_page = 2  # Start from the second page

    @property
    def source_type(self) -> str:
        return "Corporate"

    @property
    def language(self) -> str:
        return "Chinese"

    @property
    def timezone(self) -> str:
        return "Asia/Shanghai"

    def get_articles(self, response) -> list:
        return response.xpath('//li[@class="listTableLi"]/a/@href').getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response, entry=None) -> str:
        title = response.xpath('//title/text()').get()
        return title.strip() if title else ""

    def get_body(self, response, entry=None) -> str:
        body_parts = response.xpath('//div[@class="txt"]//text()').getall()
        return body_normalization(body_parts)

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response, entry=None) -> int:
        date = response.xpath("//span[@class='date']//text()").get()
        return date.replace("发布时间：", "")

    def get_authors(self, response, entry=None) -> List[str]:
        return []

    def get_images(self, response, entry=None) -> List[str]:
        img_urls = response.xpath('//img/@src').getall()
        return [urljoin(response.url, img) for img in img_urls]

    def get_page_flag(self) -> bool:
        return True

    def get_next_page(self, response) -> List[str]:
        if self.current_page <= 5:
            next_page = f"https://www.avicindustry-finance.com/sycd/xwzx/zhcrxw/index_{self.current_page}.shtml?PC=PC"
            self.current_page += 1
            return next_page
        elif self.current_page <= 46:
            next_page = f"https://www.avicindustry-finance.com/cms/ui/catalog/19354/pc/index_{self.current_page}.shtml?PC=PC"
            self.current_page += 1
            return next_page
        return None 

    def get_document_urls(self, response, entry=None) -> list:
        docs = response.xpath('//a[contains(@href, ".pdf")]/@href').getall()
        return [urljoin(response.url, doc) for doc in docs]
