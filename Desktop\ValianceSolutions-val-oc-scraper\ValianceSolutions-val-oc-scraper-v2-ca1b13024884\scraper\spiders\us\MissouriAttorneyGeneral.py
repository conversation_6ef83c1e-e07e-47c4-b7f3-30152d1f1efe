from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
from typing import List
import scrapy

class MissouriAttorneyGeneral(OCSpider):
    name = 'MissouriAttorneyGeneral'

    country = "US"
    
    start_urls_names = {
        'https://ago.mo.gov/press-releases/': "Press-Release"
    } 

    article_data_map ={}  # Mapping articles with Author, dates from start URL
    
    def parse_intermediate(self, response):
        all_articles = response.xpath("//div[@class='et_pb_ajax_pagination_container']//h2//a//@href").getall()
        total_articles = len(all_articles)
        articles_per_page = 100
        start_url = response.meta.get("start_url", response.url)
        for start_idx in range(0, total_articles, articles_per_page): # indexing for virtual pagination as more than 100 articles in start URL
            yield scrapy.Request(
                url=start_url,
                callback=self.parse,
                meta={
                    'start_idx': start_idx, 
                    'articles': all_articles, 
                    'start_url': start_url
                },
                dont_filter=True
            )
            
    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Central"
    
    def get_articles(self, response) -> list:
        try:
            all_articles = response.xpath("//div[@class='et_pb_ajax_pagination_container']//h2//a//@href").getall()
            all_articles = [url.strip() for url in all_articles if url.strip()]
            for full_url in all_articles:
                author = response.xpath("//span[@class='author vcard']//text()").get()
                date = response.xpath("//span[@class='published']//text()").get()
                if full_url:
                    if not author:
                        author = response.xpath("//title/text()").get(default="").strip()
                    if not date:
                        date = response.xpath("//date/text()").get(default="").strip()
                    self.article_data_map[full_url] = {  # Mapping done for indexing of Articles with respective author, date and URL.
                    "author": author.strip(),
                    "date": date.strip().replace("\xa0", " "),
                    "url": [full_url]
                }
            start_idx = response.meta.get('start_idx', 0) # More than 100 articles are there in Start URL
            end_idx = start_idx + 100
            return all_articles[start_idx:end_idx] # Only Article url's are extracted and returned
        except Exception as e:
            return []

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//h1//text()").get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='et_pb_module et_pb_post_content et_pb_post_content_0_tb_body']//text()").getall())
    
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return '%b %d, %Y'

    def get_date(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
    
    def get_authors(self, response):
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("author", "")

    def get_document_urls(self, response, entry=None) -> List[str]:
        return response.xpath("//div[@class='et_pb_module et_pb_post_content et_pb_post_content_0_tb_body']//a[contains(@href,'.pdf')]//@href").getall()

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        next_page = response.xpath("//div[@class='alignleft']//a[contains(text(),'« Older Entries')]//@href").get()
        if next_page:
            return next_page
        else:
            return None