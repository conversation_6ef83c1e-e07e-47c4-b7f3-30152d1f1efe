
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization

class KentuckyTransportationCabinet(OCSpider):
    name = "KentuckyTransportationCabinet"

    country="US"

    start_urls_names = {
        "https://transportation.ky.gov/Pages/Newsroom-Archives.aspx" :"Newsroom",
    }

    HEADLESS_BROWSER_WAIT_TIME = 30000 
    
    custom_settings = {
		"DOWNLOADER_MIDDLEWARES" : {
			'scraper.middlewares.HeadlessBrowserProxy': 350,
		},
		"DOWNLOAD_DELAY" : 5,
	}

    charset="iso-8859-1"

    @property
    def language(self) :
        return "English"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "US/Eastern"
    
    article_data_map = {}  # Mapping date with articles from start URL
    
    def get_articles(self, response) -> list:
        # Extract articles from function
        self.extract_articles_with_dates(response)
        return [response.urljoin(link) for link in response.xpath("//td[@class='ms-vb2'][2]//a//@href").getall()]
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map[response.request.meta.get('entry')].get("title")
        
    def get_body(self, response) -> str:
        return ""

    def get_images(self, response, entry=None) :
        return []

    def date_format(self) -> str:
        return "%m/%d/%Y"
    
    def get_date(self, response) -> str:
        return self.article_data_map[response.request.meta.get('entry')].get("date")

    def get_authors(self, response, entry=None) -> list[str]:
        return []
    
    def get_document_urls(self, response, entry=None):
        return self.article_data_map[response.request.meta.get('entry')].get("pdf")
    
    def get_page_flag(self) -> bool:
        return True

    def get_next_page(self, response) -> str:
        return 
    
    def extract_articles_with_dates(self, response):
        # Function to extract dates of respective articles from start URL
        mapping = {}
        for article in response.xpath("//tr"):
            url = article.xpath(".//td[@class='ms-vb2'][2]//a/@href").get()
            title = article.xpath(".//td[@class='ms-vb2'][1]/text()").get()
            date = article.xpath(".//td[@class='ms-vb2'][5]/text()").get()
            if url and title and date:
                full_url = response.urljoin(url.strip())
                clean_date=date.strip()
                mapping[full_url] = {"title": title.strip(), "date": clean_date, "pdf": [full_url]}
            self.article_data_map.update(mapping)
        return self.article_data_map