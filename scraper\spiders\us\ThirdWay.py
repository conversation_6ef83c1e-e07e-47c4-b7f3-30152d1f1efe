import logging
from typing import Optional
import scrapy
from scraper.OCSpider import <PERSON><PERSON>pid<PERSON>
from scraper.utils.helper import body_normalization

class ThirdWay(OCSpider):
    name = 'ThirdWay'
    
    country = "US"

    start_urls_names = {
        'https://www.thirdway.org/press': 'News'
    }
    
    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350
        },
        "DOWNLOAD_DELAY": 5,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    HEADLESS_BROWSER_WAIT_TIME = 30000  # 30 seconds wait time
    
    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Central"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@class= "mb-4 PaginatedEntry row"]/div//h2/a/@href').getall() 
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self,response) -> str:
        return response.xpath('//div[@class= "col-xs-12"]/h1[@class= "mb-4"]/text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class= "col-xs-12 col-lg-8"]/section/p/text()').getall())    
            
    def get_images(self, response) -> list[str]:
        return []

    def date_format(self) -> str:
        return "%B %d, %Y"

    def get_date(self, response) -> str:
        return response.xpath('//div[@class= "published-at"]/text()').re_first(r"([A-Za-z]+ \d{1,2}, \d{4})")
    
    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response, current_page):
        if response.status != 200:
            return None
        else:
            return str(int(current_page) + 1)
    
    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        next_page = self.get_next_page(response, current_page)
        url=f"https://www.thirdway.org/press?page={next_page}"
        if next_page:
            yield scrapy.Request(
                url=url,
                method='GET',
                callback=self.parse, 
                dont_filter=True,
                 meta={
                        'current_page': next_page, 
                        'start_url': start_url,
                    }
            )
        else:
            logging.info("No more pages to fetch.")
            yield None