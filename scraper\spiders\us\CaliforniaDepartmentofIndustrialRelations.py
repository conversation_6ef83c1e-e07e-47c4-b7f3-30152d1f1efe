import datetime
import scrapy
import re
from urllib.parse import urljoin
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import re


class CaliforniaDepartmentOfIndustrialRelations(OCSpider):
    name = 'CaliforniaDepartmentOfIndustrialRelations'
    country = "US"

    start_urls_names = {
        'https://www.dir.ca.gov/dirnews/': "News"
    }

    custom_settings = {
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    charset = "iso-8859-1"
    article_date_map = {}
    article_title_map = {}

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self) -> str:
        return "US/Eastern"

    @property
    def language(self):
        return "English"

    def normalize_url(self, url: str) -> str:
        return url.lower().strip()

    def get_articles(self, response) -> list:
        self.extract_articles_with_dates(response)
        return response.xpath('//div[@class="scp_post clearfix"]//a[@class="scp_title scp_part"]/@href').getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        article_url = response.url
        normalized_url = article_url.lower().strip()
        return self.article_title_map.get(normalized_url)

    def get_body(self, response):
        if not response.headers.get('Content-Type', b'').startswith(b'text/html'):
            return "" 
        return body_normalization(response.xpath('//p//text()').getall())

    def get_images(self, response) -> list[str]:
        return []

    def date_format(self) -> str:
        return '%B %d, %Y'

    def get_date(self, response):
        article_url = response.url
        normalized_url = self.normalize_url(article_url)
        return self.article_date_map.get(normalized_url)

    def get_authors(self, response, entry=None) -> list[str]:
        return []
    
    def get_document_urls(self, response, entry=None) -> list:
        link = entry if isinstance(entry, str) else entry.get('link', '') if isinstance(entry, dict) else ''
        if link.lower().endswith('.pdf'):
            return [response.urljoin(link)]     
        pdf_links = response.xpath('//div[@class="container"]//ul/li/a[contains(@href, ".pdf")]/@href').getall()
        full_links = [response.urljoin(pdf_link) for pdf_link in pdf_links]
        return full_links


    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        current_year = str(datetime.datetime.now().year)
        archive_links = response.xpath('//ul[@class="yearpick indent"]/li/a/@href').getall()
        full_links = [response.urljoin(href) for href in archive_links if not href.endswith(f"NR{current_year}.html")]
        for link in full_links:
            self.visited_links = getattr(self, 'visited_links', set())
            if link not in self.visited_links:
                self.visited_links.add(link)
                return link
        return None
    
    def extract_articles_with_dates(self, response):
        base_url = response.url
        rows = response.xpath("//table//tr")
        for row in rows:
            href = row.xpath(".//a/@href").get()
            date = row.xpath(".//td[1]/text()").get()
            title = row.xpath(".//a/text()").get()
            if href and date and title:
                full_url = urljoin(base_url, href)
                normalized_url = full_url.lower().strip()
                cleaned_date = date.strip()
                cleaned_title = title.strip()
                self.article_date_map[normalized_url] = cleaned_date
                self.article_title_map[normalized_url] = cleaned_title

