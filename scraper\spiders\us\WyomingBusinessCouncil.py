from typing import Optional
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
from datetime import datetime

class WyomingBusinessCouncil(OCSpider):
    name = "WyomingBusinessCouncil"

    country = "US"

    start_urls_names = {
        "https://wyomingbusiness.org/news/": "News",
    }

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350
        },
        "DOWNLOAD_DELAY": 5,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    HEADLESS_BROWSER_WAIT_TIME = 30000

    charset = "utf-8"

    article_date_map = {}  # Mapping child articles with title and date from start URL

    @property
    def language(self):
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self):
        return "America/Denver"

    def get_articles(self, response) -> list:
        self.extract_articles_with_dates(response)
        return response.xpath('//h3[contains(@class, "elementor-post__title")]//a/@href').getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//h1[contains(@class, "elementor-heading-title")]/text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//p//text()').getall())

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%Y/%m/%d"
    
    def get_date(self, response) -> Optional[str]:
        article_url = response.url
        raw_date = self.article_date_map.get(article_url, None)
        if raw_date is None:
            return None
        try:
            date_obj = datetime.strptime(raw_date, "%B %d, %Y")
            formatted_date = date_obj.strftime("%Y/%m/%d")
            return formatted_date
        except ValueError as e:
            return None

    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> Optional[str]:
        next_page = response.xpath('//nav[contains(@class, "elementor-pagination")]//a[contains(@class, "next")]/@href').get()
        if next_page:
            return next_page
        return None

    def extract_articles_with_dates(self, response):
        for article in response.xpath('//article[contains(@class, "elementor-post")]'):
            url = article.xpath('.//h3[contains(@class, "elementor-post__title")]/a/@href').get()
            date = article.xpath('.//span[contains(@class, "elementor-post-date")]/text()').get()
            if url and date:
                self.article_date_map[url] = date.strip()
        return self.article_date_map