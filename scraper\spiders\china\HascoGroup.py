from scraper.OCSpider import OCSpider
import scrapy

class HascoGroup(OCSpider):
    name = "HascoGroup"

    country="china"

    start_urls_names = {
        # "https://www.hasco-group.com/IR/notice.html" :"华域汽车",
        "https://www.hasco-group.com/news/news.html":"华域汽车",
        # "https://www.hasco-group.com/IR/service.html":"华域汽车",
    }

    # HEADLESS_BROWSER_WAIT_TIME = 10000

    # custom_settings = {
    #     "DOWNLOADER_MIDDLEWARES":
    #         {
    #             'scraper.middlewares.HeadlessBrowserProxy': 350,
    #         },
    #     "DOWNLOAD_DELAY": 1,
    # }

    charset="iso-8859-1"

    @property
    def language(self) :
        return "English"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    article_data_map = {}  # Mapping date with articles from start URL
    
    def get_articles(self, response) -> list:
        try:
            articles = response.xpath("//div[@id='listData_0']//a//@href").getall()
            article_urls = []
            if len(articles)>0:
                for article in response.xpath("//div[@id='listData_0']"):
                    url = article.xpath(".//a/@href").get()
                    title = article.xpath(".//h6//text()").get()
                    date = article.xpath(".//p//text()").get()
                    if url and title and date:
                        full_url = response.urljoin(url.strip())
                        clean_date=date.strip()
                        self.article_data_map[full_url] = {"title": title.strip(), "date": clean_date, "pdf": [full_url]}
                        article_urls.append(full_url)
                return article_urls
            else:
                for article in response.xpath("//div[@class='container']"):
                    url = article.xpath(".//a/@href").get()
                    title = article.xpath(".//a//p//text()").get()
                    date = article.xpath(".//a//dd[@class='fright cf']//span//text()").get()
                    if url and title and date:
                        full_url = response.urljoin(url.strip())
                        clean_date=date.strip()
                        self.article_data_map[full_url] = {"title": title.strip(), "date": clean_date, "pdf": [full_url]}
                        article_urls.append(full_url)
                return article_urls
        except Exception as e:
            return []
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map[response.request.meta.get('entry')].get("title")
        
    def get_body(self, response) -> str:
        return response.xpath("//div[@class='nd-con']//p//text()").getall()

    def get_images(self, response, entry=None) :
        return []

    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        return self.article_data_map[response.request.meta.get('entry')].get("date")

    def get_authors(self, response, entry=None) -> list[str]:
        return []
    
    def get_document_urls(self, response, entry=None):
        return self.article_data_map[response.request.meta.get('entry')].get("pdf")
    
    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        return None