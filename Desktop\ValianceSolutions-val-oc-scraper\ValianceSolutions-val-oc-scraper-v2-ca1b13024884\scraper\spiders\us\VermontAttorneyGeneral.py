from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime

class VermontAttorneyGeneral(OCSpider):
    name = "VermontAttorneyGeneral"

    country = "US"

    start_urls_names = {
        "https://ago.vermont.gov/blog/category/press-releases": "Press Releases",
    }
    
    charset = "utf-8"

    @property
    def language(self): 
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "America/New_York"
    
    def get_articles(self, response) -> list:
        return response.xpath('//article[@about]//h2/a/@href').getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="margin-0"]/span/text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@property="schema:text"]//p//text()').getall()) 
        
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%m-%d-%Y"
    
    def get_date(self, response) -> str:
        date_str = response.xpath('//article[@data-history-node-id]/div/div[contains(@class, "field--name-field-date")]/text()').get()
        return datetime.strptime(date_str, "%B %d, %Y").strftime("%m-%d-%Y") 
        
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]: 
        next_page =  response.xpath('//li[@class="usa-pagination__item usa-pagination__arrow"]/a[@class="usa-pagination__link usa-pagination__next-page"]/@href').get()
        if next_page:
            return response.urljoin(next_page)
        else:
            return None