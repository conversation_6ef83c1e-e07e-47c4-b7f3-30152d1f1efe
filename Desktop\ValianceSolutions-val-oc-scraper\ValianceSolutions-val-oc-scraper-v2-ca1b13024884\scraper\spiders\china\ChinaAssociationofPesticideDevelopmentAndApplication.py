import json
import re
from scraper.OCSpider import OCSpider
from scraper.middlewares import HeadlessBrowserProxy
import scrapy
from typing import List
from dotenv import load_dotenv
from scraper.utils.helper import body_normalization
from typing import Optional
import logging
load_dotenv()

class ChinaAssociationOfPesticideDevelopmentAndApplication(OCSpider):
    name = "ChinaAssociationOfPesticideDevelopmentAndApplication"

    start_urls_names={
        "https://www.zgnyxh.org.cn/djgz":"党建工作", 
        "https://www.zgnyxh.org.cn/tzgg":"通知公告", 
        "https://www.zgnyxh.org.cn/xhdt":"协会动态", 
    }

    charset = "utf-8"

    api_start_urls = {
        "https://www.zgnyxh.org.cn/djgz": {
            "url": "https://www.zgnyxh.org.cn/Designer/Common/GetData",
            "payload": {
                "dataType": "news",
                "pageIndex": "1",
                "pageSize": "10",
                "selectCategory": "389177,389179,389178,541290",
                "selectId": "",
                "dateFormater": "yyyy-MM-dd",
                "orderByField": "createtime",
                "orderByType": "desc",
                "templateId": "0",
                "postData": "",
                "es": "false",
                "setTop": "true",
                "__RequestVerificationToken": ""
            },
        },
        "https://www.zgnyxh.org.cn/tzgg": {
            "url": "https://www.zgnyxh.org.cn/Designer/Common/GetData",
            "payload": {
                "dataType": "news",
                "pageIndex": "1",
                "pageSize": "10",
                "selectCategory": "389180",
                "selectId": "",
                "dateFormater": "yyyy-MM-dd",
                "orderByField": "createtime",
                "orderByType": "desc",
                "templateId": "0",
                "postData": "",
                "es": "false",
                "setTop": "true",
                "__RequestVerificationToken": ""
            },
        },
        "https://www.zgnyxh.org.cn/xhdt": {
            "url": "https://www.zgnyxh.org.cn/Designer/Common/GetData",
            "payload": {
                "dataType": "news",
                "pageIndex": "1",
                "pageSize": "10",
                "selectCategory": "541291",
                "selectId": "",
                "dateFormater": "yyyy-MM-dd",
                "orderByField": "createtime",
                "orderByType": "desc",
                "templateId": "0",
                "postData": "",
                "es": "false",
                "setTop": "true",
                "__RequestVerificationToken": ""
            },
        },
    }

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            self.logger.error(f"No API configuration found for start_url: {start_url}")
            return
        api_url = api_data["url"]
        payload = api_data["payload"]
        payload["pageIndex"] = payload.get("pageIndex")
        yield scrapy.http.FormRequest(
            url = api_url,
            method = "POST",
            headers={
                "Content-Type": "application/x-www-form-urlencoded;",
            },
            dont_filter=True,
            formdata = payload,
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_data["url"],
                "payload": payload,
                "current_page": payload["pageIndex"]
            },
        )

    @property
    def source_type(self) -> str:
        return 'IndustryAssociation'

    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        try:
            data = json.loads(response.text)
            articles = data.get("Data" or  "data", [])
            article_urls = [
                self.construct_article_url(article)
                for article in articles
                if article
            ]
            return article_urls
        except json.JSONDecodeError as e:
            self.logger.error(f"Error decoding JSON: {e}")
            return []
        
    def get_title(self, response) -> str:
        return response.xpath("//h1[@class='w-title']/text()").get()
         
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[contains(@class,'w-detail') or contains(@class,'new-box-content')]//span//text()").getall())
    
    def get_href(self, entry) -> str:
        return entry

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_images(self, response, entry=None) -> List[str]:
        return response.xpath('//div[@class="w-detail"]//img//@src').getall()

    def get_date(self, response) -> str:
        return re.search(r"\d{4}-\d{2}-\d{2}", response.xpath('//span[@class="w-createtime-item w-createtime-date"]//text()').get()).group()

    def get_authors(self, response, entry=None) -> list[str]:
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response, current_page):
        data = response.json().get('Data' or 'data', []) 
        return str(int(current_page) + 1) if data != [] else None

    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        api_url = response.meta.get("api_url")
        if not api_url:
            logging.error("API URL not found in meta data.")
            return
        next_page = self.get_next_page(response, current_page)
        if next_page:
            payload = response.meta.get('payload')
            payload['pageIndex'] = next_page
            yield scrapy.http.FormRequest(
                url=api_url,
                method='POST',
                formdata=payload,
                headers={
                    "Content-Type": "application/x-www-form-urlencoded;",
                },
                callback=self.parse_intermediate,  
                dont_filter=True,
                meta={
                        "api_url": api_url,
                        'current_page': next_page, 
                        'start_url': start_url,
                        'payload': payload,
                    }
            )
        else:
            yield None

    def construct_article_url(self, article):
        LinkUrl = article.get('LinkUrl')   
        hbp = HeadlessBrowserProxy()
        if LinkUrl:
            return hbp.get_proxy(f"https://www.zgnyxh.org.cn{LinkUrl}", timeout=10000)  
        else: 
            return None