def body_normalization(body, delimiter='\n'):
    body_list = []
    for p in body:
        element = p.replace("\t", "").replace("\r", "").replace("\n", "").replace("\u3000", "").replace("\xa0", "").strip()
        if element != "":
            body_list.append(element)
    return delimiter.join(body_list)


def body_normalization_us(text):
    text = (text.replace("\t", "").replace("\r", "").replace("\u3000", "").replace("\xa0", "").strip())
    return text.replace('Washington, D.C. -', '')\
        .replace('Atlanta, GA—', '')\
        .replace('Washington, DC –', '')\
        .replace('Washington, D.C. –', '')\
        .replace('WILBERFORCE, OHIO–', '')\
        .replace('Tupper Lake, N.Y. -', '')\
        .replace('Greenbush, N.Y. – ', '')\
        .replace('WASHINGTON, DC – ', '')\
        .replace('WASHINGTON –', '')\
        .replace('(ATLANTA) —', '')\
        .replace('SPRINGFIELD, IL —', '')\
        .replace('(ATLANTA) –', '')\
        .replace('(WASHINGTON) –', "")\
        .replace('WASHINGTON, D.C. —', "")\
        .replace('CAHOKIA HEIGHTS, IL —', "")\
        .replace('(WASHINGTON) —', "")\
        .replace('WASHINGTON\n –', '')\
        .replace('NORTH JERSEY —', '')\
        .replace('WASHINGTON—', '')\
        .replace('Washington, D.C.–', '')\
        .replace('Nicholasville, KY–', '')\
        .replace('Duluth, GA—', '')\
        .replace('MIAMI, FL -', '')\
        .replace('TALLAHASSEE, FL –', '')\
        .replace('WASHINGTON, D.C.-', '')\
        .replace('East Greenbush, N.Y. - ', '')\
        .replace('Champlain, N.Y. -', '')\
        .replace('East Greenbush, N.Y.–', '')\
        .replace('DAYTON, OHIO–', '')\
        .replace('Rome, N.Y. -', '')\
        .replace('Norcross, GA—', '')\
        .replace('Carlisle, KY–', '')\
        .replace('STUART, Fla. -', '')\
        .replace('Flemingsburg, KY–', '')\
        .replace('Birmingham, AL —', '')\
        .replace('KEY WEST, FL -', '')\
        .replace('WASHINGTON–', '')\
        .replace('OTSEGO –', '')\
        .replace('STUART, Fla - U.S', '')\
        .replace('Lexington, KY–', '')\
        .replace('PHOENIX—', '')

SOURCE_TIER = {
    "1": [
        "CommunistPartyOfChina",
        "StateCouncil",
        "AVICIndustryFinanceMinimal",
        "peoplenews",
        "RobamScraperAlternative",
        "EuropeanCommission",
        "JakartaPost",
        "RobamMinimal",
        "WustecScraper",
        "WustecCninfoScraper",
        "WustecCninfoMinimal",
        "WustecCninfoNew",
        "WustecCninfoLocal",
        "WingtechSseMinimal",
        "WingtechSseSpider",
        "West95582Spider",
        "GacSpider",
        "NITI",
        "NHK",
        "ChinaAssociationForBarCodeTechnologyAndApplication",
        "MinistryOfForeignAffairsIndonesia",
        "tempo",
        "NationalReserveBankOfIndia",
        "xinhuameiridaily",
        "Vnanet",
        "mospi",
        "VietnamCommunistParty",
        "MediaIndonesia",
        "Kompas",
        "VoiceOfVietnam",
        "houseofrepresentatives",
        "primeministersoffice",
        "tribunnews",
        "EuropeanParliamentPositions",
        "NationalAssemblyOfVietnam",
        "BankOfJapan",
        "LiberalDemocraticParty",
        "HouseOfCouncillors",
        "MinistryOfForeignAffairsJapan",
        "cabinetoffice",
        "officialeujournal",
        "VietnamGovernmentPortal",
        "officialeujournal",
        "HouseOfRepresentatives",
        "us_house_representative",
        "us_congress_press_release",
        "EuConsiliumPressRelease",
        "xinhuanet",
        "GovtOfFrance",
        "USAdmin_woproxy",
        "USAdmin_proxy",
        "GermanyGovtSpeeches"
    ],
    "2": [
        "CentralCommissionForDisciplineInspection",
        "NationalDevelopmentAndReformCommission",
        "StateAdministrationForMarketRegulation",
        "StateOwnedAssetsSupervisionAndAdministrationCommision",
        "PeopleBankOfChina",
        "gmwnews",
        "EUCoreperAgendas",
        "EUCoreper2Agendas",
        "EUCouncilAgendaOfMeetings",
        "EUPublicVoteResults",
        "committeesofeuropeanparliament",
        "RegisterofCommissionDocumentsCommisionProposals",
        "qnb",
        "sakshi",
        "tribunnews",
        "alaskagovernor",
        "alaskagovernorminimal",
        "alaskagovernorflexible",
        "avicindustryfinance",
        "avicindustryfinanceminimal",
        "avicindustryfinanceminimal2",
        "avicindustryfinancefocused",
        "avicindustryfinanceflexible",
        "avicindustryfinancesimplified",
        "robamscraper",
        "robamscraperalternative",
        "robamminimal",
        "wusteccninfo",
        "wusteccninfominimal",
        "EuropeanParliamentNews"
    ],
    "3": [
        "sina_news",
        "yicai",
        "ChinaSecuritiesRegulatoryCommission",
        "BankingAndInsuranceRegulatoryCommission",
        "MinistryOfAgricultureAndRuralAffairs",
        "MinistryOfCommerce",
        "MinistryOfEcologyAndEnvironment",
        "MinistryOfDefense",
        "MinistryOfEducation",
        "MinistryOfFinance",
        "MinistryOfForeignAffairs",
        "MinistryOfIndustryAndInformationTechnology",
        "MinistryOfHousingAndUrbanRuralDevelopment",
        "MinistryOfJustice",
        "MinistryOfScienceAndTechnology",
        "MinistryOfTransport",
        "MinistryOfNaturalResources",
        "NationalEnergyAdministration",
        "NationalFoodAndStrategicReserveAdminstration",
        "NationalHealthcareSecurityAdministration",
        "NMPA"
    ],
    "4": [
        'FederalForeignOffice', # like this
        'zgshb',
        'CivilAviationAdministration',
        'AVICIndustryFinance',
        'COFCOGroupCoLtd',
        'AgriculturalBankofChinaCoLtd',
        'ctdnews',
        'NationalPublicComplaintsAndProposalsAdministration',
        'OfficeOfTheCentralCyberspaceAffairsCommission',
        'ccgp_site_information',
        'ChinaNonferrousMiningGroupCoLtd',
        'ChinaRailwayConstructionGroupCorporationLTD',
        'ChinaRailwayEngineeringGroupCoLtd',
        'ChinaGeneralNuclearPowerCorporationLtd',
        'UsCongressPressReleasesPattern2',
        'ctn',
        'ChinaFAWGroupCoLtd',
        'renmingongandaily',
        'ynetnews',
        'zhongguoyiyaodaily',
        'ChinaInternationalEngineeringConsultingCoLTD',
        'ChinaNationalRailwayGroupCorporationLTD',
        'ChinaThreeGorgesCorporation',
        'gongyishibao',
        'stdaily',
        'ChinaStateShipbuildingCorporation',
        'XinxingInternationalChinaGroupCoLtd',
        'ChinaOrientAssetManagementCoLtd',
        'MiningMetallurgyTechnologyGroupCoLtd',
        'ChinaDongfangElectricGroupCoLtd',
        'ChinaUnitedNetworkCommunicationsGroupCoLtd',
        'ChinaResourcesGroupCoLtd',
        'ChinaPutianInformationIndustryGroupCoLtd',
        'chinahightech',
        'USHouseRepresentativePattern1_101_150',
        'hunandaily',
        'NationalRailwayAdministration',
        'ben',
        'StateAdministrationOfTraditionalChineseMedicine',
        'ChinaAgriculturalDevelopmentGroupCoLtd',
        'henandaily',
        'NationalIntellectualPropertyAdministration',
        'zzdzb',
        'rmrbhwb',
        'ChinaSinochemGroupCoLtd',
        'Anhuidaily',
        'ningxiadaily',
        'xinhuameiridaily',
        'ChinaPetroleumNewsCenter',
        'PipelineChina',
        'CentralGovernmentBondRegistrationClearingCoLtd',
        'ChinaPostGroupCompanyLimited',
        'CECChinaElectronics',
        'bbtnews',
        'xinhuadaily',
        'ChinaPetroleumNewsCenter2',
        'GeneralInstituteMechanicalScienceResearchGroupCoLtd',
        'ElectronicsTechnologyGroupCorporation',
        'zhongguotiedaodaily',
        'ChinaInternationalDevelopmentCooperationAgency',
        'pkulaw_lar',
        'USHouseRepresentativePattern2_1_50',
        'MinistryOfWaterResources',
        'zhongguojiaoshidaily',
        'NationalImmigrationAdministration',
        'ChinaTourismGroupCoLtd',
        'peoplenews',
        'chongqingdaily',
        'xfrb',
        'ChinaHuarongAssetManagementCoLtd',
        'TaiwanWorkOffice',
        'ChinaInspectionandCertificationGroup',
        'ChinaNationalMachineryIndustryGroupCoLtd',
        'zhongguohuanjingdaily',
        'ChinaPublishingGroupCoLTD',
        'ChinaGeneralTechnologyCoLTD',
        'ChinaGoldGroupCoLtd',
        'ChinaPolyGroupCoLtd',
        'ChinaOceanShippingGroupCoLTD',
        'guizhoudaily',
        'nfrb',
        'ChinaEnergyConstructionGroupCoLTD',
        'szsb',
        'ChinaBuildingResearchInstituteCoLTD',
        'gansudaily',
        'cctv',
        'jcrb',
        'ChinaJianyinInvestmentcoLTD2',
        'zhongguojiaoyudaily',
        'cicn',
        'jjckb',
        'IndustrialCommercialBankofChinaCoLtd',
        'zhongguodianyingdaily',
        'ChinaStateConstructionGroupCoLtd',
        'shfzb',
        'USHouseRepresentativePattern1_51_100',
        'zhongguoshehuidaily',
        'zhongguoqingniandaily',
        'BankofCommunicationsCoLtd',
        'SinosteelGroupCoLTD',
        'DongfengMotorGroupCoLtd',
        'ChinaGalaxyFinancialHoldingCoLtd',
        'hubeidaily',
        'NationalCryptographyAdministration',
        'xianggangwenhuidaily',
        'ChinaNationalCoalGroupCoLTD',
        'zhongguoshuiwudaily',
        'CommissionForPublicSectorReform',
        'jjrb',
        'HongKongAndMacauAffairsOffice',
        'HongKongAndMacauAffairsOffice_1',
        'HongKongAndMacauAffairsOffice_2',
        'HongKongAndMacauAffairsOffice_3',
        'pkulaw_chl',
        'ChinaChengtongHoldingGroupCoLtd',
        'AgriculturalDevelopmentBankofChina',
        'zqrb',
        'zgshbnews',
        'ChinaNationalOffshoreOilCorporationLTD',
        'ChinaNationalAviationGroupCoLTD',
        'ChinaExportCreditInsuranceCorporation',
        'NationalCultureHeritageAdministration',
        'TheUnitedFrontWorkDepartment',
        'ChinaAviationFuelGroupCoLTD',
        'ChinaNationalChemicalCorporationLtd',
        'ChinaInvestmentCorporationLimited',
        'ChinaNationalTobaccoCorporation',
        'wangyi',
        'NationalGovernmentOfficesAdmin',
        'ChinaForestryGroupCoLtd',
        'jiangxidaily',
        'zejiangdaily',
        'AuditOffice',
        'ChinaDevelopmentInvestmentGroupCoLtd',
        'OverseasChineseTownGroupCoLtd',
        'ChinaCommunicationsConstructionGroupCoLtd',
        'ChinaDevelopmentBank',
        'ChinaGalaxyInvestmentManagementCoLtd',
        'ChinaConstructionBankLTD',
        'ChinaInternationalTechnicalIntelligenceCoLTD',
        'ChinaInformationCommunicationTechnologyGroupCoLTD',
        'ChinaRailwayMaterialsGroupCoLtd',
        'zzqyb',
        'ChinaNationalPharmaceuticalGroupCoLtd',
        'xinhuanet_2',
        'laoningdaily',
        'ChinaTobacco',
        'MinistryOfCultureAndTourism',
        'PartySchoolOfTheCentralCommitteeOfCPC',
        'ChinaCITICGroupCoLtd',
        'UsCongressPressReleasesPattern3',
        'OfficialEUJournal',
        'ChinaSaltIndustryGroupCoLtd',
        'YouyanTechnologyGroupCoLtd',
        'xinmin',
        'ChinaReinsuranceCoLtd',
        'caacmedia',
        'CentralPeopleGovernmentOfThePeopleRepublicOfChina',
        'StateSecrecyBureau',
        'ChinaSteelResearchTechnologyGroupCoLTD',
        'MinistryOfCivilAffairs',
        'cb',
        'ChinaHuanengGroupCorporation',
        'CounsellorOfficeAndCentralInstitueForCultureAndHistory',
        'ChinaCindaAssetManagementCoLtd',
        'zzyzb',
        'xinjiangdaily',
        'ChinaCoalGeologyAdministration',
        'ChinaMinmetalsCorporationLTD',
        'ChinaNanguangGroupCoLTD',
        'AnshanIronSteelGroupCoLtd',
        'StateCouncil2',
        'ChinaMerchantsGroup',
        'ChinaXDGroupCoLtd',
        'USHouseRepresentativePattern1_201_250',
        'sszn',
        'ExportImportBankofChina',
        'ChinaRailwayRollingStockCorporationLtd',
        'xizangdaily',
        'fujiandaily',
        'MinistryOfVeternalAffaires',
        'ChinaCoalTechnologyIndustryGroupCoLTD',
        'cnii',
        'zhongguoribaodaily',
        'nanfangdaily',
        'guangmingdaily',
        'ChinaRadioAndTelevisionNetworkCoLTD',
        'ccgp_procurement',
        'NationalEthnicsAffairsCommission',
        'ChinaCivilAviationInformationGroupCoLtd',
        'ChinaGreatWallAssetManagementCoLtd',
        'GeneralAdministrationMetallurgicalGeologyChina',
        'ChinaLifeInsuranceGroupCorporation',
        'cqnnews',
        'USHouseRepresentativePattern1_151_200',
        'zhongguohangtiandaily',
        'zhongguoxiaofeizhedaily',
        'zhongguominzhudaily',
        'ChinaTelecom',
        'zhongguoxinxidaily',
        'rmzxb',
        'NationalNuclearSafetyAdministration',
        'ChinaAeroEngineCorporation',
        'ChinaHuadianGroupCorporation',
        'ChinaFirstHeavyGroupCoLtd',
        'fzbnews',
        'zhongguohangkongdaily',
        'InstituteOfPartyHistoryAndLiterature',
        'sichuandaily',
        'ChinaForeignCultureGroupCoLTD',
        'MinistryOfHumanResourcesAndSocialSecurity',
        'CommissionForPoliticalAndLegalAffairs',
        'HarbinElectricGroupCoLtd',
        'qnb',
        'ChinaRailwaySignalCommunicationGroupCoLtd',
        'jingji',
        'nongmindaily',
        'ceh',
        'CommercialAircraftCorporationChinaLtd',
        'dagongdaily',
        'ChinaPetroleumChemicalCorporation',
        'ChinaEnergyConservationEnvironmentalProtectionGroupCoLtd',
        'pcn',
        'zhongguoshangbaodaily',
        'zhonghuadushudaily',
        'ChinaAerospaceScienceAndTechnologyCorporation',
        'UsCongressPressReleasesPattern1',
        'jiaotonganquanzhoukandaily',
        'StatePostBureau',
        'StateArchivesBureau',
        'gongrenribaodaily',
        'ChinaGrainReservesManagementGroupCoLTD',
        'ChinaSouthernAirlinesGroupCoLTD',
        'BankofChinaCorporationLimited',
        'ChinaBuildingMaterialsGroupCoLtd',
        'GeneralAdministrationOfSport',
        'ChinaEverbrightGroupCorporation',
        'cnfood',
        'USHouseRepresentativePattern2_101_150',
        'ChinaCivilizationNetwork',
        'ChinaNorthIndustriesGroupCorporationLimited',
        'bjd',
        'hainandaily',
        'gmwnews',
        'heilongjiangdaily',
        'CentralCommitteeOfTheCommunistPartyOfChina',
        'ChinaConstructionTechnologyCoLtd',
        'StateAdministrationOfForeignExchange',
        'ChinaJianyinInvestmentcoLTD',
        'MinistryOfEmergencyManagement',
        'jfjbmap',
        'zhongguozhengquandaily',
        'InternationalDepartment',
        'neimenggudaily',
        'StateForestryAndGrasslandAdministration',
        'xsdb',
        'PowerConstructionCorporationOfChinaLTD',
        'ChinaHualuGroupCoLTD',
        'zzzzrsb',
        'USHouseRepresentativePattern1_1_50',
        'ChinaDatangGroupCorporation',
        'NationalEnergyGroup',
        'StatePowerNivestmentCorporation',
        'zhongguoguofangdaily',
        'USHouseRepresentativePattern2_51_100',
        'PeopleInsuranceGroupCompanyOfChinaLimited',
        'cpdnews',
        'xinhuanet_1',
        'wenyibaodaily',
        'jilindaily',
        'StateAdministrationOfTaxation',
        'AluminumCorporationOfChinaLtd',
        'sciencenet',
        'tianjindaily',
        'ChinaSouthernPowerGrid',
        'NMPASpider',
        'ChinaguoxinholdingcoLTD',
        'jiefangdaily',
        'ChinaNationalChemicalEngineeringGroupCoLTD',
        'USSenatePressReleasePattern1',
        'USSenatePressReleasePattern2',
        'USSenatePressReleasePattern3',
        'USSenatePressReleasePattern4',
        'USSenatePressReleasePattern5',
        'USSenatePressReleasePattern6',
        'USSenatePressReleasePattern7',
        'zhongguowenwudaily',
        'yunnandaily',
        'PaymentsandClearingAssociationofChina',
        'NationalAssociationofFinancialMarketInstitutionalInvestors',
        'NationalInternetFinanceAssociationOfChina',
        'ChinaNationalAssociationofFinanceCompanies',
        'ChinaCertifiedTaxAgentsAssociation',
        'AssetManagementAssociationofChina',
        'ChinaTrusteeAssociation',
        'ChinaFinancingGuaranteeAssociation',
        'ChinaMicrocreditCompaniesAssociation',
        'InsuranceAssetManagementAssociationOfChina',
        'ChinaMergersAcquisitionsAssociation',
        'ChinaEducationInvestorsChamberofCommerce',
        'ChinaEnvironmentChamberofCommerce',
        'ChinaNewspaperAssociation',
        'ChinaXinhuaBookstoreAssociation',
        'ChinaPrintingTechnologyAssociation',
        'ChinaPublicRelationsAssociation',
        'ChinaCultureandEntertainmentIndustryAssociation',
        'ChinaPeriodicalAssociation',
        'PublisherAssociationofChina',
        'ChinaAssociationOfPerformingArts',
        'ChinaCultureEntertainmentIndustryAssociation',
        'ChinaEnterpriseCulturePromotionAssociation',
        'ChinaCulture',
        'ChinaMediaCulturePromotionAssociation'
        'BooksAndPeriodicalsDistributionAssociationOfChina',
        'ChinaMinistryOfCultureAndTourism',
        'ChinaPeriodicalAssociation',
        'ChinaFruitMarketingAssociation',
        'ChinaCooperationForNgoAssociation',
        'ChinaSteviaAssociation'
        'ChinaInternationalCultureAssociation',
        'ChinaPeriodicalAssociation',
        'CopyrightSocietyofChina',
        'PublisherAssociationOfChina',
        'ChinaMediaCulturePromotionAssociation',
        'ChinaScienceFilmVideoAssociation',
        'BooksAndPeriodicalsDistributionAssociationOfChina',
        'ChinaPublicCultureCenterAssociation',
        'ChinaSoftwareIndustryAssociation',
        'ChinaChamberofTourism',
        'ChinaAssociationOfRuralEnergyIndustry',
        'ChinaCustomBrokerAssociation',
        'ChinaPublicCultureCenterAssociation',
        'ChinaRadarIndustryAssociation',
        'ChinaCooperationForNgoAssociation',
        'ChinaTourismAssociation',
        'InternetSocietyOfChina',
        'ChinaNuclearIndustrySurveyDesignAssociation',
        'ChinaMunicipalEngineeringAssociation',
        'ChinaSteviaAssociation',
        'ChinaCommunicationsStandardsAssociation',
        'ChinaCommunicationsStandardsAssociation2',
        'AssociationOfChinaRareEarthIndustry',
        'ChinaInternationalExchangeAndPromotiveAssociationForMedicalAndHealthCare',
        'ChinaAssociationOfPesticideDevelopmentAndApplication',
        'ChinaQualityManagementAssociationForElectronicsIndustry',
        'ChinaAssociationDevelopmentZones',
        'ChinaFisheriesAssociation',
        'ChinaIsotopeAndRadiationAssociation',
        'ChinaMobileCommunicationsAssociation',
        'ChinaSemiconductorIndustryAssociation',
        'ChinaCivilAirportsAssociation1',
        'ChinaCivilAirportsAssociation2',
        'TheChineseEducationalArtAssociation',
        'ChinaMaritimeSafetyAssociation',
        'ChinaAssociationPromotionHealthScienceTechnology'
        'ChinaLocalRailwayAssociation',
        'AssociationForPromotionOfWestChinaResearchAndDevelopment',
        'ChinaTouristAttractionsAssociation',
        'ChinaCooperativeTradeEnterprisesAssociation',
        'ChinaAssociationOfShippingAgencies',
        'ChinaContainerIndustryAssociation',
        'ChinaNationalAssociationOfEngineeringConsultants',
        'ChinaExpressAssociation',
        'ChinaAirTransportAssociation',
        'ChinaAssociationPromotionHealthScienceTechnology',
        'ChinaPaintingsPhotographerAssociation',
        'ChinaMetrologyAssociation',
        'ChinaAssociationforEducationalTechnology',
        'ChinaChamberOfCommerceForImport',
        'ChinaAssociationOfRailwayEngneeringConstruction',
        'ChinaAssociationForQualityInspection',
        'ChinaWaterConservationAssociation'
        ],
    "5": [
        "NewAmericaFoundation",
        "CarnegieEndowmentForInternationalPeace",
        "BipartisanPolicyCenter",
        "AtlanticCouncil",
        "CenterForStrategicAndInternationalStudies",
        "WilsonCenter",
        "RANDCorporation",
        "PewResearchCenter",
        "CenterOnBudgetAndPolicyPriorities",
        "IllinoisDepartmentofCommerceandEconomicOpportunity",
        "KansasDepartmentofCommerce",
        "MarylandDepartmentofCommerce",
        "MaineDepartmentofEconomicandCommunityDevelopment",
        "LouisianaEconomicDevelopment",
        "KentuckyCabinetforEconomicDevelopment",
        "IndianaEconomicDevelopmentCorporation",
        "IdahoDepartmentofCommerce",
        "HawaiiDepartmentofBusinessEconomicDevelopmentAndTourism",
        "NewMexicoDepartmentofWorkforceSolutions",
        "NewHampshireEmploymentSecurity",
        "OhioDepartmentofJobandFamilyServices",
        "NebraskaDepartmentofLabor",
        "NorthCarolinaDepartmentofCommerce",
        "LouisianaWorkforceCommission",
        "MontanaDepartmentofLaborandIndustry",
        "AlaskaDepartmentOfLaborAndWorkforceDevelopment",
        "NevadaAttorneyGeneral",
        "KentuckyTransportationCabinet",
        "VermontAgencyofTransportation",
        "PennsylvaniaDepartmentofTransportation",
        "OregonDepartmentofTransportation",
        "NevadaDepartmentofTransportation",
        "MississippiDepartmentofTransportation",
        "NewHampshireDepartmentofTransportation",
        "NebraskaDepartmentofTransportation",
        "NorthCarolinaDepartmentofTransportation",
        "NorthDakotaDepartmentofTransportation",
        "NewYorkStateDepartmentofTransportation",
        "NewJerseyDepartmentofTransportation",
        "MissouriAttorneyGeneral",
        "MissouriDepartmentofTransportation",
        "DepartmentOfTheTreasuryNewJersey",
        "LouisianaDepartmentOfTheTreasury",
        "OfficeOfStateTreasuryArizona",
        "StateOfDelawareNews",
        "MississippiDepartmentOfFinanceAndAdministration",
        "OfficeOfTheStateTreasureMiami",
        'AlabamaStateTreasury',
        'CaliforniaStateTreasurersOffice',
        'NewMexicoStateTreasurer',
        'NorthCarolinaDepartmentOfStateTreasurer',
        'NorthDakotaStateTreasurer',
        'OhioTreasurerofState',
        'OklahomaStateTreasurer',
        'OregonStateTreasury',
        'PennsylvaniaTreasuryDepartment',
        'SouthCarolinaStateTreasurer',
        'RhodeIslandOfficeoftheGeneralTreasurer',
        'ColoradoDepartmentoftheTreasury',
        'NewYorkStateDepartmentofTaxationandFinance',
        'KansasStateTreasurer',
        'MinnesotaOfficeOfTheStateTreasurer',
        'MontanaDepartmentOfAdministration',
        'NevadaStateTreasurer',
        'IowaStateTreasurer',
        'NewJerseyDepartmentOfTreasury',
        'MichiganDepartmentOfTreasury'
        'DepartmentOfTheTreasuryNewJersey',
        'MaineOfficeOfTheStateTreasurer',
        'DelawareStateTreasury',
        'GeorgiaOfficeOfTheStateTreasurer',
        'MassachusettsStateTreasury',
        'ArizonaStateTreasurersOffice',
        'SouthDakotaStateTreasurerOffice',
        'TexasComptrollerOfPublicAccount',
        'UtahStateTreasurerOffice',
        'VermontOfficeOfTheStateTreasurer',
        'WashingtonStateTreasurerOffice',
        'WestVirginiaStateTreasurerOffice',
        'WisconsinDepartmentOfRevenue',
        'WyomingStateTreasurerOffice',
        'ConnecticutOfficeOfTheStateTreasurer',
        'IllionoisOfficeOfTheStateTreasurers',
        'NewYorkAttorneyGeneral',
        'NorthDakotaAttorneyGeneral',
        'OhioAttorneyGeneral',
        'OklahomaAttorneyGeneral',
        'GeorgiaAttorneyGeneral',
        'IowaAttorneyGeneral',
        'TennesseeAttorneyGeneral',
        'TexasComptTexasAttorneyGeneral',
        'VirginiaAttorneyGeneral',
        'WashingtonAttorneyGeneral',
        'WisconsinAttorneyGeneral',
        'AlbamaOfficeOfAttorneyGeneral',
        'CaliforniaStateOfAttorneyGeneral',
        'ConnecticutOfficeOfAttorneyGeneral',
        'ColoradoAttorneyGeneral',
        'FloridaOfficeOfAttorneyGeneral',
        'IdahoOfficeOfAttorneyGeneral',
        'NorthCarolinaAttorneyGeneral',
        'HawaiiAttorneyGeneral',
        'MichiganAttorneyGeneral',
        'OregonAttorneyGeneral',
        'MaineAttorneyGeneral',
        'MassachusettsAttorneyGeneral',
        'MississippiAttorneyGeneral',
        'MontanaAttorneyGeneral',
        'NebraskaAttorneyGeneral',
        'NewHampshireAttorneyGeneral',
        'NewJerseyAttorneyGeneral',
        'ArkansasAttorneyGeneral',
        'NewMexicoAttorneyGeneral',
        'DelawareAttorneyGeneral',
        'IdahoOfficeOfAttorneyGeneral',
        'IllinoinsAttorneyGeneral',
        'KansasAttorneyGeneral',
        'LouisianaAttorneyGeneral',
        'NorthCarolinaAttorneyGeneral',
        'IllinoisStateTreasurersOffice',
        'AlaskaAttorneyGeneral',
        'ArizonaAttorneyGeneral',
        'SouthCarolinaAttorneyGeneral',
        'SouthDakotaAttorneyGeneral',
        'RhodeIslandAttorneyGeneral',
        'RhodeIslandDepartmentOfTransportation',
        'SouthDakotaDepartmentofTransportation',
        'TennesseeDepartmentofTransportation',
        'VirginiaDepartmentOfTransportation',
        'WashingtonStateDepartmentOfTransportation',
        'WestVirginiaDepartmentOfTransportation',
        'NewYorkEmpireStateDevelopment',
        'NorthCarolinaDepartmentOfCommerce',
        'OhioDevelopmentServicesAgency',
        'OklahomaDepartmentOfCommerce',
        'PennsylvaniaDepartmentOfCommunity',
        'RhodeIslandCommerceCorporation',
        'SouthCarolinaDepartmentOfCommerce',
        'BusinessOregon',
        'NorthDakotaDepartmentOfCommerce',
        'SouthDakotaGovernorsOfficeOfEconomicDevelopment',
        'TennesseeDepartmentOfEconomic'
        ,
        'MinnesotaDepartmentOfTransportation',
        'MassachusettsDepartmentOfTransportation',
        'LoiusianaDepartmentOfTransportation',
        'MichiganDepartmentOfTransportation',
        'OregonAttorneyGeneral',
        'PennsylvaniaAttorneyGeneral',
        'RhodeIslandAttorneyGeneral',
        'UtahAttorneyGeneral',
        'VermontAttorneyGeneral',
        'WestVirginiaAttorneyGeneral',
        'NewMexicoStateDepartmentofTransportation',
        'ArizonaDepartmentOfTransportation',
        'ArkansasDepartmentOfTransportation',
        'CaliforniaDepartmentOfTransportation',
        'FloridaDepartmentOfTransportation',
        'HawaiiDepartmentOfTransportation',
        'IdahoTransportationDepartment',
        'KansasDepartmentOfTransportation',
        'WisconsinDepartmentOfTransportation',
        'WyomingDepartmentOfTransportation',
        'MaineDepartmentOfTransportation',
        'AlabamaDepartmentOfTransportation',
        'ConnecticutDepartmentOfTransportation',
        'IllinoisDepartmentOfTransportation',
        'IowaDepartmentOfTransportation',
        'AlaskaDepartmentOfTransportationAndPublicFacilities',
        'ColoradoDepartmentOfTransportation',
        'UtahDepartmentOfTransportation',
        'GeorgiaDepartmentOfTransportation',
        'WyomingAttorneyGeneral'
        'MaineDepartmentOfLabour',
        'AlbamaDepartmentOfLabour',
        'ArizonaDepartmentOfEconomicSecurity',
        'IdahoDepartmentOfLabour',
        'TexasWorkforceCommisson',
        'MichiganDepartmentOfLaborAndEconomicOpportunity',
        'MarylandDepartmentOfLabour',
        "JobServiceNorthDakota",
        "WashingtonStateEmploymentSecurityDepartment",
        "VermontDepartmentofLabor",
        "WisconsinDepartmentofWorkforceDevelopment",
        "NewJerseyDepartmentofLaborandWorkforceDevelopment",
        "GeorgiaDepartmentOfLabor",
        "ArkansasDivisionOfWorkforceServices",
        "DelawareDepartmentOfLabor",
        "ColoradoDepartmentOfLaborAndEmployment",
        "CaliforniaDepartmentOfIndustrialRelations",
        "FloridaDepartmentOfEconomicOpportunity",
        "IllinoisDepartmentOfEmploymentSecurity",
        "TennesseeDepartmentofLaborandWorkforceDevelopment",
        "MassachusettsExecutiveOfficeOfLaborAndWorkforceDevelopment",
        "IndianaDepartmentofWorkforceDevelopment",
        'VirginiaDepartmentOfTransportation',
        'WestVirginiaDepartmentOfTransportation',
        'WashingtonStateDepartmentOfTransportation',
        'TennesseeDepartmentofTransportation',
        'SouthDakotaDepartmentOfTransportation',
        'RhodeIslandDepartmentOfTransportation',
        'SouthDakotaDepartmentOfLabour',
        'TexasDepartmentOfTransportation',
        'PennsylvaniaDepartmentofLaborandIndustry',
        "MassachusettsExecutiveOfficeofHousingandEconomicDevelopment",
        "MichiganEconomicDevelopmentCorporation",
        "MississippiDevelopmentAuthority",
        "MissouriDepartmentofEconomicDevelopment",
        "MontanaDepartmentofCommerce",
        "NebraskaDepartmentofEconomicDevelopment",
        "WyomingBusinessCouncil",
        "NewJerseyEconomicDevelopmentAuthority",
        "ForeignPolicyResearchInstitute",
        "MiddleEastInstitute",
        "PetersonInstituteforInternationalEconomics",
       "NationalBureauofEconomicResearch",
       "MercatusCenter",
       "ResourcesfortheFuture",
       "CenterforDataInnovation",
       "BelferCenterforScienceandInternationalAffairs",
       "CenterforInternationalSecurityandCooperation",
       "InformationTechnologyandInnovationFoundation"
        'TexasEconomicDevelopmentCorporation',
        'UtahGovernorOfficeOfEconomicOpportunity',
        'VermontDepartmentOfEconomicDevelopment',
        'VirginiaEconomicDevelopmentPartnership',
        'WashingtonStateDepartmentOfCommerce',
        'WestVirginiaDepartmentOfEconomicDevelopment',
        'WisconsinEconomicDevelopmentCorporation',
        'AlaskaDepartmentOfCommerceCommunityAndEconomicDevelopment',
        'AlabamaDepartmentOfCommerce',
        'ArizonaCommerceAuthority',
        'ArkansasEconomicDevelopmentCommission',
        'ConnecticutDepartmentOfEconomicAndCommunityDevelopment',
        'DelawareDepartmentOfTransportation',
        'DelawareDivisionOfSmallBusiness',
        'EnterpriseFlorida',
        'GeorgiaDepartmentOfEconomicDevelopment',
        'GovernorOfficeOfBusinessAndEconomicDevelopment'
        'MaineDepartmentOfLabour'
        'GovernorOfficeOfBusinessAndEconomicDevelopment',
        'ConnecticutDepartmentOfLabor',
        'HawaiiDepartmentOfLaborAndIndustrialRelations',
        'IowaWorkforceDevelopment',
        'KansasDepartmentOfLabor',
        'MississippiDepartmentOfEmploymentSecurity',
        'MissouriDepartmentOfLaborAndIndustrialRelations',
        'NewYorkStateDepartmentOfLabor',
        'OklahomaEmploymentSecurityCommission',
        'OregonEmploymentDepartment',
        'SouthCarolinaDepartmentOfEmploymentAndWorkforce'
    ],
}


def get_source_tier(name, source=None):
    for tier, sources in SOURCE_TIER.items():
        # this will handle cases spiders are split into multiple spiders
        # and we use the same source, to identify the tier
        if source in sources or name in sources:
            return tier
    raise Exception("Spider not listed in SOURCE_TIER, please update!")


def extract_text(el):
    if el is None or "gsc-link-list" in el.get("class", ""):
        return ""
    text_parts = []
    if el.text:
        text_parts.append(el.text)
    for child in el:
        text_parts.append(extract_text(child))
        if child.tail:
            text_parts.append(child.tail)
    if el.tag == 'a':
        return f" {''.join(text_parts).strip()}({el.get('href', '')}) "
    text = ''.join(text_parts)
    if el.tag == 'p':
        text += '\n'
    return text