from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime

class NewHampshireAttorneyGeneral(OCSpider):
    name = 'NewHampshireAttorneyGeneral'

    country = "US"

    start_urls_names = {
        'https://www.doj.nh.gov/news/': "News"
    }

    HEADLESS_BROWSER_WAIT_TIME = 30000
    
    custom_settings = {
		"DOWNLOADER_MIDDLEWARES" : {
			'scraper.middlewares.HeadlessBrowserProxy': 350,
		},
		"DOWNLOAD_DELAY" : 4,
	}

    charset = "utf-8"
    
    @property
    def source_type(self) -> str:
        return 'ministry'
    
    @property
    def timezone(self):
        return "US/Eastern"
    
    @property
    def language(self):
        return "English"
    
    def get_articles(self, response) -> list:
        return response.xpath("//div[@class='title']//a/@href").getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//h1/text()").get() 
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='field field--name-body field--type-text-with-summary field--label-hidden field__item']//p/text()").getall())
    
    def get_images(self, response) -> list:
        return []
        
    def date_format(self) -> str:
        return "%B %d, %Y"

    def get_date(self, response) -> str:
        raw_date = response.xpath("//div[@class='date']//span[@class='date-label']/following-sibling::text()").get()
        if raw_date:
            cleaned_date = raw_date.replace('\n', '').strip()
            try:
                date_object = datetime.strptime(cleaned_date, '%B %d, %Y')
                return date_object.strftime('%B %d, %Y')  
            except ValueError as e:
                return None
        else:
            return None 
    
    def get_authors(self, response): 
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        next_page = response.xpath("//div[@class='container']//ul/li/a[contains(@title,'Next')]/@href").get()
        if next_page:
            return next_page
        else:
            return None