from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class IdahoDepartmentofCommerce(OCSpider):
    name = 'IdahoDepartmentofCommerce'

    country = "US"

    start_urls_names = {
        "https://commerce.idaho.gov/press-releases/": "Press Releases",
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self):
        return "US/Eastern"

    @property
    def language(self):
        return "English"

    def get_articles(self, response) -> list:
        return response.xpath("//h2//a//@href").getall()       
    
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//h1/text()").get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='entry-content']//p//text()").getall())

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%B %d, %Y"

    def get_date(self, response) -> str:
        return response.xpath("//h1//small/text()").get()

    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        next_page = response.xpath("//div[@class='nav-next alignright']//a//@href").get()
        if next_page:
            return next_page
        return None