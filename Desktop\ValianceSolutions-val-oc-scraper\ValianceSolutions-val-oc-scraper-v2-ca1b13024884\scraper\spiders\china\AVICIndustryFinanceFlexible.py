from typing import List
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
import re
from datetime import datetime
import json
from urllib.parse import urljoin

class AVICIndustryFinanceFlexible(OCSpider):
    name = "avicindustryfinanceflexible"
    
    country = "CN"
    
    # Start URL for the news page
    start_urls_names = {
        "https://www.avicindustry-finance.com/sycd/xwzx/zhcrxw/": "AVIC Industry Finance News",
    }
    
    custom_settings = {
        "DOWNLOAD_DELAY": 2,
    }
    
    charset = "utf-8"
    
    @property
    def source_type(self) -> str:
        return "Corporate"
    
    @property
    def language(self) -> str:
        return "Chinese"
    
    @property
    def timezone(self) -> str:
        return "Asia/Shanghai"
    
    def get_page_flag(self) -> bool:
        return True
    
    def get_articles(self, response) -> list:
        # Extract article URLs from the page
        # Try multiple selectors to find article links
        articles = response.xpath('//a[contains(@href, "/c/")]/@href').getall()
        
        # If no articles found, try a different selector
        if not articles:
            articles = response.xpath('//div[@class="news-list"]//a/@href').getall()
        
        # If still no articles found, try a more general selector
        if not articles:
            articles = response.xpath('//a[contains(@href, ".shtml")]/@href').getall()
        
        # Make sure all URLs are absolute
        return [response.urljoin(link) for link in articles]
    
    def get_href(self, entry) -> str:
        # Return the article URL
        return entry
    
    def get_title(self, response, entry=None) -> str:
        # Extract the article title
        # Try multiple selectors to find the title
        title = response.xpath('//h1/text()').get()
        if not title:
            title = response.xpath('//div[@class="article-title"]/text()').get()
        if not title:
            title = response.xpath('//title/text()').get()
        
        return title.strip() if title else ""
    
    def get_body(self, response, entry=None) -> str:
        # Extract the article body
        # Try multiple selectors to find the content
        body_parts = response.xpath('//div[@class="article-content"]//text()').getall()
        
        # If the above selector doesn't work, try a more general approach
        if not body_parts:
            body_parts = response.xpath('//div[contains(@class, "content")]//p/text()').getall()
        
        # If still no content, try a broader selector
        if not body_parts:
            body_parts = response.xpath('//div[contains(@class, "article")]//text()').getall()
            
        return body_normalization(body_parts)
    
    def date_format(self) -> str:
        # Define the date format used on the website
        return "%Y-%m-%d"
    
    def get_date(self, response, entry=None) -> int:
        # Extract the article date and convert to timestamp
        # Try to find the date in the article page
        date_str = response.xpath('//div[@class="article-info"]/span[@class="date"]/text()').get()
        if not date_str:
            date_str = response.xpath('//span[contains(@class, "date")]/text()').get()
        
        if date_str:
            date_str = date_str.strip()
            try:
                # Parse the date string
                date_obj = datetime.strptime(date_str, self.date_format())
                return int(date_obj.timestamp())
            except Exception as e:
                self.logger.error(f"Error parsing date: {e}")
                
        # If we can't find or parse the date, try to extract it from the URL
        try:
            url_match = re.search(r'/(\d{4})-(\d{2})-(\d{2})/', response.url)
            if url_match:
                year, month, day = url_match.groups()
                date_obj = datetime(int(year), int(month), int(day))
                return int(date_obj.timestamp())
            
            # Try another URL pattern
            url_match = re.search(r'/c/(\d{4})-(\d{2})-(\d{2})/', response.url)
            if url_match:
                year, month, day = url_match.groups()
                date_obj = datetime(int(year), int(month), int(day))
                return int(date_obj.timestamp())
        except Exception as e:
            self.logger.error(f"Error extracting date from URL: {e}")
            
        # Return current time if date not found
        return int(datetime.now().timestamp())
    
    def get_authors(self, response, entry=None) -> List[str]:
        # Extract the article authors
        authors = []
        
        # Try to find the author in the article
        author = response.xpath('//span[@class="author"]/text()').get()
        if not author:
            author = response.xpath('//div[@class="author"]/text()').get()
        if not author:
            # Try to find author in byline
            author = response.xpath('//p[contains(text(), "作者")]/text()').get()
            if author and "作者" in author:
                author = author.replace("作者", "").strip()
        
        # Check if we found an author
        if author:
            author = author.strip()
            authors.append(author)
        
        return authors
    
    def get_images(self, response, entry=None) -> List[str]:
        # Extract image URLs from the article
        images = []
        
        # Try to find images in the article content
        img_urls = response.xpath('//div[@class="article-content"]//img/@src').getall()
        if not img_urls:
            img_urls = response.xpath('//div[contains(@class, "content")]//img/@src').getall()
        if not img_urls:
            img_urls = response.xpath('//img/@src').getall()
            
        for img in img_urls:
            img_url = urljoin(response.url, img)
            images.append(img_url)
        
        return images
    
    def get_next_page(self, response) -> List[str]:
        # Extract the next page URL for pagination
        next_page = response.xpath('//a[contains(text(), "下一页")]/@href').get()
        if not next_page:
            next_page = response.xpath('//a[@class="next"]/@href').get()
        if not next_page:
            next_page = response.xpath('//a[contains(@class, "next")]/@href').get()
            
        if next_page:
            return [response.urljoin(next_page)]
            
        return []
    
    def get_document_urls(self, response, entry=None) -> list:
        # Extract document URLs (PDFs, etc.) from the article
        docs = response.xpath('//div[@class="article-content"]//a[contains(@href, ".pdf") or contains(@href, ".doc") or contains(@href, ".docx")]/@href').getall()
        
        # If no documents found, try a more general approach
        if not docs:
            docs = response.xpath('//a[contains(@href, ".pdf") or contains(@href, ".doc") or contains(@href, ".docx")]/@href').getall()
            
        return [urljoin(response.url, doc) for doc in docs]
    
    def parse(self, response):
        # Get all article URLs from the page
        article_urls = self.get_articles(response)
        
        # Follow each article URL
        for url in article_urls:
            yield response.follow(url, callback=self.parse_article)
            
        # Check for next page
        next_pages = self.get_next_page(response)
        for next_page in next_pages:
            yield response.follow(next_page, callback=self.parse)
    
    def parse_article(self, response):
        # Create a dictionary to store the article data
        article = {}
        
        # Extract the required fields
        article['url'] = response.url
        
        # Get title (if available)
        title = self.get_title(response)
        if title:
            article['title'] = title
        else:
            article['title'] = ""
        
        # Get body (if available)
        body = self.get_body(response)
        if body:
            article['body'] = body
        else:
            article['body'] = ""
        
        # Get images (if available) and convert to JSON string
        images = self.get_images(response)
        article['images'] = json.dumps(images) if images else "[]"
        
        # Get date (if available) and convert to string
        date_timestamp = self.get_date(response)
        article['date'] = datetime.fromtimestamp(date_timestamp).strftime('%Y-%m-%d %H:%M:%S')
        
        # Get document URLs (if available) and convert to JSON string
        document_urls = self.get_document_urls(response)
        article['document_urls'] = json.dumps(document_urls) if document_urls else "[]"
        
        # Get authors (if available) and convert to string
        authors = self.get_authors(response)
        article['authors'] = ', '.join(authors) if authors else ""
        
        # Increment the articles_crawled counter
        self.crawler.stats.inc_value('articles_crawled')
        self.crawler.stats.inc_value('articles_successfully_scraped')
        
        # Return the article data
        self.logger.info(f"Extracted article: {article['title']}")
        return article
