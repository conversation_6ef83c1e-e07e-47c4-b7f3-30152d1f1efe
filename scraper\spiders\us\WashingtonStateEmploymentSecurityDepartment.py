from typing import Optional
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
from datetime import datetime

class WashingtonStateEmploymentSecurityDepartment(OCSpider):
    name = 'WashingtonStateEmploymentSecurityDepartment'

    country = "US"

    start_urls_names = {
        'https://esd.wa.gov/newsroom':'News',
    }

    charset = "utf-8"

    @property
    def language(self): 
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "America/New_York"
    
    def get_articles(self, response) -> list:
        return response.xpath("//div[@class='views-field views-field-title']/h2/a/@href").getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//div[@class='content']/h1/span/text()").get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//article//div[@class='clearfix text-formatted field field--name-body field--type-text-with-summary field--label-hidden field__item']").getall())
 
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%m-%d-%Y"
    
    def get_date(self, response) -> str:
        date_str = response.xpath("//p[@class='my-3 text-gray60']/time/text()").get()
        return datetime.strptime(date_str, "%B %d, %Y").strftime(self.date_format())
        
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
     
    def get_next_page(self, response) -> Optional[str]:
        current_url = response.url
        if 'page=' in current_url:
            current_page = int(current_url.split('page=')[-1])
        else:
            current_page = 1
        next_button_exists = response.xpath('//li[@class="page-item "]/a')
        if not next_button_exists:
            return None
        next_page = current_page + 1
        next_page_url = f"https://esd.wa.gov/about-us/news-releases?page={next_page}"
        return next_page_url