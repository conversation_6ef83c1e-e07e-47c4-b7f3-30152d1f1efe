import re
from scraper.OCSpider import OCSpider
from scraper.middlewares import HeadlessBrowserProxy
import scrapy
from typing import List
from dotenv import load_dotenv
from scraper.utils.helper import body_normalization
from typing import Optional
import logging
load_dotenv()

class ChinaNationalAssociationOfEngineeringConsultants(OCSpider):
    name = "ChinaNationalAssociationOfEngineeringConsultants"

    start_urls_names = {
        "https://www.cnaec.com.cn/news/18/" : "协会要闻",                          
        "https://www.cnaec.com.cn/news/33/" : "通知公告",
        "https://www.cnaec.com.cn/news/15/" : "产业资讯",
        "https://www.cnaec.com.cn/news/28/" : "协会党支部", #This url has no api call
        "https://www.cnaec.com.cn/news/29/" : "行业动态",
        "https://www.cnaec.com.cn/news/35/" : "政策法规",  #This url has no api call
    }

    charset = "utf-8"

    #4th url and 6th url have only 1 page and no api call
    api_start_urls = {
        "https://www.cnaec.com.cn/news/18/": {
            "url": "https://www.cnaec.com.cn/comp/portalResNews/list.do?compId=portalResNews_list-16499214523827982&cid=18&pageSize=6&currentPage=1",
            "payload": {
                "compId": "portalResNews_list-16499214523827982",
                "cid": "18",
                "pageSize": "6",
                "currentPage": "1",
            },
        },
        "https://www.cnaec.com.cn/news/33/": {
            "url": "https://www.cnaec.com.cn/comp/portalResNews/list.do?compId=portalResNews_list-1651803695134&cid=33&pageSize=6&currentPage=1",
            "payload": {
                "compId": "portalResNews_list-1651803695134",
                "cid": "33",
                "pageSize": "6",
                "currentPage": "1",
            },
        },
        "https://www.cnaec.com.cn/news/15/": {
            "url": "https://www.cnaec.com.cn/comp/portalResNews/list.do?compId=portalResNews_list-1651803695134&cid=15&pageSize=6&currentPage=1",
            "payload": {
                "compId": "portalResNews_list-1651803695134",
                "cid": "15",
                "pageSize": "6",
                "currentPage": "1",
            },
        },
        "https://www.cnaec.com.cn/news/28/": {
        },
        "https://www.cnaec.com.cn/news/29/": {
            "url": "https://www.cnaec.com.cn/comp/portalResNews/list.do?compId=portalResNews_list-16685031265253334&cid=29&pageSize=10&currentPage=1",
            "payload": {
                "compId": "portalResNews_list-16685031265253334",
                "cid": "29",
                "pageSize": "10",
                "currentPage": "1",
            },
        },
        "https://www.cnaec.com.cn/news/35/": {
        },
    }

    custom_settings = {
        "DOWNLOAD_DELAY": 2,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_data=self.api_start_urls.get(start_url)
        if not api_data:
            yield scrapy.Request(
                url=start_url,
                method="GET",
                callback=self.parse,
                meta={"start_url": start_url},
            )
        else:    
            payload = api_data["payload"]
            pageSize=payload.get("pageSize")
            cid=payload.get("cid")
            compId= payload.get("compId") 
            payload["currentPage"] = payload.get("currentPage")
            url=f"https://www.cnaec.com.cn/comp/portalResNews/list.do?compId={compId}&cid={cid}&pageSize={pageSize}&currentPage={payload['currentPage']}"
            yield scrapy.FormRequest(
                url = url,
                method = "POST",
                headers={
                    "Content-Type": "text/html;",
                },
                dont_filter=True,
                formdata = payload,
                callback=self.parse,
                meta={
                    "start_url": start_url,
                    "api_url": api_data["url"],
                    "payload": payload,
                    "current_page": payload["currentPage"],
                    "compId":payload["compId"]
                },
            )

    @property
    def source_type(self) -> str:
        return 'IndustryAssociation'

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_articles(self, response) -> list:
        start_url = response.meta.get("start_url")
        api_data=self.api_start_urls.get(start_url)
        articles_urls = []
        if not api_data:
            article= response.xpath("//div[@class='p_news new-lists']//a/@href").getall()
            return article
        else:
            links = response.xpath('//a/@href').getall()
            articles_urls = []
            hbp=HeadlessBrowserProxy()
            for url in links:
                child_url=hbp.get_proxy(f"https://www.cnaec.com.cn{url}",timeout=30000)
                articles_urls.append(child_url)
            return articles_urls

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="e_title h1 p_headA"]/div/text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="e_box p_articles"]/p//text()').getall())
    
    def date_format(self) -> str:
        return"%Y-%m-%d %H:%M"
    
    def get_date(self, response) -> str:
        return response.xpath('//ul[@class="e_box p_dataSource borderT_default"]//li[4]//text()').re_first(r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}')
    
    def get_images(self, response, entry=None) -> List[str]:
        return response.xpath('//div[@class="e_box p_articles"]//img/@src').getall()
    
    def get_authors(self,entry=None) -> list[str]:
        return entry

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response,current_page ):
        current_page = int(response.meta.get('current_page'))+1
        return str(current_page)

    def go_to_next_page(self, response, start_url, current_page: Optional[str] = "1"):
        api_url = response.meta.get("api_url")
        if not api_url:
            logging.info("API URL not found in meta data.")
            return None
        next_page = self.get_next_page(response, current_page)
        if next_page:
            payload = response.meta.get('payload')
            payload['currentPage'] = next_page
            yield scrapy.FormRequest(
                url=api_url,
                method='POST',
                formdata=payload,
                headers={
                    "Content-Type": "application/x-www-form-urlencoded;",
                },
                callback=self.parse_intermediate,  
                dont_filter=True,
                meta={
                        "api_url": api_url,
                        'start_url': start_url,
                        'payload': payload,
                    }
            )
        else:
            self.logger.info(f"No more pages to crawl for start_url: {start_url}")