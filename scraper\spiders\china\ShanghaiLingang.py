from datetime import datetime
import logging
import re
from typing import Optional
import scrapy
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ShanghaiLingang(OCSpider):
    name = 'ShanghaiLingang'

    start_urls_names = {
        'http://www.lingangholding.com/news/index.shtml': 'News',
        'http://www.lingangholding.com/was5/web/index.jsp': 'Company Announcement',
    }
    
    api_start_url = {
        'http://www.lingangholding.com/was5/web/index.jsp': 'http://www.lingangholding.com/was5/web/search?channelid=212870&searchword=&keyword=&startDate=&endDate=&page={page}&token=2.1474875739839.39&_=1747982951593'
    }
    
    def parse_intermediate(self, response):
        start_url = response.meta.get('start_url')
        api_base_url = self.api_start_url.get(start_url)
        if api_base_url:
            current_page = response.meta.get("current_page", 1)
            api_url = api_base_url.format(page = current_page)
            if not api_base_url:
                self.logger.error(f"No API configuration found for start_url: {start_url}")
            else:
                self.logger.info(f"Fetching API URL: {api_url}")
            yield scrapy.Request(
                url = api_url,
                callback = self.parse,
                meta={
                    "start_url": start_url,
                    "api_url": api_base_url,
                    "current_page": current_page
                }
            )
        else:
            yield scrapy.Request(
                url = start_url,
                callback = self.parse,
                meta={
                    "start_url": start_url,
                    # "current_page": current_page
                }
            )
            
    custom_settings = {
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }
        
    article_data_map ={}
    
    charset = "iso-8859-1"

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"

    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        articles =  response.xpath('//ul[@class= "ul03"]//li//div[@class= "text"]//a/@href').getall()
        if articles:
            return articles
        else:
            data = response.json()
            articles = []
            for item in data.get("rows", []):
                url = item.get("adjunctUrl")
                title = item.get("announcementtitle")
                datetime_str = item.get("announcementtime")

                if url and title and datetime_str:
                    try:
                        # Convert "2025.04.29 00:00:00" to "2025-04-29"
                        date_obj = datetime.strptime(datetime_str.strip(), "%Y.%m.%d %H:%M:%S")
                        clean_date = date_obj.strftime("%Y-%m-%d")
                    except ValueError:
                        clean_date = datetime_str.strip()  # fallback if parsing fails

                    full_url = f"http://www.lingangholding.com/pdfResult/{url}"
                    clean_title = title.strip()

                    # Check if it's a PDF
                    if ".pdf" in full_url.lower():
                        pdf = full_url
                    else:
                        pdf = "None"

                    # Store in your article map
                    self.article_data_map[full_url] = {
                        "title": clean_title,
                        "date": clean_date,
                        "pdf": pdf
                    }

                    articles.append(full_url)

            return articles

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self,response) -> str:
        if ".pdf" in response.url.lower():
            return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
        title = response.xpath('//div[@class= "detailTitle"]//text()').get()
        return title.strip()
    
    def get_body(self, response) -> str:
        if ".pdf" in response.url.lower():
            return ""
        return body_normalization(response.xpath('//div[@class= "detailCon"]//div[@class= "TRS_Editor"]//p//text()').getall())
    
    def get_images(self, response) -> list[str]:
        if ".pdf" in response.url.lower():
            return ""
        return response.xpath('//div[@class= "detailCon"]//div[@class= "TRS_Editor"]//p//img/@src').getall()
    
    def date_format(self) -> str:
        return '%Y-%m-%d'
    
    def get_date(self, response) -> str:
        if ".pdf" in response.url.lower():
            return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        url = response.url
        match = re.search(r't(\d{8})_', url)
        if match:
            date_str = match.group(1)
            return f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:]}"
        return None

    
    def get_authors(self, response):
        return []
    
    def get_document_urls(self, response, entry=None) ->list:
        pdf_url = self.article_data_map.get(response.request.meta.get('entry'), {}).get("pdf")
        return [pdf_url] if pdf_url and pdf_url != "None" else []
    
        
    def get_page_flag(self) -> bool:
        return True

    def get_next_page(self, response, current_page) -> Optional[str]:
        start_url = response.meta.get('start_url')
        if start_url == 'http://www.lingangholding.com/was5/web/index.jsp':
            if response.status == 200:
                return current_page + 1
            else:
                return None
        else:
            if response.status == 404:
                return None
            current_url = response.url
            print("the current url is:", current_url)
            if "index.shtml" in current_url:
                next_page_url = current_url.replace("index.shtml", "index_1.shtml")
            else:
                import re
                match = re.search(r"index_(\d+)\.shtml", current_url)
                if match:
                    current_page_num = int(match.group(1))
                    next_page_num = current_page_num + 1
                    next_page_url = current_url.replace(
                        f"index_{current_page_num}.shtml",
                        f"index_{next_page_num}.shtml"
                    )
                else:
                    return None
            return next_page_url
    
    def go_to_next_page(self, response, start_url, current_page):
        api_url = response.meta.get("api_url")
        next_page = self.get_next_page(response, current_page)
        print("THE VALUE OF NEXT PAGE IS:", next_page)
        if not api_url: 
            if next_page is not None:
                self.crawler.stats.inc_value("pagination_stats__pages_crawled")
                next_page = response.urljoin(next_page)
                request = response.request.replace(url=next_page, callback=self.parse)
                request.meta['start_url'] = start_url
                request.meta['current_page'] = int(response.request.meta.get('current_page', 1)) + 1
                yield request
            else:
                logging.info("No more pages to fetch.")
                return
        if next_page:
            yield scrapy.Request(
                url=api_url,
                callback=self.parse_intermediate,
                meta={'current_page': next_page, 'start_url': start_url}
            )
        else:
            logging.info("No more pages to fetch.")
            return