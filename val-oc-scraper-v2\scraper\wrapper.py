import logging
import os
import traceback
from pathlib import Path

from dotenv import load_dotenv

from scraper.items import ArticleItem, OfficialLineItem


def error_safe(func):
    '''Decorator that interecept error so it can be saved to bq error table'''

    def wrap(*args, **kwargs):
        try:
            return {"success": True, "value": func(*args, **kwargs)}
        except Exception:
            logging.error(traceback.format_exc())
            return {"success": False, "value": traceback.format_exc()}

    return wrap

def save_error(error_pipeline, object, pipeline_source):

    try:
        print(object.get("url"))
        print(object.get("error"))
        error_pipeline.save_error(object.to_error_article_json(), pipeline_source)
        logging.error(f"{pipeline_source} : {object.to_error_article_json()}")
    except Exception as e:
        json_data = object.to_error_article_json()
        json_data['exception'] = str(traceback.format_exc())
        append_new_line("/tmp/scrapy_error.log", str(json_data))


def append_new_line(file_name, text_to_append):
    """Append given text as a new line at the end of file"""
    # Open the file in append & read mode ('a+')
    with open(file_name, "a+") as file_object:
        # Move read cursor to the start of file.
        file_object.seek(0)
        # If file is not empty then append '\n'
        data = file_object.read(100)
        if len(data) > 0:
            file_object.write("\n")
        # Append text at the end of file
        file_object.write(text_to_append)

def build_article(start_url, id, cls, response) -> ArticleItem:
    article = ArticleItem()

    article['start_url'] = start_url

    if "uscongresspressreleases" in cls.name.lower() or "ushouserepresentative" in cls.name.lower():
        article['source'] = "us_congress_press_release"
    # some times we have HongKongAndMacauAffairsOffice_1, HongKongAndMacauAffairsOffice_2, HongKongAndMacauAffairsOffice_3
    # therefore we use the attribute `source` to get the name of the source
    elif getattr(cls, "source", None):
        article['source'] = cls.source
    else:
        article['source'] = "xinhuanet" if cls.name == "xinhuanet_2" else cls.name

    article['source_name'] = cls.start_urls_names.get(start_url)

    article['source_type'] = cls.source_type

    article['id'] = id

    is_from_splash = response.request.meta.get("from_splash", False)

    article['url'] = response.request.meta.get("article_url") if is_from_splash else response.request.url

    # article['raw_html'] = response.body.decode(cls.charset)
    if "www.spic.com.cn" in start_url:
        article['raw_html'] = response.body.decode("ISO-8859-1")
    else:
        article['raw_html'] = response.body.decode(cls.charset)

    return article

def build_official_line_article(start_url, id, cls, response, article_index) -> OfficialLineItem:
    article = OfficialLineItem()

    article['id'] = id # id contains article url

    article['article_url'] = response.request.url

    article['start_url'] = response.request.url

    article['url'] = response.request.url

    article['article_number'] = response.meta['index']

    article['newspaper'] = "xinhuanet" if cls.name.lower() == "xinhuanet_1" else cls.name.lower()

    return article


def load_env():
    ENV_PATH = os.getenv("ENV_PATH", f"{Path(os.path.dirname(__file__)).parent}/.env")

    logging.info(f"Env Path is {ENV_PATH}")

    load_dotenv(ENV_PATH)
