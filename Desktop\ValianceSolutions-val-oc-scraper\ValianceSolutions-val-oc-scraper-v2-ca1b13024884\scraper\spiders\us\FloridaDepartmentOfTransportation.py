from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime 

class FloridaDepartmentOfTransportation(OCSpider):
    name = "FloridaDepartmentOfTransportation"
    
    country = "US"
    
    start_urls_names = {
        "https://www.fdot.gov/info/newsroom.shtm": "News",
    }
    
    charset = "utf-8"
    
    visited_links = set()  # Keep track of visited URLs to avoid reprocessing

    @property
    def language(self): 
        return "English"
     
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "America/New_York"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="sfContentBlock"]//a/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        title = response.xpath('//h4[@align="center" and contains(@style, "text-align:center")]/strong/strong/text()').get() \
            or response.xpath('//h4[@align="center"]/strong/text()').get()
        return title
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="container"]//p | //div[@class="container"]//ul | //div[@class="container"]//h4').getall())

    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return"%m-%d-%Y"
    
    def get_date(self, response) -> Optional[str]:
        date_str = response.xpath('//div[@class="sfContentBlock"]//p/br/following-sibling::text()').get()
        date_obj = datetime.strptime(date_str, "%B %d, %Y")
        return date_obj.strftime("%m-%d-%Y")
       
    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        next_page = response.xpath('//div[@id="Contentplaceholder1_C017_Col01"]//ul/li/a/@href').get()
        return next_page  