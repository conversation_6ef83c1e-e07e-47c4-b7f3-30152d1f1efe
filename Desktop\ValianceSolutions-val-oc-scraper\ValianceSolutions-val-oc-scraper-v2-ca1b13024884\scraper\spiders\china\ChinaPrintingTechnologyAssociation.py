from typing import Optional
from scraper.OCSpider import OCSpider
from bs4 import BeautifulSoup
from scraper.utils.helper import body_normalization

class ChinaPrintingTechnologyAssociation(OCSpider):
    name = "ChinaPrintingTechnologyAssociation"

    start_urls_names = {
        "https://www.cnprint.org.cn/a/xhfs/xhgg/": "协会公告",
        "https://www.cnprint.org.cn/a/xhfs/xhdt/": "协会动态",
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"

    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        return response.xpath("//ul[@class='e2']//li//a[@class='title']/@href").getall()
    
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        title = response.xpath("//h1/text()").get()
        return title if title else None
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='content']//p//text()").getall())
    
    def get_images(self, response) -> list:
        images = []
        for imgTag in BeautifulSoup(self.get_body(response), features="lxml").find_all('img'):
            images.append(response.urljoin(imgTag['src']))
        return images
    
    def get_document_urls(self, response, entry=None) -> list:
        documents = response.xpath("//p//a/@href").getall()
        absolute_documents = [response.urljoin(doc) for doc in documents]
        return absolute_documents
    
    def date_format(self) -> str:
        return"%Y-%m-%d %H:%M"
    
    def get_date(self, response) -> str:
        return response.xpath("//div[@class='info']/text()").re_first(r"\d{4}-\d{2}-\d{2} \d{2}:\d{2}")
    
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        next_page = response.xpath("//ul/li/a[contains(text(), '下一页')]/@href").get()
        if next_page:
            return response.urljoin(next_page)
        return None