from typing import Optional
from scraper.OCSpider import OCSpider
from bs4 import BeautifulSoup
from scraper.utils.helper import body_normalization

class ShanxiXinghuacunFenjiuDistillery(OCSpider):
    name = "ShanxiXinghuacunFenjiuDistillery"

    start_urls_names = {
        "https://www.fenjiu.com.cn/gf/corporateNews/index.html" : "新闻资讯",
        "https://www.fenjiu.com.cn/gf/media/index.html" : "新闻资讯",
        "https://www.fenjiu.com.cn/gf/disclosure/index.html" : "信息披露" 
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"

    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        return response.xpath('//ul[@class="news_list clearfix position_r"]/li//a/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//div[@class="news_detail_content_title"]/text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="news_detail_content_text"]//text()').getall())
    
    def get_images(self, response) -> list:
        return response.xpath('//div[@class="news_detail_content_text"]//img/@src').getall()
    
    def date_format(self) -> str:
        return"%Y-%m-%d"
    
    def get_date(self, response) -> str:
        return response.xpath('//div[@class="news_detail_bread"]//span//text()').re_first(r"\d{4}-\d{2}-\d{2}")
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return True
    
    def get_next_page(self, response) -> Optional[str]:
        if response.status == 404:
            return None
        current_url = response.url
        if "index.html" in current_url:
            # First page (index.html), next will be index_2.html
            next_page_url = current_url.replace("index.html", "index_2.html")
        else:
            # Subsequent pages: increment the page number
            import re
            match = re.search(r"index_(\d+)\.html", current_url)
            if match:
                current_page_num = int(match.group(1))
                next_page_num = current_page_num + 1
                next_page_url = current_url.replace(
                    f"index_{current_page_num}.html",
                    f"index_{next_page_num}.html"
                )
            else:
                # Cannot find page number, stop
                return None
        return next_page_url