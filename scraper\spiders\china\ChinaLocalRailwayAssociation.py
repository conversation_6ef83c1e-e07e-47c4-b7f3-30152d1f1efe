import json
import re
from scraper.OCSpider import OCSpider
from scraper.middlewares import HeadlessBrowserProxy
import scrapy
from typing import List
from dotenv import load_dotenv
from scraper.utils.helper import body_normalization
from typing import Optional
import logging
load_dotenv()

class ChinaLocalRailwayAssociation(OCSpider):
    name="ChinaLocalRailwayAssociation"

    start_urls_names={
        "http://www.china-dftlxh.cn/Tnews.html?id=3":"党建时政", 
        "http://www.china-dftlxh.cn/TindustryInformationList.html?id=5":"行业资讯",
        "http://www.china-dftlxh.cn/Tnotice.html?id=11&subId=40":"通知公告", 
    }

    charset = "utf-8"

    api_start_urls = {
        "http://www.china-dftlxh.cn/Tnews.html?id=3": {
            "url": "http://www.china-dftlxh.cn/railway/tCmsContent/api/getContentList",
            "payload": {
                "id": "89",
                "pageNumber": "1",
                "pageSize": "10"
            },
        },
        "http://www.china-dftlxh.cn/TindustryInformationList.html?id=5": {
            "url": "http://www.china-dftlxh.cn/railway/tCmsContent/api/getContentList",
            "payload": {
                "id": "26",
                "pageNumber": "1",
                "pageSize": "10"
            },
        },
        "http://www.china-dftlxh.cn/Tnotice.html?id=11&subId=40": {
            "url": "http://www.china-dftlxh.cn/railway/tCmsNotice/api/listActive",
            "payload": {
                "id": "40",
                "pageNumber": "1",
                "pageSize": "10"
            },
        },
    }

    @property
    def source_type(self) -> str:
        return 'IndustryAssociation'

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            self.logger.error(f"No API configuration found for start_url: {start_url}")
            return
        api_url = api_data["url"]
        payload = api_data["payload"]
        payload["pageNumber"] = payload.get("pageNumber")
        yield scrapy.FormRequest(
            url = api_url,
            method = "POST",
            headers={
                "Content-Type": "application/x-www-form-urlencoded;",
            },
            dont_filter=True,
            formdata = payload,
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_data["url"],
                "payload": payload,
                "current_page": payload["pageNumber"]
            },

        )

    def get_articles(self, response) -> list:
        try:
            main_section_id = response.meta.get("payload").get("id")
            data = json.loads(response.text)
            articles = data.get("data", [])
            article_urls = [
                self.construct_article_url(article,main_section_id)
                for article in articles
                if article
            ]
            return article_urls
        except json.JSONDecodeError as e:
            self.logger.error(f"Error decoding JSON: {e}")
            return []
        
    def get_href(self, entry) -> str:
        return entry   

    def get_title(self, response) -> str:
        return response.xpath("//*[@class='article-title']//text()").get() 
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//*[contains(@class, 'rm_txt_con cf') or contains(@id, 'textBox')]//text()").getall())

    def get_images(self, response, entry=None) -> List[str]:
        return response.xpath("//*[contains(@class, 'rm_txt_con cf') or contains(@id, 'textBox')]//img/@src").getall()
    
    def get_authors(self, response, entry=None) -> list[str]:
        author = response.xpath("//*[@class='author']//text()").getall()
        return author or [text.split("来源：", 1)[-1].strip() for text in response.xpath("//*[contains(@class, 'author-set')]//text()").getall() if "来源：" in text][:1]

    def get_document_urls(self, response, entry=None) -> list:
        return response.xpath('//embed//@src').getall()

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response) -> str:
        date = response.xpath("//div[contains(@class, 'details-box')]//*[@id='updateTime']//text()").get()
        return re.search(r"\d{4}-\d{2}-\d{2}", date).group()

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response, current_page):
        data = response.json().get('data', [])
        return str(int(current_page) + 1) if data!=[] else None

    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        api_url = response.meta.get("api_url")
        if not api_url:
            return
        next_page = self.get_next_page(response, current_page)
        if next_page:
            payload = response.meta.get('payload')
            payload['pageNumber'] = next_page
            yield scrapy.FormRequest(
                url=api_url,
                method='POST',
                formdata=payload,
                headers={
                    "Content-Type": "application/x-www-form-urlencoded;",
                },
                callback=self.parse_intermediate,  # callback parse_intermediate
                dont_filter=True,
                meta={
                        "api_url": api_url,
                        'current_page': next_page, 
                        'start_url': start_url,
                        'payload': payload,
                    }
            )
        else:
            yield None
                    
    def construct_article_url(self, article,main_section_id):
        hbp = HeadlessBrowserProxy()
        article_id = article.get('id')
        if article_id:
            return hbp.get_proxy(f"http://www.china-dftlxh.cn/Tdetails.html?id={main_section_id}&detailsId={article_id}",timeout=30000)   # Eg: "article url": "https://www.capa.com.cn/#/index/NewsDetail?activeName=%E8%A1%8C%E4%B8%9A%E5%B9%B4%E6%8A%A5&id=1859123183003656193"
        else:
            return None