import scrapy.selector
from scraper.OCSpider import OCSpider
import scrapy
from dotenv import load_dotenv
from scraper.utils.helper import body_normalization
from typing import Optional
import logging
load_dotenv()

class ChinaChamberOfCommerceForImport(OCSpider):
    name = "ChinaChamberOfCommerceForImport"

    start_urls_names = {
        "https://www.cccmhpie.org.cn/ShowNewsList.aspx?QueryStr=x08x12o8q7x09x01w1y1830y0250z5201z8395zO3w8w1u9v2v2vZO3x10x02x11p4x2X12x01w1u8z8p2x01q9p4x2X12x01w1u9z8w7x08q7x15x15p3x0X14x18x0X14o3w8w1p3p9p3p3x0X14x18x0X14z8w7x08q7x15x15p4q7q8x08x01o8q7x09x01w1p3x2X15q5w7x08q7x15x15z8p5x10x05x13x17x01o3w8w1z8w8q7x16q7p3x0X14x18x0X14o3w8w1p3p9p3p3x0X14x18x0X14z8w8q7x16q7p4q7q8x08x01o8q7x09x01w1w8x11q9q5o0x05x14x15x16pQ7x03x01z8x00x0X15q9p5x10x05x13x17x01o3w8w1u9v2v2vZ8p2x1X1X16w7x08q7x15x15o3w8w1v7u8u9v5z8w7x08q7x15x15o3w8w1u9v2v2vZ8z2o6x05x10x07o3w8w1u9v2v2v3" : "商会要闻",                     
        "https://www.cccmhpie.org.cn/ShowNewsList.aspx?QueryStr=x08x12o8q7x09x01w1z6890z0693y0844y1578zO3w8w1vV1v0vZO3x10x02x11p4x2X12x01w1u8z8p2x01q9p4x2X12x01w1u9z8w7x08q7x15x15p3x0X14x18x0X14o3w8w1p3p9p3p3x0X14x18x0X14z8w7x08q7x15x15p4q7q8x08x01o8q7x09x01w1p3x2X15q5w7x08q7x15x15z8p5x10x05x13x17x01o3w8w1z8w8q7x16q7p3x0X14x18x0X14o3w8w1p3p9p3p3x0X14x18x0X14z8w8q7x16q7p4q7q8x08x01o8q7x09x01w1w8x11q9q5o0x05x14x15x16pQ7x03x01z8x00x0X15q9p5x10x05x13x17x01o3w8w1vV1v0vZ8p2x1X1X16w7x08q7x15x15o3w8w1v7u8u9v5z8w7x08q7x15x15o3w8w1vV1v0vZO6x05x10x07o3w8w1vV1v0v3" : "通知公告",
        "https://www.cccmhpie.org.cn/ShowNewsList.aspx?QueryStr=x08x12o8q7x09x01w1z4892x9994z6164z5759zO3w8w1u9v5v5v5zO3x10x02x11p4x2X12x01w1u8z8p2x01q9p4x2X12x01w1u9z8w7x08q7x15x15p3x0X14x18x0X14o3w8w1p3p9p3p3x0X14x18x0X14z8w7x08q7x15x15p4q7q8x08x01o8q7x09x01w1p3x2X15q5w7x08q7x15x15z8p5x10x05x13x17x01o3w8w1z8w8q7x16q7p3x0X14x18x0X14o3w8w1p3p9p3p3x0X14x18x0X14z8w8q7x16q7p4q7q8x08x01o8q7x09x01w1w8x11q9q5o0x05x14x15x16pQ7x03x01z8x00x0X15q9p5x10x05x13x17x01o3w8w1u9v5v5v5z8p2x1X1X16w7x08q7x15x15o3w8w1v7u8u9v5z8w7x08q7x15x15o3w8w1u9v5v5v5zO6x05x10x07o3w8w1u9v5v5v5" : "行业资讯",  
        "https://www.cccmhpie.org.cn/ShowNewsList.aspx?QueryStr=x08x12o8q7x09x01w1y2269z8469y1160y4577zO3w8w1u9v5v5v1zO3x10x02x11p4x2X12x01w1u8z8p2x01q9p4x2X12x01w1u9z8w7x08q7x15x15p3x0X14x18x0X14o3w8w1p3p9p3p3x0X14x18x0X14z8w7x08q7x15x15p4q7q8x08x01o8q7x09x01w1p3x2X15q5w7x08q7x15x15z8p5x10x05x13x17x01o3w8w1z8w8q7x16q7p3x0X14x18x0X14o3w8w1p3p9p3p3x0X14x18x0X14z8w8q7x16q7p4q7q8x08x01o8q7x09x01w1w8x11q9q5o0x05x14x15x16pQ7x03x01z8x00x0X15q9p5x10x05x13x17x01o3w8w1u9v5v5v1z8p2x1X1X16w7x08q7x15x15o3w8w1v7u8u9v5z8w7x08q7x15x15o3w8w1u9v5v5v1zO6x05x10x07o3w8w1u9v5v5v1" : "国际动态",
    }

    charset = "gb2312"

    custom_settings = {
        "DOWNLOAD_DELAY": 2,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    # Common payload shared by all URLs
    common_payload = {
        "__VIEWSTATE": "/wEPDwULLTIxMzY2NjgyODFkZDJkXuFt0vMoXx9tD8zNZV070avxe8SgSpXjcFR0KLLS",
        "__VIEWSTATEGENERATOR": "0B2C1791",
        "Txt_Search": "",
        "swGridView_sortHidden": "",
        "swGridView_CurrentPageIndex": "0",
        "swGridView_SelectedValues": "",
        "swGridView_currentUniqueID": "",
        "swGridView_pageNum": "1",
    }

    api_start_urls = {
        "https://www.cccmhpie.org.cn/ShowNewsList.aspx?QueryStr=x08x12o8q7x09x01w1y1830y0250z5201z8395zO3w8w1u9v2v2vZO3x10x02x11p4x2X12x01w1u8z8p2x01q9p4x2X12x01w1u9z8w7x08q7x15x15p3x0X14x18x0X14o3w8w1p3p9p3p3x0X14x18x0X14z8w7x08q7x15x15p4q7q8x08x01o8q7x09x01w1p3x2X15q5w7x08q7x15x15z8p5x10x05x13x17x01o3w8w1z8w8q7x16q7p3x0X14x18x0X14o3w8w1p3p9p3p3x0X14x18x0X14z8w8q7x16q7p4q7q8x08x01o8q7x09x01w1w8x11q9q5o0x05x14x15x16pQ7x03x01z8x00x0X15q9p5x10x05x13x17x01o3w8w1u9v2v2vZ8p2x1X1X16w7x08q7x15x15o3w8w1v7u8u9v5z8w7x08q7x15x15o3w8w1u9v2v2vZ8z2o6x05x10x07o3w8w1u9v2v2v3": {
            "url": "https://www.cccmhpie.org.cn/ShowNewsList.aspx?QueryStr=x08x12o8q7x09x01w1y1830y0250z5201z8395zO3w8w1u9v2v2vZO3x10x02x11p4x2X12x01w1u8z8p2x01q9p4x2X12x01w1u9z8w7x08q7x15x15p3x0X14x18x0X14o3w8w1p3p9p3p3x0X14x18x0X14z8w7x08q7x15x15p4q7q8x08x01o8q7x09x01w1p3x2X15q5w7x08q7x15x15z8p5x10x05x13x17x01o3w8w1z8w8q7x16q7p3x0X14x18x0X14o3w8w1p3p9p3p3x0X14x18x0X14z8w8q7x16q7p4q7q8x08x01o8q7x09x01w1w8x11q9q5o0x05x14x15x16pQ7x03x01z8x00x0X15q9p5x10x05x13x17x01o3w8w1u9v2v2vZ8p2x1X1X16w7x08q7x15x15o3w8w1v7u8u9v5z8w7x08q7x15x15o3w8w1u9v2v2vZ8z2o6x05x10x07o3w8w1u9v2v2v3",
            "payload": common_payload    
        },
        "https://www.cccmhpie.org.cn/ShowNewsList.aspx?QueryStr=x08x12o8q7x09x01w1z6890z0693y0844y1578zO3w8w1vV1v0vZO3x10x02x11p4x2X12x01w1u8z8p2x01q9p4x2X12x01w1u9z8w7x08q7x15x15p3x0X14x18x0X14o3w8w1p3p9p3p3x0X14x18x0X14z8w7x08q7x15x15p4q7q8x08x01o8q7x09x01w1p3x2X15q5w7x08q7x15x15z8p5x10x05x13x17x01o3w8w1z8w8q7x16q7p3x0X14x18x0X14o3w8w1p3p9p3p3x0X14x18x0X14z8w8q7x16q7p4q7q8x08x01o8q7x09x01w1w8x11q9q5o0x05x14x15x16pQ7x03x01z8x00x0X15q9p5x10x05x13x17x01o3w8w1vV1v0vZ8p2x1X1X16w7x08q7x15x15o3w8w1v7u8u9v5z8w7x08q7x15x15o3w8w1vV1v0vZO6x05x10x07o3w8w1vV1v0v3": {
            "url": "https://www.cccmhpie.org.cn/ShowNewsList.aspx?QueryStr=x08x12o8q7x09x01w1z6890z0693y0844y1578zO3w8w1vV1v0vZO3x10x02x11p4x2X12x01w1u8z8p2x01q9p4x2X12x01w1u9z8w7x08q7x15x15p3x0X14x18x0X14o3w8w1p3p9p3p3x0X14x18x0X14z8w7x08q7x15x15p4q7q8x08x01o8q7x09x01w1p3x2X15q5w7x08q7x15x15z8p5x10x05x13x17x01o3w8w1z8w8q7x16q7p3x0X14x18x0X14o3w8w1p3p9p3p3x0X14x18x0X14z8w8q7x16q7p4q7q8x08x01o8q7x09x01w1w8x11q9q5o0x05x14x15x16pQ7x03x01z8x00x0X15q9p5x10x05x13x17x01o3w8w1vV1v0vZ8p2x1X1X16w7x08q7x15x15o3w8w1v7u8u9v5z8w7x08q7x15x15o3w8w1vV1v0vZO6x05x10x07o3w8w1vV1v0v3",
            "payload": common_payload
        },
        "https://www.cccmhpie.org.cn/ShowNewsList.aspx?QueryStr=x08x12o8q7x09x01w1z4892x9994z6164z5759zO3w8w1u9v5v5v5zO3x10x02x11p4x2X12x01w1u8z8p2x01q9p4x2X12x01w1u9z8w7x08q7x15x15p3x0X14x18x0X14o3w8w1p3p9p3p3x0X14x18x0X14z8w7x08q7x15x15p4q7q8x08x01o8q7x09x01w1p3x2X15q5w7x08q7x15x15z8p5x10x05x13x17x01o3w8w1z8w8q7x16q7p3x0X14x18x0X14o3w8w1p3p9p3p3x0X14x18x0X14z8w8q7x16q7p4q7q8x08x01o8q7x09x01w1w8x11q9q5o0x05x14x15x16pQ7x03x01z8x00x0X15q9p5x10x05x13x17x01o3w8w1u9v5v5v5z8p2x1X1X16w7x08q7x15x15o3w8w1v7u8u9v5z8w7x08q7x15x15o3w8w1u9v5v5v5zO6x05x10x07o3w8w1u9v5v5v5": {
            "url": "https://www.cccmhpie.org.cn/ShowNewsList.aspx?QueryStr=x08x12o8q7x09x01w1z4892x9994z6164z5759zO3w8w1u9v5v5v5zO3x10x02x11p4x2X12x01w1u8z8p2x01q9p4x2X12x01w1u9z8w7x08q7x15x15p3x0X14x18x0X14o3w8w1p3p9p3p3x0X14x18x0X14z8w7x08q7x15x15p4q7q8x08x01o8q7x09x01w1p3x2X15q5w7x08q7x15x15z8p5x10x05x13x17x01o3w8w1z8w8q7x16q7p3x0X14x18x0X14o3w8w1p3p9p3p3x0X14x18x0X14z8w8q7x16q7p4q7q8x08x01o8q7x09x01w1w8x11q9q5o0x05x14x15x16pQ7x03x01z8x00x0X15q9p5x10x05x13x17x01o3w8w1u9v5v5v5z8p2x1X1X16w7x08q7x15x15o3w8w1v7u8u9v5z8w7x08q7x15x15o3w8w1u9v5v5v5zO6x05x10x07o3w8w1u9v5v5v5",
            "payload": common_payload
        },
        "https://www.cccmhpie.org.cn/ShowNewsList.aspx?QueryStr=x08x12o8q7x09x01w1y2269z8469y1160y4577zO3w8w1u9v5v5v1zO3x10x02x11p4x2X12x01w1u8z8p2x01q9p4x2X12x01w1u9z8w7x08q7x15x15p3x0X14x18x0X14o3w8w1p3p9p3p3x0X14x18x0X14z8w7x08q7x15x15p4q7q8x08x01o8q7x09x01w1p3x2X15q5w7x08q7x15x15z8p5x10x05x13x17x01o3w8w1z8w8q7x16q7p3x0X14x18x0X14o3w8w1p3p9p3p3x0X14x18x0X14z8w8q7x16q7p4q7q8x08x01o8q7x09x01w1w8x11q9q5o0x05x14x15x16pQ7x03x01z8x00x0X15q9p5x10x05x13x17x01o3w8w1u9v5v5v1z8p2x1X1X16w7x08q7x15x15o3w8w1v7u8u9v5z8w7x08q7x15x15o3w8w1u9v5v5v1zO6x05x10x07o3w8w1u9v5v5v1": {
            "url": "https://www.cccmhpie.org.cn/ShowNewsList.aspx?QueryStr=x08x12o8q7x09x01w1y2269z8469y1160y4577zO3w8w1u9v5v5v1zO3x10x02x11p4x2X12x01w1u8z8p2x01q9p4x2X12x01w1u9z8w7x08q7x15x15p3x0X14x18x0X14o3w8w1p3p9p3p3x0X14x18x0X14z8w7x08q7x15x15p4q7q8x08x01o8q7x09x01w1p3x2X15q5w7x08q7x15x15z8p5x10x05x13x17x01o3w8w1z8w8q7x16q7p3x0X14x18x0X14o3w8w1p3p9p3p3x0X14x18x0X14z8w8q7x16q7p4q7q8x08x01o8q7x09x01w1w8x11q9q5o0x05x14x15x16pQ7x03x01z8x00x0X15q9p5x10x05x13x17x01o3w8w1u9v5v5v1z8p2x1X1X16w7x08q7x15x15o3w8w1v7u8u9v5z8w7x08q7x15x15o3w8w1u9v5v5v1zO6x05x10x07o3w8w1u9v5v5v1",
            "payload": common_payload,
        },
    }

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")      
        api_data=self.api_start_urls.get(start_url)
        api_url = api_data["url"]
        if not api_url:
            self.logger.error(f"No API configuration found for start_url: {start_url}")
        else:
            payload = api_data["payload"]
            current_page_index = payload.get("swGridView_CurrentPageIndex")
            url=f"{api_url}&__VIEWSTATE=/wEPDwULLTIxMzY2NjgyODFkZDJkXuFt0vMoXx9tD8zNZV070avxe8SgSpXjcFR0KLLS&__VIEWSTATEGENERATOR=0B2C1791&swGridView_CurrentPageIndex={current_page_index}"
        yield scrapy.FormRequest(
            url=url,
            method="POST",
            dont_filter=True,
            formdata=payload,
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_data["url"],
                "payload": payload,
                "current_page": current_page_index
            },
        )
    
    @property
    def source_type(self) -> str:
        return "IndustryAssociation"
    
    @property
    def timezone(self): 
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        article_url=[]
        for item in response.xpath('//td[@class="DocTitle"]'):
            link=item.xpath('.//a/@href').get()
            url=f"https://www.cccmhpie.org.cn{link}"
            if url is not None:
                article_url.append(url) 
        return article_url
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h3[@class="head"]//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="pagesContent"]//p//text()').getall())
    
    def get_images(self, response, entry=None):
        return response.xpath('//div[@class="pagesContent"]//p//img/@src').getall()
    
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self,response):
       date_str = response.xpath('//span[@class="time"]//text()').get()
       if date_str:
        # Cleaning the date by replacing the Chinese characters
        cleaned_date_str = date_str.replace('年', '-').replace('月', '-').replace('日', '')
        return cleaned_date_str
    
    def get_authors(self,entry=None):
        return entry
    
    def get_page_flag(self):
        return False
    
    def get_next_page(self, response,current_page ):
        last_page = response.xpath('//a[contains(text(), "末页")]/@onclick').re(r'GoToPage\((\d+),')
        max_pages=int(last_page[0])
        current_page = int(response.meta.get('current_page'))+1
        return str(current_page) if current_page<max_pages else None
    
    def go_to_next_page(self, response, start_url, current_page: Optional[str] = "1"):
        api_url = response.meta.get("api_url")
        if not api_url:
            logging.info("API URL not found in meta data.")
            return None
        next_page = self.get_next_page(response, current_page)
        if next_page:
            payload = response.meta.get('payload')
            payload["swGridView_CurrentPageIndex"]= next_page
            yield scrapy.FormRequest(
            url = api_url,
            method = "POST",
            dont_filter=True,
            formdata=payload,
            callback=self.parse_intermediate,
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "payload": payload,
                "current_page": payload["swGridView_CurrentPageIndex"]
            },
        )
        else:
            self.logger.info(f"No more pages to crawl for start_url: {start_url}")