from datetime import datetime
from typing import Optional
import scrapy
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ColoradoAttorneyGeneral(OCSpider):
    name = 'ColoradoAttorneyGeneral'
    
    country = "US"

    year = datetime.now().year  # Fetching the data starting from Current Year

    start_urls_names = {
        f'https://coag.gov/category/{year}/': 'press releases',
    }
    
    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Mountain"
    
    def get_articles(self, response) -> list:
        return response.xpath("//header//h2/a/@href").getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self,response) -> str:
        return response.xpath('//h1[@class = "page-header"]//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='content__wrapper']//p//text()").getall())
    
    def get_images(self, response) -> list[str]:
        return []
    
    def date_format(self) -> str:
        return "%B %d, %Y"
    
    def get_date(self, response) -> str:
        return response.xpath("//p/text()").re_first(r"([A-Za-z]+ \d{1,2}, \d{4})")
    
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        next_page = response.xpath('//div[@class="next btn"]//a/@href').get()
        return response.urljoin(next_page) if next_page else None
                
    def go_to_next_page(self, response, start_url, current_page=None):
        start_url = response.meta.get("start_url", response.url)  # Keep original start URL
        current_year = response.meta.get("current_year", datetime.now().year)  # Fetch current year
        next_page = self.get_next_page(response)
        if next_page:
            yield scrapy.Request(
                url=next_page,
                callback=self.parse,
                meta={
                    "start_url": start_url,
                    "current_year": current_year
                }
            )
        else:
            previous_year = current_year - 1
            if response.status == 200:
                url = f"https://coag.gov/category/{previous_year}/"
                yield scrapy.Request(
                    url=url,
                    callback=self.parse,
                    meta={
                        "start_url": start_url,
                        "current_year": previous_year
                    }
                )