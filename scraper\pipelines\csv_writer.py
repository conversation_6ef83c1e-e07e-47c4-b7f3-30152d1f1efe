import csv
import logging


class Pipeline:
    def open_spider(self, spider):
        # Open the CSV file when the spider starts
        self.file = open("output.csv", "w", newline="", encoding="utf-8")
        self.csv_writer = None

    def process_item(self, item, spider):
        # Initialize the CSV writer on the first item to set headers
        try:
            
            if self.csv_writer is None:
                # Define fieldnames using item keys
                fieldnames = item.keys()
                logging.info("CSV WRITER ______________________-", fieldnames)
                self.csv_writer = csv.DictWriter(self.file, fieldnames=fieldnames)
                self.csv_writer.writeheader()

            # Write the item to the CSV
            self.csv_writer.writerow(item)
            return item
        except Exception as e:
            print("Exception in csv_writer.py: ", e)
            logging.error("Exception in csv_writer.py: ", e)
            return item

    def close_spider(self, spider):
        # Close the CSV file when the spider ends
        self.file.close()
