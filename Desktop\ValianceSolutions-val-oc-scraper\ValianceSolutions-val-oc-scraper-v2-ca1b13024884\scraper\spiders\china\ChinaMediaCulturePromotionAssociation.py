import json
from scraper.OCSpider import OCSpider
from dotenv import load_dotenv
from scraper.utils.helper import body_normalization
from typing import Optional
import logging
load_dotenv()
from scrapy.http import Request
import math
import requests


class ChinaMediaCulturePromotionAssociation(OCSpider):
    name = "ChinaMediaCulturePromotionAssociation"

    custom_settings = {
        "DOWNLOAD_DELAY": 5,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    start_urls_names = {
        "http://cmcpa.cn/#/Notice/Noticech": "通知公告",
        "http://cmcpa.cn/#/Assocdynamic/NewsCenter1?id=&name=%3Cp%3E%E5%8D%8F%E4%BC%9A%E9%A2%86%E5%AF%BC%E5%8A%A8%E6%80%81%3C%2Fp%3E": "协会领导动态",
    }

    charset = "utf-8"

    # Start urls mapping with corresponding APIs and their payloads
    api_start_urls = {
        'http://cmcpa.cn/#/Notice/Noticech': {
            "url": "http://cmcpa.cn/api/SiteContentAPI/GetPageList",
            "payload": {
                "ChannelID": "2858",
                "JournalID": "202007100001",
                "CurrentPage": 1,
                "PageSize": 10
            },
        },
        'http://cmcpa.cn/#/Assocdynamic/NewsCenter1?id=&name=%3Cp%3E%E5%8D%8F%E4%BC%9A%E9%A2%86%E5%AF%BC%E5%8A%A8%E6%80%81%3C%2Fp%3E': {
            "url": "http://cmcpa.cn/api/SiteContentAPI/GetPageList",
            "payload": {
                "ChannelID": "",
                "JournalID": "202007100001",
                "CurrentPage": 1,
                "PageSize": 10
            },
        }
    }

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            self.logger.error(f"No API configuration found for start_url: {start_url}")
            return
        api_url = api_data["url"]
        payload = api_data["payload"]
        current_page = payload.get("CurrentPage")
        yield Request(
            url = api_url,
            method = "POST",
            headers = {
                "Accept": "application/json, text/plain, */*",
                "Content-Type": "application/json;charset=UTF-8",
                "Host": "cmcpa.cn",
                "Origin": "http://cmcpa.cn",
                "Referer": "http://cmcpa.cn/",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
            },
            body = json.dumps(payload),
            callback = self.parse,    # callback parse
            meta={
                "start_url": start_url,
                "api_url": api_data["url"],
                "payload": payload,
                "current_page": current_page
            },
        )
    
    @property
    def source_type(self) -> str:
        return "IndustryAssociation"
    
    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        try:
            start_url = response.meta.get("start_url")
            data = json.loads(response.text)
            records = data.get("_itemList", [])
            if start_url == 'http://cmcpa.cn/#/Notice/Noticech': 
                articles = [
                    f"http://cmcpa.cn/#/Notice/Noticech/Noticedetail?id={record.get('ContentID')}"
                    for record in records if record.get("ContentID")
                ]
            else:
                articles = [
                    f"http://cmcpa.cn/#/Assocdynamic/NewsCenter/NewsCenterdetail?id={record.get('ContentID')}"
                    for record in records if record.get("ContentID")
                ]
            print("Extracted articles:", articles)
            return articles
        except json.JSONDecodeError as e:
            self.logger.error(f"Error decoding JSON: {e}")
            return []

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        # API logic for title
        # article_url = response.request.url
        article_url = response.url
        api_response = self.get_api_response(article_url)
        title = api_response.get("Title")
        return title
        
        # # Xpath logic for title 
        # title = response.xpath('//div[@class="content_body"]/p[1]/p/text()').get()
        # return title
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="content_body"]/p[3]//p//text()').getall())
    
    def get_images(self, response) -> list:
        return [
            response.urljoin(url)
            for url in
            response.xpath('//div[@class="content_body"]/p[3]//img/@src').getall()
        ]
    
    def get_document_urls(self, response, entry=None) -> list:
        return []
    
    def date_format(self) -> str:
        return "%Y-%m-%dT%H:%M:%S"
    
    def get_date(self, response) -> str:
        # # API logic for date
        # article_url = response.request.url
        article_url = response.url
        api_response = self.get_api_response(article_url)
        return api_response.get("AddDate")
    
        # # Xpath logic for date
        # date =  response.xpath('//div[@class="flRseniority_just"]/ul//li/span[2]//text()').re_first(r"\d{4}-\d{2}-\d{2}")
        # return date
    
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response, current_page):
        total_articles = response.json().get('totalRecords')
        pageSize = response.json().get('pageSize')
        max_pages = math.ceil(total_articles / pageSize)
        print("Inside get_next_page:, max pages are ", max_pages)
        next_page = current_page + 1 if current_page < max_pages else None
        print("Inside get_next_page:, next page is ", next_page)
        return next_page
    
    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        api_url = response.meta.get("api_url")
        if not api_url:
            logging.error("API URL not found in meta data.")
            return
        next_page = self.get_next_page(response, current_page)
        if next_page:
            payload = response.meta.get('payload')
            payload['CurrentPage'] = next_page
            print("Inside go_to_next_page:, payload is ", payload)
            # API request for the next page
            yield Request(
                url = api_url,
                method = "POST",
                body = json.dumps(payload),
                headers = {
                    "Accept": "application/json, text/plain, */*",
                    "Content-Type": "application/json;charset=UTF-8",
                    "Host": "cmcpa.cn",
                    "Origin": "http://cmcpa.cn",
                    "Referer": "http://cmcpa.cn/",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
                },
                callback=self.parse_intermediate,    # callback parse_intermediate
                meta={'current_page': next_page, 'start_url': start_url}
            )
        else:
            logging.info("No more pages to fetch.")
            yield None
        
    def get_api_response(self, article_url: str) -> Optional[dict]:
        try:
            # Extract ContentID from the article URL
            content_id = article_url.split("?id=")[-1]   # extract ContentID from the article URL 
            print("The content ID extracted is ", content_id)
            api_url = "http://cmcpa.cn/api/SiteContentAPI/GetModel"
            payload = {"ContentID": content_id}
            
            # Make a POST request to the API
            headers = {
                "Accept": "application/json, text/plain, */*",
                "Content-Type": "application/json;charset=UTF-8",
                "Host": "cmcpa.cn",
                "Origin": "http://cmcpa.cn",
                "Referer": "http://cmcpa.cn/",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
            }
            response = requests.post(api_url, json=payload, headers=headers)
            if response.status_code == 200:
                return response.json()
            else:
                self.logger.error(f"Failed to fetch API response. Status code: {response.status_code}")
                return None
        except Exception as e:
            self.logger.error(f"Error in get_api_response: {e}")
            return None