from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class InstituteForPolicyStudies(OCSpider):
    name = "InstituteForPolicyStudies"

    country = "US"

    start_urls_names = {
        "https://ips-dc.org/reports/" : "Research"
    }
    
    charset = "utf-8"

    @property
    def language(self): 
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "US/Eastern"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="item-info"]//a/@href').getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="p-name"]//text()').get()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="e-content"]//p/text()').getall()) 
        
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%B %d, %Y"
    
    def get_date(self, response) -> str:
        return response.xpath('//div[@class="post-date"]//text()').get().strip()
        
    def get_authors(self, response):
        authors = response.xpath('//div[@class="entry-meta-info"]//a//text()').getall()
        authors = [author for author in authors if author.strip()]
        return authors
    
    def get_page_flag(self) -> bool:
        return False
     
    def get_next_page(self, response): 
        return response.xpath('//a[contains(@class, "next") and contains(@class, "page-numbers")]/@href').get()