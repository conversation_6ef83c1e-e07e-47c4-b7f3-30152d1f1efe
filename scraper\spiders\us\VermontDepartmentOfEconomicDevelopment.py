from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization

class VermontDepartmentOfEconomicDevelopment(OCSpider):
    name = 'VermontDepartmentOfEconomicDevelopment'
    
    country = "US"

    start_urls_names = {
        'https://accd.vermont.gov/vcgi/press-releases': 'News'
    }
    
    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Eastern"
    
    def get_articles(self, response) -> list:
        return response.xpath('//span[@class= "field-content"]//div/a/@href').getall() 
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self,response) -> str:
        return response.xpath('//h1[@class= "margin-0"]/span/text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class= "usa-prose field field--name-body field--type-text-with-summary field--label-hidden field__item"]//p/text()').getall())
    
    def get_images(self, response) -> list[str]:
        return []

    def date_format(self) -> str:
        return "%B %d, %Y"
    
    def get_date(self, response) -> str:
        return response.xpath('//div[@class= "field field--name-field-date field--type-datetime field--label-hidden field__item"]/text()').re_first(r"([A-Za-z]+ \d{1,2}, \d{4})")
    
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        return response.xpath('//li[contains(@class, "usa-pagination__arrow")]/a[contains(@class, "usa-pagination__next-page")]/@href').get()