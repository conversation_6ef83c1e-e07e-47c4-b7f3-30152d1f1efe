import logging
from typing import List, Union
import scrapy
import json
from scraper.middlewares import HeadlessBrowserProxy
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
from urllib.parse import urlencode, urljoin
from bs4 import BeautifulSoup


class ChinaScienceFilmVideoAssociation(OCSpider):
    name = 'ChinaScienceFilmVideoAssociation'
    
    custom_settings = {   
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }
    
    start_urls_names = {
        'http://www.csfva.org.cn/xwzx/tzgg/index.html': '通知公告',   # Data is extracted from API response
        'http://www.csfva.org.cn/xwzx/xhdt/zxdt/index.html': '最新动态',   # Data is extracted from API response
        'http://www.csfva.org.cn/dj/index.html': '党建'   # Data is extracted from Xpaths as there is no API call
    }
    
    api_start_url = {
        'http://www.csfva.org.cn/xwzx/tzgg/index.html': 'http://www.csfva.org.cn/api-gateway/jpaas-publish-server/front/page/build/unit?webId=8826683f0c824698a9d12639d218eb67&pageId=df4f05fde88e44ae8ce04fb21da82337&parseType=bulidstatic&pageType=column&tagId=%E5%88%97%E8%A1%A8&tplSetId=24da0863661c458d935ead58bc587962',
        'http://www.csfva.org.cn/xwzx/xhdt/zxdt/index.html': 'http://www.csfva.org.cn/api-gateway/jpaas-publish-server/front/page/build/unit?webId=8826683f0c824698a9d12639d218eb67&pageId=54089d07195641c9955eafdbb3eb6249&parseType=bulidstatic&pageType=column&tagId=%E5%88%97%E8%A1%A8&tplSetId=24da0863661c458d935ead58bc587962'
    }
    
    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_base_url = self.api_start_url.get(start_url)
        if not api_base_url:
            self.logger.info(f"No API configuration found for start_url: {start_url}")
            hbp = HeadlessBrowserProxy()
            self.logger.info(f"Falling back to headless browser for {start_url}")
            request = scrapy.Request(hbp.get_proxy(response.url, timeout=10000), callback=self.parse
            )
            request.meta['start_url'] = response.request.meta['start_url']
            yield request
        else:
            current_page = response.meta.get("current_page", 1)
            param_json = {
                "pageNo": current_page,
                "pageSize": 6
            }
            api_url = f"{api_base_url}&{urlencode({'paramJson': json.dumps(param_json)})}"
            yield scrapy.Request(
                url = api_url,
                headers = {
                    "Content-Type": "application/json;charset=UTF-8"
                },
                callback = self.parse, 
                meta={
                    "start_url": start_url,
                    "api_url": api_base_url,
                    "current_page": current_page,
                },
            )
                
    @property
    def source_type(self) -> str:
        return "IndustryAssociation"
    
    @property
    def timezone(self): 
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        all_links = set()   
        start_url = response.meta.get("start_url")
        if start_url == 'http://www.csfva.org.cn/dj/index.html':     # Only for this Start_Url, articles links are extracted from Xpaths
            xpath_links = response.xpath("//ul[@class='list_14 clearfix bor_bt']/li//a//@href").extract() 
            all_links.update(urljoin(start_url, link) for link in xpath_links)  # 'urljoin' is necessary here because headless browser proxy is used
        else:
            try:
                html_content = response.json().get("data", {}).get("html")
                if not html_content:
                    self.logger.error("No HTML content found in the API response.")
                    return []
                soup = BeautifulSoup(html_content, "html.parser")
                h3_links = [
                    a['href'] for h3 in soup.find_all("h3") for a in h3.find_all("a", href=True)
                ]
                all_links.update(h3_links)
            except Exception as e:
                self.logger.error(f"Error while extracting articles: {e}",)
                return []
        return list(all_links)    

    def get_href(self, entry) -> str:
        return entry

    def get_title(self,response) -> str:
        title = response.xpath("//div[@class='u_content_title']//text()").get()
        return title
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='u_content_text']//p/text()").getall())
    
    def get_images(self, response) -> List[str]:
        images = response.xpath("//div[@class='u_content_text']/p//img/@src").extract()
        return images
    
    def date_format(self) -> str:
        return '%Y-%m-%d'

    def get_date(self, response) -> str:
        date = response.xpath("//div[@class='u_content_tip']/span//text()").re_first(r"\d{4}-\d{2}-\d{2}")
        return date

    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response, current_page) -> Union[None, str]:
        html_content = response.json().get("data", {}).get("html")
        if not html_content:
            self.logger.error("No more child articles links found for the page, Stopping.")
            return None
        else:   
            return current_page + 1

    def go_to_next_page(self, response, start_url, current_page=1):
        api_url = response.meta.get("api_url")
        if not api_url: 
            logging.info("API URL not found in meta data.")
            return
        next_page = self.get_next_page(response, current_page)
        if next_page:
            # API request for the next page
            yield scrapy.Request(
                url=api_url,
                headers={
                    "Content-Type": "application/json;charset=UTF-8",
                },
                callback=self.parse_intermediate,
                meta={'current_page': next_page, 'start_url': start_url}
            )
        else:
            logging.info("No more pages to fetch.")
            yield None