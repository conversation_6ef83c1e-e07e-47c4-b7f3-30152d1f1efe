from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime
from typing import Optional

class ColoradoDepartmentOfLaborAndEmployment(OCSpider):
    name = 'ColoradoDepartmentOfLaborAndEmployment'
    
    country = "US"
    
    start_urls_names = {
        'https://cdle.colorado.gov/category/press-releases': 'Press Releases'
    }
    
    charset = "utf-8"
    
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Eastern"
    
    def get_articles(self, response) -> list:
        return response.xpath("//h2[@class='text-2xl fw-500']/a/@href").getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//h1/text()").get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='field field--name-body field--type-text-with-summary field--label-hidden field--item']//p//text()").getall())

    def get_images(self, response) -> list[str]:
        return []
    
    def date_format(self) -> str:
        return "%b %d, %Y"
    
    def get_date(self, response) -> Optional[str]:
        raw_date = response.xpath("//div[contains(@class, 'author_date')]//time/text()").get()
        if not raw_date:
            raw_date = response.xpath("//p/strong[contains(text(), 'Date:')]/following-sibling::text()").get()
        if raw_date:
            raw_date = raw_date.strip().replace("\xa0", " ")
            try:
                parsed_date = datetime.strptime(raw_date, "%A, %B %d, %Y")
            except ValueError:
                try:
                    parsed_date = datetime.strptime(raw_date, "%B %d, %Y")
                except ValueError:
                    return None
            return parsed_date.strftime(self.date_format())
        return None

    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        next_page = response.xpath("//li[contains(@class, 'pager__item--next')]/a/@href").get()
        return response.urljoin(next_page) if next_page else None