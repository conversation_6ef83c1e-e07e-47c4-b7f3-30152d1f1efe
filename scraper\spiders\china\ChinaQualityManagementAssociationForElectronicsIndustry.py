from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from scrapy.http import Request
from dotenv import load_dotenv
import json
from scraper.middlewares import HeadlessBrowserProxy
load_dotenv()

class ChinaQualityManagementAssociationForElectronicsIndustry(OCSpider):
    name = "ChinaQualityManagementAssociationForElectronicsIndustry"

    custom_settings = {
        "DOWNLOAD_DELAY": 2,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    charset = "utf-8"

    start_urls_names = {
        "https://www.cqae.org.cn/more/1": "通知公告",
        "https://www.cqae.org.cn/7/8?paid=20": "协会公示",
        "https://www.cqae.org.cn/7/9?paid=21": "协会声明",
        "https://www.cqae.org.cn/7/10?paid=22": "社会活动",
        "https://www.cqae.org.cn/7/11?paid=2": "协会动态",
        # "https://www.cqae.org.cn/7/13?paid=1": "通知公告",  # Ignoring this start url as it contains same articles present as in the first start url
        "https://www.cqae.org.cn/14/0?paid=206": "党建文化"
    }

    api_start_url_mapping = {
        "https://www.cqae.org.cn/more/1": "https://www.cqae.org.cn/web/notice/selectList?paid=1&pageSize=10",
        "https://www.cqae.org.cn/7/8?paid=20":  "https://www.cqae.org.cn/web/notice/selectList?paid=20&&pageSize=10",
        "https://www.cqae.org.cn/7/9?paid=21": "https://www.cqae.org.cn/web/notice/selectList?paid=21&pageSize=10",
        "https://www.cqae.org.cn/7/10?paid=22": "https://www.cqae.org.cn/web/notice/selectList?paid=22&pageSize=10",
        "https://www.cqae.org.cn/7/11?paid=2": "https://www.cqae.org.cn/web/notice/selectList?paid=2&pageSize=10",
        # "https://www.cqae.org.cn/7/13?paid=1": "https://www.cqae.org.cn/web/notice/selectList?paid=1&pageSize=10", # Same API as the first start url
        "https://www.cqae.org.cn/14/0?paid=206": "https://www.cqae.org.cn/web/notice/selectList?paid=206&pageSize=10"
    }

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_url = self.api_start_url_mapping.get(start_url)
        current_page = response.meta.get("current_page", 1)
        if not api_url:
            self.logger.error(f"No API found for start_url: {start_url}")
            return
        paginated_api_url = f"{api_url}&pageNum={current_page}"
        yield Request(
            url=paginated_api_url,
            callback=self.parse,
            meta={
                "current_page": current_page,
                "api_url": api_url,
                "start_url": start_url,
            },
        )    

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"

    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        try:
            data = json.loads(response.text)
            records = data.get("result", {}).get("records", [])
            articles = [
                f"https://www.cqae.org.cn/details/{record.get('paId')}/{record.get('nbId')}"
                for record in records
            ]
            return self.get_proxy_articles(articles)
        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to decode JSON from response: {e}")
            return []

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="left"]/h2/text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="custom-content"]//p//text()').getall())

    def get_images(self, response) -> list:
        return response.xpath("//div[@class='custom-content']//img/@src").getall()

    def get_document_urls(self, response, entry=None) -> list:
        return [response.urljoin(doc) for doc in response.xpath('//div[@class="custom-content"]//a/@href').getall()]

    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        return response.xpath('//div[@class="left"]/p/span/text()').re_first(r"\d{4}-\d{2}-\d{2}")

    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response, current_page: Optional[int] = 1) -> Optional[str]:
        api_url = response.meta.get('api_url')
        if not api_url:
            return None        
        try:
            data = json.loads(response.text)
            max_pages = data.get("result", {}).get("pages")
        except json.JSONDecodeError as e:
            return None
        next_page = current_page + 1
        if next_page > max_pages:
            return None
        return f"{api_url}&pageNum={next_page}"
    
    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        next_page_url = self.get_next_page(response, current_page)
        if next_page_url:
            request = response.request.replace(
                url=next_page_url,
                callback=self.parse_intermediate
            )
            request.meta.update({
                'start_url': start_url,
                'current_page': current_page + 1,
                'api_url': response.meta.get('api_url')
            })
            yield request
        else:
            self.logger.info(f"No more pages to crawl for start_url: {start_url}")
    
    def get_proxy_articles(self, articles):
        try:
            hbp = HeadlessBrowserProxy()
            return [hbp.get_proxy(url, timeout = 40000) for url in articles]
        except Exception as e:
            self.logger.error(f"Failed to fetch proxy articles: {e}")
            return []