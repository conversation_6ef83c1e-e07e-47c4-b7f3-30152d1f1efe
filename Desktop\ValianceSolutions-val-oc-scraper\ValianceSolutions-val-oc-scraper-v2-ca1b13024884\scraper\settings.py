import logging

LOG_ENABLED = True

root_logger = logging.getLogger()
root_logger.setLevel(logging.INFO)

SPIDER_MODULES = ['scraper.spiders']
NEWSPIDER_MODULE = 'scraper.spiders'

# Crawl responsibly by identifying yourself (and your website) on the user-agent
USER_AGENT = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 11_1_0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.150 Safari/537.36'

# Obey robots.txt rules
ROBOTSTXT_OBEY = False

# Configure maximum concurrent requests performed by Scrapy (default: 16)
# CONCURRENT_REQUESTS = 32

# Configure a delay for requests for the same website (default: 0)
# See https://docs.scrapy.org/en/latest/topics/settings.html#download-delay
# See also autothrottle settings and docs
# DOWNLOAD_DELAY = 3
# The download delay setting will honor only one of:
CONCURRENT_REQUESTS_PER_DOMAIN = 1
# CONCURRENT_REQUESTS_PER_IP = 16

# Disable cookies (enabled by default)
# COOKIES_ENABLED = False

# Disable Telnet Console (enabled by default)
# TELNETCONSOLE_ENABLED = False

# Override the default request headers:
# DEFAULT_REQUEST_HEADERS = {
#   'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
#   'Accept-Language': 'en',
# }

# Enable or disable spider middlewares
# See https://docs.scrapy.org/en/latest/topics/spider-middleware.html
# SPIDER_MIDDLEWARES = {
#    'scraper.middlewares.ScraperSpiderMiddleware': 543,
# }

# Enable or disable downloader middlewares
# See https://docs.scrapy.org/en/latest/topics/downloader-middleware.html
DOWNLOADER_MIDDLEWARES = {
    'scrapy_splash.SplashCookiesMiddleware': 723,
    'scrapy_splash.SplashMiddleware': 725,
    'scrapy.downloadermiddlewares.httpcompression.HttpCompressionMiddleware': 810,
}



# Configure item pipelines
# See https://docs.scrapy.org/en/latest/topics/item-pipeline.html

ITEM_PIPELINES = {
    # 'scraper.pipelines.data_validation.Pipeline': 1,
    # 'scraper.pipelines.checkpoint.datastore.Pipeline': 2,
    # 'scraper.pipelines.nlp.process.Pipeline': 3,
    # 'scraper.pipelines.storage.bq_with_nlp.Pipeline': 6,
    # 'scraper.pipelines.storage.elastic.Pipeline': 5,
    # 'scraper.pipelines.Alert.AlertPipeline': 6
    #'scraper.pipelines.checkpoint.datastore.OfficialLinePipeline': 3,
    # 'scraper.pipelines.dummy.Pipeline': 5,
    'scraper.pipelines.csv_writer.Pipeline': 200,
    # 'scraper.pipelines.storage.gcs_raw.Pipeline': 2,
    # 'scraper.pipelines.storage.bq_raw.Pipeline': 5
}

SPIDER_MIDDLEWARES = {
        'scrapy_splash.SplashDeduplicateArgsMiddleware': 100,
        #'scrapy_autounit.AutounitMiddleware': 950,
        'scrapy_testmaster.TestMasterMiddleware': 950
}

#AUTOUNIT_ENABLED = True
TESTMASTER_ENABLED = False
#AUTOUNIT_MAX_FIXTURES_PER_CALLBACK = 5
#TESTMASTER_MAX_FIXTURES_PER_CALLBACK=10




# Enable and configure the AutoThrottle extension (disabled by default)
# See https://docs.scrapy.org/en/latest/topics/autothrottle.html
# AUTOTHROTTLE_ENABLED = True
# The initial download delay
# AUTOTHROTTLE_START_DELAY = 5
# The maximum download delay to be set in case of high latencies
# AUTOTHROTTLE_MAX_DELAY = 60
# The average number of requests Scrapy should be sending in parallel to
# each remote server
# AUTOTHROTTLE_TARGET_CONCURRENCY = 1.0
# Enable showing throttling stats for every response received:
AUTOTHROTTLE_DEBUG = True

# Enable and configure HTTP caching (disabled by default)
# See https://docs.scrapy.org/en/latest/topics/downloader-middleware.html#httpcache-middleware-settings
# HTTPCACHE_ENABLED = True
# HTTPCACHE_EXPIRATION_SECS = 0
# HTTPCACHE_DIR = 'httpcache'
# HTTPCACHE_IGNORE_HTTP_CODES = []
# HTTPCACHE_STORAGE = 'scrapy.extensions.httpcache.FilesystemCacheStorage'

RETRY_ENABLED = True
RETRY_TIMES = 3
DOWNLOAD_TIMEOUT = 300
DUPEFILTER_CLASS='scrapy_splash.SplashAwareDupeFilter'

# Splash settings
SPLASH_URL = 'http://localhost:8050'
HTTPCACHE_STORAGE = 'scrapy_splash.SplashAwareFSCacheStorage'

