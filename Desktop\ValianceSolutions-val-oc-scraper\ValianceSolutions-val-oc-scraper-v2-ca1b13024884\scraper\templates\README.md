# Spider Templates

This directory contains templates for creating new spiders in the Valiance Solutions web scraping project.

## Types of Spiders

There are two main types of spiders:

1. **OCSpider**: Generic base class for scraping websites.
   - Example: https://www.zgsz.org.cn/h-col-122.html
   - Template: `OCSpiderTemplate.py`

2. **OfficialLineSpider**: Used for websites where URLs include date parameters, allowing easy navigation through historical data.
   - Example: https://epaper.sakshi.com/Andhra_Pradesh_Main?eid=99&edate=08/12/2024
   - Template: `OfficialLineSpiderTemplate.py`

## Required Methods

When implementing a new spider, you need to define the following methods:

### Common Methods for Both Spider Types

- `source_type`: Define the type of source (ministry, newspaper, IndustryAssociation, etc.)
- `timezone`: Define the timezone of the source
- `get_articles`: Extract article URLs from the page
- `get_href`: Return the article URL
- `get_title`: Extract the article title
- `get_body`: Extract the article body
- `get_images`: Extract image URLs from the article
- `date_format`: Define the date format used on the website
- `get_date`: Extract the article date and convert to timestamp
- `get_authors`: Extract the article authors
- `get_document_urls`: Extract document URLs (PDFs, etc.) from the article
- `get_next_page`: Extract the next page URL for pagination

### Additional Methods for OCSpider

- `get_page_flag`: Return True if pagination is available, False otherwise
- `get_meta`: Extract any additional metadata
- `get_pdf`: Extract PDF content if needed
- `get_subhead`: Extract article subheading if available

### Additional Methods for OfficialLineSpider

- `get_start_url`: Format the date for the URL
- `get_subhead`: Extract article subheading if available

## Optional Methods

- `parse_intermediate`: Used when a headless browser is needed to render JavaScript before parsing the content
- `go_to_next_page`: Used in conjunction with parse_intermediate to navigate to the next page in headless mode

## How to Create a New Spider

1. Choose the appropriate template based on the website structure:
   - Use `OCSpiderTemplate.py` for regular websites
   - Use `OfficialLineSpiderTemplate.py` for websites with date parameters in URLs

2. Create a new file in the appropriate directory:
   - For OCSpider: `scraper/spiders/{country}/{SpiderName}.py`
   - For OfficialLineSpider: `scraper/spiders/official_line/{SpiderName}.py`

3. Copy the template content and customize it for your specific website:
   - Update the class name and spider name
   - Set the appropriate country code and language
   - Define the start URLs
   - Customize the XPath selectors for extracting content
   - Implement any additional methods as needed

4. Test your spider by running:
   ```
   scrapy crawl {spider_name}
   ```

## Special Configurations

### Headless Browser

If the website requires JavaScript rendering, you can use the HeadlessBrowserProxy middleware:

```python
HEADLESS_BROWSER_WAIT_TIME = 5000  # 5 seconds wait time

custom_settings = {
    "DOWNLOADER_MIDDLEWARES": {
        'scraper.middlewares.HeadlessBrowserProxy': 350,
    },
    "DOWNLOAD_DELAY": 3,
}
```

### Geo-Targeted Proxy

If the website requires access from a specific country, you can use the GeoProxyMiddleware:

```python
proxy_country = "us"  # Country code

custom_settings = {
    "DOWNLOADER_MIDDLEWARES": {
        'scraper.middlewares.GeoProxyMiddleware': 350,
    },
    "DOWNLOAD_DELAY": 2,
}
```

## Example Implementations

- OCSpider Example: `scraper/spiders/china/ChinaWaterConservationAssociation.py`
- OfficialLineSpider Example: `scraper/spiders/official_line/SakshiNewspaper.py`

These examples demonstrate how to implement the required methods for each spider type.
