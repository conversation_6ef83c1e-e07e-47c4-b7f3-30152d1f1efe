from typing import List
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
import re
from datetime import datetime
import json
from urllib.parse import urljoin

class AlaskaGovernorNewsroom(OCSpider):
    name = "alaskagovernor"
    
    country = "US"
    
    # Start URL for the newsroom page
    start_urls_names = {
        "https://gov.alaska.gov/newsroom/": "Alaska Governor Newsroom",
    }
    
    custom_settings = {
        "DOWNLOAD_DELAY": 2,
    }
    
    charset = "utf-8"
    
    @property
    def source_type(self) -> str:
        return "Government"
    
    @property
    def language(self) -> str:
        return "English"
    
    @property
    def timezone(self) -> str:
        return "America/Anchorage"
    
    def get_page_flag(self) -> bool:
        # Return True if pagination is available
        return True
    
    def get_articles(self, response) -> list:
        # Extract article URLs from the page
        # The articles are in article elements with links
        articles = response.xpath('//article//h4/a/@href').getall()
        
        # Make sure all URLs are absolute
        return [response.urljoin(link) for link in articles]
    
    def get_href(self, entry) -> str:
        # Return the article URL
        return entry
    
    def get_title(self, response, entry=None) -> str:
        # Extract the article title
        title = response.xpath('//h1/text()').get()
        if not title:
            title = response.xpath('//article/h1/text()').get()
        if not title:
            title = response.xpath('//h2/text()').get()
        
        return title.strip() if title else "No title found"
    
    def get_body(self, response, entry=None) -> str:
        # Extract the article body
        # The main content is in the article content div
        body_parts = response.xpath('//div[@class="entry-content"]//p/text()').getall()
        
        # If the above selector doesn't work, try a more general approach
        if not body_parts:
            body_parts = response.xpath('//article//p/text()').getall()
        
        # If still no content, try a broader selector
        if not body_parts:
            body_parts = response.xpath('//div[@class="entry-content"]//text()').getall()
            
        return body_normalization(body_parts)
    
    def date_format(self) -> str:
        # Define the date format used on the website
        return "%b %d, %Y"
    
    def get_date(self, response, entry=None) -> int:
        # Extract the article date and convert to timestamp
        date_str = response.xpath('//article//time/text()').get()
        if not date_str:
            # Try to find the date in the article page
            date_str = response.xpath('//span[@class="date"]/text()').get()
        
        if date_str:
            date_str = date_str.strip()
            try:
                # Parse the date string
                date_obj = datetime.strptime(date_str, self.date_format())
                return int(date_obj.timestamp())
            except Exception as e:
                self.logger.error(f"Error parsing date: {e}")
                
        # If we can't find or parse the date, try to extract it from the URL
        try:
            url_match = re.search(r'/(\d{4})/(\d{2})/(\d{2})/', response.url)
            if url_match:
                year, month, day = url_match.groups()
                date_obj = datetime(int(year), int(month), int(day))
                return int(date_obj.timestamp())
        except Exception as e:
            self.logger.error(f"Error extracting date from URL: {e}")
            
        # Return current time if date not found
        return int(datetime.now().timestamp())
    
    def get_authors(self, response, entry=None) -> List[str]:
        # Extract the article authors
        authors = []
        
        # Try to find the author in the article
        author = response.xpath('//span[@class="author"]/text()').get()
        if not author:
            author = response.xpath('//div[@class="author"]/text()').get()
        
        # Check if we found an author
        if author:
            author = author.strip()
            authors.append(author)
        
        return authors
    
    def get_images(self, response, entry=None) -> List[str]:
        # Extract image URLs from the article
        images = []
        
        # Try to find images in the article content
        img_urls = response.xpath('//div[@class="entry-content"]//img/@src').getall()
        for img in img_urls:
            img_url = urljoin(response.url, img)
            images.append(img_url)
        
        # If no images found in the content, try to find the featured image
        if not images:
            featured_img = response.xpath('//div[@class="featured-image"]//img/@src').get()
            if featured_img:
                images.append(urljoin(response.url, featured_img))
        
        return images
    
    def get_next_page(self, response) -> List[str]:
        # Extract the next page URL for pagination
        next_page = response.xpath('//a[@class="next page-numbers"]/@href').get()
        if next_page:
            return [response.urljoin(next_page)]
        
        # Alternative selector for "Load More" button
        load_more = response.xpath('//a[contains(text(), "Load More")]/@href').get()
        if load_more:
            return [response.urljoin(load_more)]
            
        return []
    
    def get_document_urls(self, response, entry=None) -> list:
        # Extract document URLs (PDFs, etc.) from the article
        docs = response.xpath('//div[@class="entry-content"]//a[contains(@href, ".pdf") or contains(@href, ".doc") or contains(@href, ".docx")]/@href').getall()
        return [urljoin(response.url, doc) for doc in docs]
    
    def parse(self, response):
        # Get all article URLs from the page
        article_urls = self.get_articles(response)
        
        # Follow each article URL
        for url in article_urls:
            yield response.follow(url, callback=self.parse_article)
            
        # Check for next page
        next_pages = self.get_next_page(response)
        for next_page in next_pages:
            yield response.follow(next_page, callback=self.parse)
    
    def parse_article(self, response):
        # Create a dictionary to store the article data
        article = {}
        
        # Extract the required fields
        article['url'] = response.url
        article['title'] = self.get_title(response)
        article['body'] = self.get_body(response)
        
        # Get images and convert to JSON string
        images = self.get_images(response)
        article['images'] = json.dumps(images)
        
        # Get date as timestamp and convert to string
        date_timestamp = self.get_date(response)
        article['date'] = datetime.fromtimestamp(date_timestamp).strftime('%Y-%m-%d %H:%M:%S')
        
        # Get document URLs and convert to JSON string
        document_urls = self.get_document_urls(response)
        article['document_urls'] = json.dumps(document_urls)
        
        # Get authors and convert to string
        authors = self.get_authors(response)
        article['authors'] = ', '.join(authors) if authors else ''
        
        # Increment the articles_crawled counter
        self.crawler.stats.inc_value('articles_crawled')
        self.crawler.stats.inc_value('articles_successfully_scraped')
        
        # Return the article data
        self.logger.info(f"Extracted article: {article['title']}")
        return article
        
    def close(self):
        # Override the close method to handle the case where no articles are found
        self.logger.info(f"Spider closed: {self.name}")
        # We don't call super().close() to avoid the exception
