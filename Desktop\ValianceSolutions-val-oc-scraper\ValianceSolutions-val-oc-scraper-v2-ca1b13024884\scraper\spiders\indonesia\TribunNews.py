from typing import List, Dict, Any
from scraper.OCSpider import <PERSON><PERSON><PERSON><PERSON>
from scraper.utils.helper import body_normalization
import re
from datetime import datetime
import json
from urllib.parse import urljoin

class TribunNews(OCSpider):
    name = "tribunnews"

    country = "ID"

    # Example URL: https://www.tribunnews.com/new-economy/2024/08/07/lewat-teknologi-dan-edukasi-gopay-mendukung-pemberantasan-judi-online-di-indonesia
    start_urls_names = {
        "https://www.tribunnews.com/new-economy/2024/08/07/lewat-teknologi-dan-edukasi-gopay-mendukung-pemberantasan-judi-online-di-indonesia": "GoPay Article",
    }

    custom_settings = {
        "DOWNLOAD_DELAY": 2,
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "NewsPortal"

    @property
    def language(self) -> str:
        return "Indonesian"

    @property
    def timezone(self) -> str:
        return "Asia/Jakarta"

    def get_page_flag(self) -> bool:
        # Return True if pagination is available
        return False

    def get_articles(self, response) -> list:
        # Since we're directly scraping a specific article, we return the current URL
        return [response.url]

    def get_href(self, entry) -> str:
        # Return the article URL
        return entry

    def get_title(self, response, entry=None) -> str:
        # Extract the article title
        title = response.xpath('//h1/text()').get()
        if not title:
            title = response.xpath('//title/text()').get()

        return title.strip() if title else "No title found"

    def get_body(self, response, entry=None) -> str:
        # Extract the article body
        # The main content is in paragraphs after the title
        body_parts = response.xpath('//div[@class="side-article txt-article"]/p/text()').getall()

        # If the above selector doesn't work, try a more general approach
        if not body_parts:
            body_parts = response.xpath('//div[contains(@class, "txt-article")]/p/text()').getall()

        # If still no content, try a broader selector
        if not body_parts:
            body_parts = response.xpath('//p/text()').getall()

        return body_normalization(body_parts)

    def date_format(self) -> str:
        # Define the date format used on the website
        return "%A, %d %B %Y %H:%M %Z"

    def get_date(self, response, entry=None) -> int:
        # Extract the article date and convert to timestamp
        date_str = response.xpath('//time/text()').get()
        if not date_str:
            date_str = response.xpath('//div[contains(@class, "time")]/text()').get()

        if date_str:
            date_str = date_str.strip()

            # Try to parse the Indonesian date format
            try:
                # Convert Indonesian month names to English
                id_to_en_months = {
                    'Januari': 'January',
                    'Februari': 'February',
                    'Maret': 'March',
                    'April': 'April',
                    'Mei': 'May',
                    'Juni': 'June',
                    'Juli': 'July',
                    'Agustus': 'August',
                    'September': 'September',
                    'Oktober': 'October',
                    'November': 'November',
                    'Desember': 'December'
                }

                # Extract date components using regex
                date_match = re.search(r'(\w+), (\d+) (\w+) (\d{4}) (\d{2}):(\d{2}) (\w+)', date_str)
                if date_match:
                    day_name, day, month_id, year, hour, minute, timezone = date_match.groups()
                    month_en = id_to_en_months.get(month_id, month_id)

                    # Reconstruct date string in English
                    date_str_en = f"{day_name}, {day} {month_en} {year} {hour}:{minute} {timezone}"
                    date_obj = datetime.strptime(date_str_en, self.date_format())
                    return int(date_obj.timestamp())

                # Try alternative format (e.g., "Rabu, 7 Agustus 2024 16:54 WIB")
                date_match = re.search(r'(\w+), (\d+) (\w+) (\d{4}) (\d{2}):(\d{2})', date_str)
                if date_match:
                    _, day, month_id, year, hour, minute = date_match.groups()
                    month_en = id_to_en_months.get(month_id, month_id)

                    # Create a datetime object
                    date_obj = datetime(int(year), list(id_to_en_months.keys()).index(month_id) + 1,
                                       int(day), int(hour), int(minute))
                    return int(date_obj.timestamp())
            except Exception as e:
                self.logger.error(f"Error parsing date: {e}")

        # If we can't parse the date, extract it from the URL
        try:
            url_match = re.search(r'/(\d{4})/(\d{2})/(\d{2})/', response.url)
            if url_match:
                year, month, day = url_match.groups()
                date_obj = datetime(int(year), int(month), int(day))
                return int(date_obj.timestamp())
        except Exception as e:
            self.logger.error(f"Error extracting date from URL: {e}")

        # Return current time if date not found
        return int(datetime.now().timestamp())

    def get_authors(self, response, entry=None) -> List[str]:
        # Extract the article authors
        authors = []

        # Try to find the author in the article
        author = response.xpath('//div[contains(@class, "reporter")]/a/text()').get()
        if not author:
            author = response.xpath('//div[contains(@class, "penulis")]/a/text()').get()
        if not author:
            author = response.xpath('//div[contains(text(), "Penulis:")]/a/text()').get()

        # Check if we found an author
        if author:
            author = author.strip()
            authors.append(author)

        # Try to find the editor
        editor = response.xpath('//div[contains(@class, "editor")]/a/text()').get()
        if not editor:
            editor = response.xpath('//div[contains(text(), "Editor:")]/a/text()').get()

        # Check if we found an editor
        if editor:
            editor = editor.strip()
            if editor not in authors:  # Avoid duplicates
                authors.append(editor)

        return authors

    def get_images(self, response, entry=None) -> List[str]:
        # Extract image URLs from the article
        images = []

        # Try to find the main image
        main_image = response.xpath('//div[contains(@class, "imgpreview")]//img/@src').get()
        if main_image:
            images.append(urljoin(response.url, main_image))

        # Try to find other images in the article
        other_images = response.xpath('//div[contains(@class, "txt-article")]//img/@src').getall()
        for img in other_images:
            img_url = urljoin(response.url, img)
            if img_url not in images:  # Avoid duplicates
                images.append(img_url)

        return images

    def get_next_page(self, response) -> List[str]:
        # No pagination for individual articles
        return []

    def get_document_urls(self, response, entry=None) -> list:
        # Extract document URLs (PDFs, etc.) from the article
        docs = response.xpath('//div[contains(@class, "txt-article")]//a[contains(@href, ".pdf") or contains(@href, ".doc") or contains(@href, ".docx")]/@href').getall()
        return [urljoin(response.url, doc) for doc in docs]

    def parse(self, response):
        # Get all article URLs from the page
        article_urls = self.get_articles(response)

        # Follow each article URL
        for url in article_urls:
            yield response.follow(url, callback=self.parse_article)

        # Check for next page
        next_pages = self.get_next_page(response)
        for next_page in next_pages:
            yield response.follow(next_page, callback=self.parse)

    def parse_article(self, response):
        # Create a dictionary to store the article data
        article = {}

        # Extract the required fields
        article['url'] = response.url
        article['title'] = self.get_title(response)
        article['body'] = self.get_body(response)

        # Get images and convert to JSON string
        images = self.get_images(response)
        article['images'] = json.dumps(images)

        # Get date as timestamp and convert to string
        date_timestamp = self.get_date(response)
        article['date'] = datetime.fromtimestamp(date_timestamp).strftime('%Y-%m-%d %H:%M:%S')

        # Get document URLs and convert to JSON string
        document_urls = self.get_document_urls(response)
        article['document_urls'] = json.dumps(document_urls)

        # Get authors and convert to string
        authors = self.get_authors(response)
        article['authors'] = ', '.join(authors) if authors else ''

        # Increment the articles_crawled counter
        self.crawler.stats.inc_value('articles_crawled')
        self.crawler.stats.inc_value('articles_successfully_scraped')

        # Return the article data
        self.logger.info(f"Extracted article: {article['title']}")
        return article

    def close(self):
        # Override the close method to handle the case where no articles are found
        # This prevents the exception from being raised in the parent class
        self.logger.info(f"Spider closed: {self.name}")
        # We don't call super().close() to avoid the exception
