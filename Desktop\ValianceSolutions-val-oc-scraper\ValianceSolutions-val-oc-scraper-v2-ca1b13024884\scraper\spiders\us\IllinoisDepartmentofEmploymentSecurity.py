from scraper.OCSpider import <PERSON><PERSON>pider
from scraper.utils.helper import body_normalization
import scrapy
from typing import Union
import json
from datetime import datetime

class IllinoisDepartmentOfEmploymentSecurity(OCSpider):
    name = 'IllinoisDepartmentOfEmploymentSecurity'
    
    country  = "US"

    start_urls_names = {
        'https://ides.illinois.gov/resources/newsroom.html':'News'
    }
    
    api_start_url = {
        'https://ides.illinois.gov/resources/newsroom.html': {
            'url': 'https://doit-web-content-management.ent.us-east4.gcp.elastic-cloud.com/api/as/v1/engines/ides-main/search',
            'payload': {
                "query": "unemployment",
                "page": {"size": 10, "current": 1},
                "filters": {"contenttype": ["news"]},
                "sort": {"articledate": "desc"}
            },
            'headers': {
                'Authorization': 'Bearer search-6vsm2asko372xhcp4ttjjm1e',
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'User-Agent': 'Mozilla/5.0',
                'Referer': 'https://ides.illinois.gov/resources/newsroom.html'
            }
        }
    }

    def parse_intermediate(self, response):
        start_url = response.meta.get('start_url')
        api_data = self.api_start_url.get(start_url)
        if not api_data:
            return
        api_url = api_data["url"]
        payload = api_data["payload"].copy()
        headers = api_data["headers"]
        current_page = response.meta.get("current_page", 1)
        payload["page"]["current"] = current_page
        yield scrapy.Request(
            url=api_url,
            method="POST",
            headers=headers,
            body=json.dumps(payload),
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "payload": payload,
                "current_page": current_page
            },
            dont_filter=True,
        )
        
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self) -> str:
        return "America/Chicago"
    
    @property
    def language(self):
        return "English"
    
    def get_articles(self, response) -> list:
        try:
            data = json.loads(response.text)
            articles = data.get("results", [])
            article_urls = [
                article.get("path", {}).get("raw", "")
                for article in articles
                if article.get("path")]
            return article_urls
        except json.JSONDecodeError:
            return []

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//div[contains(@class, 'cmp-title')]//h1/text()").get()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[contains(@class, 'cmp-text')]//p//text()").getall())

    def get_images(self, response) -> list[str]:
        return []
    
    def date_format(self) -> str:
        return '%Y-%m-%d'

    def get_date(self, response) -> str:
        date_text = response.xpath("//div[contains(@class, 'cmp-text')]//span[contains(@class, 'template__sub-title')]/text()").re_first(r"(\w+,\s\w+\s\d{1,2},\s\d{4})")
        if date_text:
            return datetime.strptime(date_text, "%A, %B %d, %Y").strftime("%Y-%m-%d")
        else:
            return None
        
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response, current_page) -> Union[None, int]:
        try:
            data = json.loads(response.text)
            total_pages = data.get("meta", {}).get("page", {}).get("total_pages", 0)
            if current_page < total_pages:
                return current_page + 1
        except (json.JSONDecodeError, KeyError, TypeError) as e:
            return None

    def go_to_next_page(self, response, start_url, current_page=1):
        api_url = response.meta.get("api_url")
        if not api_url:
            return
        next_page = self.get_next_page(response, current_page)
        if next_page:
            self.logger.info(f"Going to Page {next_page}")
            payload = response.meta.get("payload", {}).copy()
            payload["page"]["current"] = next_page
            yield scrapy.Request(
                url=api_url,
                method="POST",
                body=json.dumps(payload),
                headers=self.api_start_url[start_url]["headers"],
                callback=self.parse_intermediate,
                meta={
                    'current_page': next_page,
                    'start_url': start_url,
                    'api_url': api_url,
                    'payload': payload
                },
                dont_filter=True
            )
        else:
            self.logger.info("No more pages to fetch.")