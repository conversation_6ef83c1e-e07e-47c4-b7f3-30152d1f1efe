from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
import scrapy
import re
import unicodedata

class WisconsinDepartmentOfTransportation(OCSpider):
    name = 'WisconsinDepartmentOfTransportation'
    
    country = "US"

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350
            },
        "DOWNLOAD_DELAY": 2,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }
    
    HEADLESS_BROWSER_WAIT_TIME = 30000

    start_urls_names = {
        'https://wisconsindot.gov/Pages/about-wisdot/newsroom/default.aspx': 'News'
    }

    def parse_intermediate(self, response):
        all_articles = list(set(response.xpath("//div[@class='ms-rtestate-field']//ul/li//a[contains(@href,'news-rel')]//@href").getall()))
        total_articles = len(all_articles)
        articles_per_page = 100
        start_url = response.meta.get("start_url", response.url)
        if total_articles > 0:
            for start_idx in range(0, total_articles, articles_per_page):
                yield scrapy.Request(
                    url=start_url,
                    callback=self.parse,
                    meta={
                        'start_idx': start_idx, 
                        'articles': all_articles, 
                        'start_url': start_url,
                        'total_articles': total_articles
                    },
                    dont_filter=True
                )
        else:
            self.logger.warning("No articles found on the page")
            
    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Central"
    
    def get_articles(self, response) -> list:
        all_articles = response.meta.get('articles', [])
        start_idx = response.meta.get('start_idx', 0)
        end_idx = min(start_idx + 100, len(all_articles))
        return all_articles[start_idx:end_idx]
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self,response) -> str:
        title = response.xpath('//h1/text()').get().strip()
        return title
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class = "ms-rtestate-field"]//div//text() | //div[@class = "ms-rtestate-field"]//p//text() | //div[@class = "ms-rtestate-field"]//ul//text()').getall())

    def get_images(self, response) -> list[str]:
        return []

    def date_format(self) -> str:
        return "%B %d, %Y"
    
    def get_date(self, response) -> str:
        # Some articles are getting failed due to no date in article
        all_text = ''.join(response.xpath('//body//text()').getall())
        all_text = unicodedata.normalize("NFKC", all_text)
        all_text = re.sub(r'&#58;|:', ':', all_text)
        all_text = re.sub(r'\s+', ' ', all_text).strip()
        all_text = re.sub(r'\u200b', '', all_text)
        all_text = re.sub(r'([Rr])\s*elease\s*([Dd])\s*ate', r'\1elease \2ate', all_text)
        match = re.search(r'[Rr]elease\s*[Dd]ate[:\s]*([A-Za-z]+)\s*(\d{1,2})\s*,\s*(\d{4})', all_text)
        if match:
            month, day, year = match.groups()
            return f"{month} {day}, {year}"
        return None
        
    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        # No next page is there to scrap
        return None