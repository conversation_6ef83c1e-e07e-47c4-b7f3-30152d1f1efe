from typing import Optional
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
from datetime import datetime
import scrapy

class HeritageFoundations(OCSpider):
    name = "HeritageFoundations"

    country = "US"

    start_urls_names = {
        "https://www.heritage.org/press": "Press",
        "https://www.heritage.org/press/press-archive": "Press",
    }

    def parse_intermediate(self, response):
        articles = response.xpath('//div[contains(@class, "microsite-article-cards__card")]//a/@href |//div[@class="paragraph paragraph--type--report-chapter paragraph--view-mode--default"]//a/@href').getall()
        total_articles = len(articles)
        start_url = response.meta.get("start_url")
        for start_idx in range(0, total_articles, 100):
            yield scrapy.Request(
                    url=start_url,
                    callback=self.parse,
                    meta={
                        'start_idx': start_idx, 
                        'articles': articles, 
                        'start_url': start_url
                    },
                    dont_filter=True
            )
            
    charset = "utf-8"
            
    @property
    def language(self): 
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "America/New_York"
    
    def get_articles(self, response) -> list:
        all_articles = response.meta.get('articles', [])
        start_idx = response.meta.get('start_idx', 0)
        end_idx = start_idx + 100
        return all_articles[start_idx:end_idx]
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="headline article-headline"]/span/text()').get()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="article__body-copy"]//text()').getall()) 
        
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return"%m-%d-%Y"
    
    def get_date(self, response) -> str:
        date_str = response.xpath('//p[@class="article-general-info"]/text()').get().strip()
        return datetime.strptime(date_str, "%b %d, %Y").strftime("%m-%d-%Y")
            
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        # No next page to scrape
        return None