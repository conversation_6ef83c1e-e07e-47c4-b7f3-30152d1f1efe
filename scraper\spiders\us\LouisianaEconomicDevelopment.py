from scraper.OCSpider import OCSpider
import scrapy
from scraper.utils.helper import body_normalization
import json
import re

class LouisianaEconomicDevelopment(OCSpider):
    name = 'LouisianaEconomicDevelopment'
    
    country = "US"
    

    start_urls_names = {
        "https://www.opportunitylouisiana.gov/news": "News",
    }

    api_start_urls = {
        'https://www.opportunitylouisiana.gov/news': {
            "url": f"https://admin.opportunitylouisiana.gov/index.php?graphql&operationName=SearchResultsWithPagination&variables=%7B%22dateQuery%22%3Anull%2C%22terms%22%3Anull%2C%22pageSize%22%3A14%2C%22contentTypes%22%3A%5B%22POST%22%5D%2C%22sortField%22%3A%22DATE%22%2C%22sortOrder%22%3A%22DESC%22%2C%22offset%22%3A0%7D&extensions=%7B%22persistedQuery%22%3A%7B%22version%22%3A1%2C%22sha256Hash%22%3A%2253d58398a5697f2faf0edd000fd7242922a555ca3c397f41d7a6a53264dd67a7%22%7D%7D",
        }
    }

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            return
        api_url = response.meta.get("api_url") or api_data["url"]
        yield scrapy.Request(
            url=api_url,
            method="GET",
            headers={
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36",
                "Accept": "application/json",
                "Referer": "https://www.opportunitylouisiana.gov/news",
                "Origin": "https://www.opportunitylouisiana.gov",
                "Accept-Language": "en-US,en;q=0.9",
            },
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_url,
            },
        )
        
    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "US/Eastern"

    @property
    def language(self):
        return "English"
     
    def get_articles(self, response) -> list:
        try:
            data = json.loads(response.text)
            edges = data.get("data", {}).get("contentNodes", {}).get("edges", [])
            return [
                edge.get("node", {}).get("link")
                for edge in edges
                if edge.get("node", {}).get("link")
            ]
        except json.JSONDecodeError:
            return []


    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//h1//text()").get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='singlePost__content']//p//text() | //div[@class='singlePost__content']//ul//li//text()").getall())

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%B %d, %Y"

    def get_date(self, response) -> str:
        return response.xpath("//time//text()").get()
    
    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        api_url = response.meta.get("api_url")
        match = re.search(r'offset%22%3A(\d+)', api_url)
        if match:
            current_offset = int(match.group(1))
            next_offset = current_offset + 14
            return re.sub(r'offset%22%3A\d+', f'offset%22%3A{next_offset}', api_url)
        return None
    
    def go_to_next_page(self, response, start_url=None, current_page=None):
        articles = self.get_articles(response)
        if not articles:
            return
        next_page = self.get_next_page(response)
        if next_page:
            yield scrapy.Request(
                url=next_page,
                method="GET",
                headers={
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36",
                    "Accept": "application/json",
                    "Referer": "https://www.opportunitylouisiana.gov/news",
                    "Origin": "https://www.opportunitylouisiana.gov",
                    "Accept-Language": "en-US,en;q=0.9",
                },
                callback=self.parse, 
                meta={
                    "start_url": start_url or response.meta.get("start_url"),
                    "api_url": next_page,
                }
            )