from typing import List
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from urllib.parse import urljoin
import re
from datetime import datetime

class RobamMinimal(OCSpider):
    name = "RobamMinimal"
    # country = "CN"
    start_urls_names = {
        "https://www.robam.com/news.html": "ROBAM News",
        "https://www.robam.com/about/investment.html": "ROBAM Investment",
        "https://www.robam.com/about/company.html": "ROBAM Company Info",
    }
    charset = "utf-8"

    custom_settings = {
        "DOWNLOAD_DELAY": 2,
        "USER_AGENT": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    }

    @property
    def source_type(self) -> str:
        return "Industry Association"

    # @property
    # def language(self) -> str:
    #     return "Chinese"

    @property
    def timezone(self) -> str:
        return "Asia/Shanghai"

    def get_articles(self, response) -> list:
        # Different selectors for different sections
        if "news.html" in response.url:
            articles = response.xpath('//div[@class="news-list"]//a/@href').getall()
        elif "investment.html" in response.url:
            articles = response.xpath('//div[contains(@class, "investment-news")]//a/@href').getall()
        else:
            articles = []
        
        return [urljoin(response.url, link) for link in articles if link]

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response, entry=None) -> str:
        if "company.html" in response.url:
            return "About ROBAM Company"
        return response.xpath('//h1[contains(@class, "content-title")]/text()').get(default="").strip()

    def get_body(self, response, entry=None) -> str:
        if "company.html" in response.url:
            content = response.xpath('//div[contains(@class, "about-content")]//text()').getall()
        else:
            content = response.xpath('//div[contains(@class, "article-content")]//text()').getall()
        return body_normalization(content)

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response, entry=None) -> str:
        if "company.html" in response.url or "investment.html" in response.url:
            return datetime.now().strftime(self.date_format())
            
        date_str = response.xpath('//span[@class="news-time"]/text()').get()
        return re.findall(r'\d{4}-\d{2}-\d{2}', date_str or "")[0] if date_str else datetime.now().strftime(self.date_format())

    def get_authors(self, response, entry=None) -> List[str]:
        return ["ROBAM Official"] if "company.html" in response.url else []

    def get_images(self, response, entry=None) -> List[str]:
        selector = '//div[contains(@class, "about-content")]//img/@src' if "company.html" in response.url else '//div[contains(@class, "content")]//img/@src'
        return [urljoin(response.url, img) for img in response.xpath(selector).getall()]

    def get_next_page(self, response) -> List[str]:
        if "news.html" in response.url:
            next_page = response.xpath('//a[contains(text(), "下一页")]/@href').get()
            return [urljoin(response.url, next_page)] if next_page else []
        return []

    def get_document_urls(self, response, entry=None) -> list:
        return [urljoin(response.url, doc) for doc in response.xpath('//a[contains(@href, ".pdf")]/@href').getall()]

    def get_page_flag(self) -> bool:
        return True

    def get_meta(self, response, entry=None) -> list:
        return []

    def get_pdf(self, response, entry=None):
        return None