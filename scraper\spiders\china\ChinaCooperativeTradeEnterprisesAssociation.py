import json
from scraper.OCSpider import OCSpider
from scraper.middlewares import HeadlessBrowserProxy
import scrapy
from typing import List
from dotenv import load_dotenv
from scraper.utils.helper import body_normalization
from typing import Optional
import logging
load_dotenv()

class ChinaCooperativeTradeEnterprisesAssociation(OCSpider):
    name="ChinaCooperativeTradeEnterprisesAssociation"

    start_urls_names={
        "https://www.ccoop.org.cn/xhdt": "协会动态",
        "https://www.ccoop.org.cn/qydt": "企业动态",
        "https://www.ccoop.org.cn/gxzc": "供销之窗",
        "https://www.ccoop.org.cn/djgz": "党建工作",
        "https://www.ccoop.org.cn/tzgg": "通知公告"
    }

    api_start_urls = {
        "https://www.ccoop.org.cn/xhdt": {
            "url": "https://www.ccoop.org.cn/Designer/Common/GetData",
            "payload": {
                "dataType": "news",
                "key": "",
                "pageIndex": "0",
                "pageSize": "10",
                "selectCategory": "131021",
                "selectId": "",
                "dateFormater": "yyyy-MM-dd",
                "orderByField": "createtime",
                "orderByType": "desc",
                "templateId": "0",
                "postData": "",
                "es": "false",
                "setTop": "true",
                "__RequestVerificationToken": "dJhPfHI_rW1Ze_4hlD1AyMhDYvycSIRCXITx0-1xyZZo6goEEM_snNUhSwpULbt9H2WZ4EegAFoazj3UFivokNi2Ot-hTmpOZgvlKQEV08U1"
                },
        },
        "https://www.ccoop.org.cn/qydt": {
            "url": "https://www.ccoop.org.cn/Designer/Common/GetData",
            "payload": {
                "dataType": "news",
                "key": "",
                "pageIndex": "0",
                "pageSize": "10",
                "selectCategory": "140073",
                "selectId": "",
                "dateFormater": "yyyy-MM-dd",
                "orderByField": "createtime",
                "orderByType": "desc",
                "templateId": "0",
                "postData": "",
                "es": "false",
                "setTop": "true",
                "__RequestVerificationToken": "dJhPfHI_rW1Ze_4hlD1AyMhDYvycSIRCXITx0-1xyZZo6goEEM_snNUhSwpULbt9H2WZ4EegAFoazj3UFivokNi2Ot-hTmpOZgvlKQEV08U1"
                },
        },
        "https://www.ccoop.org.cn/gxzc": {
            "url": "https://www.ccoop.org.cn/Designer/Common/GetData",
            "payload": {
                "dataType": "news",
                "key": "",
                "pageIndex": "0",
                "pageSize": "10",
                "selectCategory": "132613",
                "selectId": "",
                "dateFormater": "yyyy-MM-dd",
                "orderByField": "createtime",
                "orderByType": "desc",
                "templateId": "0",
                "postData": "",
                "es": "false",
                "setTop": "true",
                "__RequestVerificationToken": "dJhPfHI_rW1Ze_4hlD1AyMhDYvycSIRCXITx0-1xyZZo6goEEM_snNUhSwpULbt9H2WZ4EegAFoazj3UFivokNi2Ot-hTmpOZgvlKQEV08U1"
                },
        },
        "https://www.ccoop.org.cn/djgz": {
            "url": "https://www.ccoop.org.cn/Designer/Common/GetData",
            "payload": {
                "dataType": "news",
                "key": "",
                "pageIndex": "0",
                "pageSize": "10",
                "selectCategory": "131018,131022,132066,530688",
                "selectId": "",
                "dateFormater": "yyyy-MM-dd",
                "orderByField": "createtime",
                "orderByType": "desc",
                "templateId": "0",
                "postData": "",
                "es": "false",
                "setTop": "true",
                "__RequestVerificationToken": "dJhPfHI_rW1Ze_4hlD1AyMhDYvycSIRCXITx0-1xyZZo6goEEM_snNUhSwpULbt9H2WZ4EegAFoazj3UFivokNi2Ot-hTmpOZgvlKQEV08U1"
                },
        },
        "https://www.ccoop.org.cn/tzgg": {
            "url": "https://www.ccoop.org.cn/Designer/Common/GetData",
            "payload": {
                "dataType": "news",
                "key": "",
                "pageIndex": "0",
                "pageSize": "10",
                "selectCategory": "140091",
                "selectId": "",
                "dateFormater": "yyyy-MM-dd",
                "orderByField": "createtime",
                "orderByType": "desc",
                "templateId": "0",
                "postData": "",
                "es": "false",
                "setTop": "true",
                "__RequestVerificationToken": "dJhPfHI_rW1Ze_4hlD1AyMhDYvycSIRCXITx0-1xyZZo6goEEM_snNUhSwpULbt9H2WZ4EegAFoazj3UFivokNi2Ot-hTmpOZgvlKQEV08U1"
                },
        },
    }

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        current_page = response.meta.get("current_page",0)
        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            self.logger.error(f"No API configuration found for start_url: {start_url}")
            return
        else:
            api_url = api_data["url"]
            if current_page==0:
                payload = api_data["payload"]
                payload["pageIndex"] = payload.get("pageIndex")
            else:
                payload = api_data["payload"]
                payload["pageIndex"] = str(int(payload.get("pageIndex")) - 1)
            yield scrapy.FormRequest(
                    url = api_url,
                    method = "POST",
                    headers={
                        "Content-Type": "application/x-www-form-urlencoded;",
                    },
                    dont_filter=True,
                    formdata = payload,
                    callback=self.parse,
                    meta={
                        "start_url": start_url,
                        "api_url": api_data["url"],
                        "payload": payload,
                        "current_page": int(payload["pageIndex"]) + 1
                    },
                )    

    charset = "utf-8"

    article_data_map={}

    @property
    def source_type(self) -> str:
        return 'IndustryAssociation'

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_articles(self, response) -> list:
        try:
            data = json.loads(response.text)
            articles = data.get("Data", [])
            article_urls = [
                self.construct_article_url(response,article)
                for article in articles
                if article
            ]
            return article_urls
        except json.JSONDecodeError as e:
            self.logger.error(f"Error decoding JSON: {e}")
            return []
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//*[@class='w-title']//text()").get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//*[@class='w-detail']//text()").getall())
    
    def get_images(self, response, entry=None) -> List[str]:
        return [response.urljoin(url) for url in response.xpath("//*[@class='w-detail']//img//@src").getall()]
    
    def get_authors(self, response, entry=None) -> list[str]:
        return []
    
    def get_document_urls(self, response, entry=None) -> list:
        return [url for url in response.xpath("//*[@class='w-detail']//a//@href").getall() if not url.startswith("http://mp.weixin.qq.com/")]
    
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        full_url = f"{response.request.meta.get('entry')}"
        return self.article_data_map.get(f'{full_url}')
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response, current_page):
        return str(int(current_page) + 1) if int(current_page) <= int(response.json().get("pages") or response.json().get("TotalPages") or 0) else None
    
    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        api_url = response.meta.get("api_url")
        if not api_url:
            return
        else:
            next_page = self.get_next_page(response, current_page)
            if next_page:
                payload = response.meta.get('payload')
                payload['pageIndex'] = next_page
                yield scrapy.http.FormRequest(
                    url=api_url,
                    method='POST',
                    formdata=payload,
                    headers={
                        "Content-Type": "application/x-www-form-urlencoded;",
                    },
                    callback=self.parse_intermediate,  # callback parse_intermediate
                    dont_filter=True,
                    meta={
                            "api_url": api_url,
                            'current_page': next_page, 
                            'start_url': start_url,
                            'payload': payload,
                        }
                )
            else:
                logging.info("No more pages to fetch.")
                yield None
    
    def construct_article_url(self, response, article):
        hbp = HeadlessBrowserProxy()
        LinkUrl = article.get('LinkUrl')
        date = article.get('QTime')
        fullUrl=hbp.get_proxy(response.urljoin(LinkUrl),timeout=5000)
        self.article_data_map[fullUrl]=date
        if fullUrl:
            return fullUrl  
        else: 
            return None