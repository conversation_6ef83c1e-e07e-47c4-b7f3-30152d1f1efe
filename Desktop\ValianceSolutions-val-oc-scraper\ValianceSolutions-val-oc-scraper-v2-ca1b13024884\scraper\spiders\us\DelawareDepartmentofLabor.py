from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import scrapy
import re

class DelawareDepartmentOfLabor(OCSpider):
    name = 'DelawareDepartmentOfLabor'

    country = "US"

    start_urls_names = {
        'https://news.delaware.gov/news-archives/':"News"
    }
    
    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self) -> str:
        return "US/Eastern"  
    
    @property
    def language(self):
        return "English"
    
    def parse_intermediate(self, response):
        archive_links = response.xpath("//div[@class='col-sm-6']//ul/li/a/@href").getall()
        month_pattern = r'https://news\.delaware\.gov/\d{4}/\d{2}/$'
        month_links = [link for link in archive_links if re.match(month_pattern, link)]
        for link in month_links:
            yield scrapy.Request(
                url=link,
                callback=self.parse,
                meta={'start_url': response.url}, 
                dont_filter=True
            )

    def get_articles(self, response) -> list:
        return response.xpath("//h3/a/@href").getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//header/h1/text()').get()
       
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@id="main_content"]//p//text()').getall())
    
    def get_images(self, response, entry=None) :
        return []

    def date_format(self) -> str:
        return '%B %d, %Y'

    def get_date(self, response) -> str:
        date_text = response.xpath("//header/p[contains(@class, 'text-muted')]/text()").getall()[-1]
        date_text = date_text.replace("Date Posted: ", "").strip()
        if date_text.startswith("| "):
            date_text = date_text[2:].strip()
        date_text = re.sub(r'^\w+, ', '', date_text)
        return date_text

    def get_authors(self, response, entry=None) -> list[str]:
        return []
    
    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        next_page = response.xpath('//div[@class="pagination"]//a[i[contains(@class, "fa-angle-right")]]/@href').get()
        return response.urljoin(next_page) if next_page else None