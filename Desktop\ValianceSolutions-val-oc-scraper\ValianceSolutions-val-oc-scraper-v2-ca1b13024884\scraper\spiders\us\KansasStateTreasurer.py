from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class KansasStateTreasurer(OCSpider):
    name = 'KansasStateTreasurer'
    
    country = "US"

    start_urls_names = {
        'https://www.kansasstatetreasurer.com/news.html': 'News'
    }
    
    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Central"
    
    def get_articles(self, response) -> list:
        return response.xpath("//div[@class= 'card-wrapper']//a/@href").getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self,response) -> str:
        title = response.xpath("//p[@class='MsoNormal']//span/b//text()").get()  # Example: https://www.kansasstatetreasurer.com/news-Treasurer-<PERSON><PERSON>-encourages-Kansans-save-America-Saves-Week
        if title:
            return title
        else:
            return response.xpath("//p[@class='MsoNormal']//b//span//text()").get()  # Example: https://www.kansasstatetreasurer.com/news-Treasurer-<PERSON>-<PERSON>-marks-National-Unclaimed-Property-Day
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//p[@class='MsoNormal']//span//span//text()").getall())    
            
    def get_images(self, response) -> list[str]:
        return []

    def date_format(self) -> str:
        return "%B %d, %Y"

    def get_date(self, response) -> str:
        return response.xpath("//p[@class='MsoNormal']//span//text()").re_first(r"([A-Za-z]+ \d{1,2}, \d{4})")
    
    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        # No next page is there to scrap
        return None