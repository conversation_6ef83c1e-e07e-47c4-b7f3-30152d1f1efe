from typing import Optional
from urllib.parse import parse_qs, urljoin, urlparse
from scraper.OCSpider import <PERSON><PERSON>pid<PERSON>
from scraper.utils.helper import body_normalization
from scraper.middlewares import HeadlessBrowserProxy
import scrapy

class AssociationForPromotionOfWestChinaResearchAndDevelopment(OCSpider):
    name = "AssociationForPromotionOfWestChinaResearchAndDevelopment"

    start_urls_names = {
        "https://www.chinawestern.org.cn/h-col-104.html#fai_468_top_0 ": "西促会动态",
        # "https://www.chinawestern.org.cn/h-col-104.html#fai_468_top_0 ": "西促会动态",    # This start URL is same as first start URL
        "https://www.chinawestern.org.cn/h-nr-j-4_17.html": "公告通知"
    }

    custom_settings = {
        "DOWNLOAD_DELAY": 2,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter'
    }

    charset = "utf-8"

    def parse_intermediate(self, response):
        hbp = HeadlessBrowserProxy()
        request = scrapy.Request(hbp.get_proxy(response.url, timeout=30000), callback=self.parse
        )
        request.meta['start_url'] = response.request.meta['start_url']
        yield request

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_articles(self, response) -> list:
        # Since the website has different structures for different sections, 
        # specific XPath expressions are used to extract article links accordingly. 
        url_xpaths = {
            "https://www.chinawestern.org.cn/h-col-104.html": 
                '//div[@class="m_news_list m_news_col_1 head_style_0"]//a/@href',
            "https://www.chinawestern.org.cn/h-nr-j-4_17.html": 
                '//div[@id="newsList31"]/div/table//td[@class="newsTitle"]//a/@href'
        }
        parsed_url = urlparse(response.url)
        base_url = f"{parsed_url.scheme}://{parsed_url.netloc}{parsed_url.path}"
        query_params = parse_qs(parsed_url.query)
        extracted_url = query_params.get('url', [None])[0]
        cleaned_url = extracted_url if extracted_url else base_url  # Remove query parameters
        xpath_expr = url_xpaths.get(cleaned_url, None)
        if xpath_expr:
            articles = response.xpath(xpath_expr).getall()
        else:
            return []
        articles = [urljoin(cleaned_url, i) for i in articles]
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//h1/text()").get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='richContent  richContent0']//text()").getall())
    
    def get_images(self, response) -> list:
        return response.xpath("//div[@class='richContent  richContent0']//img/@src").getall()   
    
    def date_format(self) -> str:
        return"%Y-%m-%d %H:%M"
    
    def get_date(self, response) -> str:
        date_text = response.xpath("//div[@class='leftInfo']//text()").re_first(r"\d{4}-\d{2}-\d{2} \d{2}:\d{2}")
        if date_text:
            return date_text.strip()
    
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        next_page = response.xpath("//div[@id='pagenation465']/span[@class='pageNext']/a/@href").get()
        baseurl = "https://www.chinawestern.org.cn"
        if next_page:
            # Ensure the next_page URL is correctly joined with the base URL
            if not next_page.startswith("http"):
                next_page = baseurl.rstrip("/") + "/" + next_page.lstrip("/")
            return next_page
        return None