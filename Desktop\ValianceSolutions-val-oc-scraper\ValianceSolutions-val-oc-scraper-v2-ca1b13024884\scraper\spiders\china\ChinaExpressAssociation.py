from typing import Optional
from urllib.parse import parse_qs, urljoin, urlparse
from bs4 import BeautifulSoup
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from scraper.middlewares import HeadlessBrowserProxy
import scrapy

class ChinaExpressAssociation(OCSpider):
    name = "ChinaExpressAssociation"

    start_urls_names = {
        "http://www.cea.org.cn/dongtai/index.html" : "行业资讯",
        "http://www.cea.org.cn/dongtai/china.html" : "中国快递协会动态",
        "http://www.cea.org.cn/dongtai/sheng.html" : "各省快递协会动态",
        "http://www.cea.org.cn/news/index.html" : "快递舆情",
        "http://www.cea.org.cn/news/number.html" : "数据发布",
        "http://www.cea.org.cn/news/international.html": "国际动态"  # Only for this start_url, pagination and data extraction is done through api for rest start_urls done through normal xpaths
    }

    custom_settings = {
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter'
    }

    api_start_url = {
        'http://www.cea.org.cn/news/international.html': 'http://www.cea.org.cn/news/interlist_{pageNo}.html'
    }

    charset = "utf-8"

    def parse_intermediate(self, response):
        start_url = response.request.meta.get('start_url')
        current_page = response.meta.get("current_page", 1)
        # Check if we are on the specific start URL and current_page is 2
        if start_url == "http://www.cea.org.cn/news/international.html" and current_page == 2:
            api_base_url = self.api_start_url.get(start_url)
            api_url = api_base_url.format(pageNo=current_page) if api_base_url else None
            if api_url:
                yield scrapy.Request(
                    url=api_url,
                    callback=self.parse,
                    meta={
                        "start_url": start_url,
                        "api_url": api_base_url,
                        "current_page": current_page,
                    },
                )
        else:
            hbp = HeadlessBrowserProxy()
            request = scrapy.Request(hbp.get_proxy(response.url, timeout=30000), callback=self.parse)
            request.meta['start_url'] = start_url
            yield request

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"

    @property
    def timezone(self):
        return "Asia/Shanghai"        

    def get_articles(self, response) -> list:
        start_url = response.meta.get('start_url')
        if start_url == "http://www.cea.org.cn/news/international.html":
            base_url = "http://www.cea.org.cn"
            html_content = response.text
            soup = BeautifulSoup(html_content, 'html.parser')
            links = []
            for dt in soup.find_all('dt'):
                a_tag = dt.find('a', href=True)
                if a_tag:
                    article_url = urljoin(base_url, a_tag['href'])
                    links.append(article_url)
            if links:
                return links
            else:
                return []    
        else:
            articles = response.xpath('//div[@class="left_box"]//ul/li//a/@href').getall()
            parsed_url = urlparse(response.url)
            query_params = parse_qs(parsed_url.query)
            extracted_url = query_params.get('url', [None])[0]
            return [urljoin(extracted_url, i) for i in articles]

    def get_href(self, entry) -> str:
        return entry    

    def get_title(self, response) -> str:
        return response.xpath("//h1/text()").get()   
    
    def get_body(self, response) -> str:
        body = body_normalization(response.xpath("//div[@class='news_con_b']//text()").getall())  # Example: http://www.cea.org.cn/content/details_13_24908.html
        if not body:
            body = body_normalization(response.xpath("//div[@class='rich_media_area_primary_inner']//text()").getall())  # Example: http://www.cea.org.cn/content/details_12_18940.html
        return body
    
    def get_images(self, response) -> list:
        return response.xpath("//div[@class='news_con_b']//p//img/@src").getall()
    
    def get_authors(self, response):
        return []
    
    def date_format(self) -> str:
        return"%Y年%m月%d日"    

    def get_date(self, response) -> str:
        return response.xpath("//div[@class='news_con_t']/i[1]/text()").re_first(r"\d{4}年\d{2}月\d{2}日")
    
    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> Optional[str]:
        start_url = response.meta.get('start_url')
        if start_url == "http://www.cea.org.cn/news/international.html":
            current_page = response.meta.get("current_page", 1)
            api_base_url = self.api_start_url.get(start_url)
            if api_base_url:
                return api_base_url.format(pageNo=current_page + 1)
        else:
            next_page = response.xpath("//div[@class='page']/a[contains(text(), '下一页')]/@href").get()
            if next_page:
                return urljoin("http://www.cea.org.cn", next_page)
        return None