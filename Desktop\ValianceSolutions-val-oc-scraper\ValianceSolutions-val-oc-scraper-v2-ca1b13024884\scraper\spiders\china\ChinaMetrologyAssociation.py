from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime

class ChinaMetrologyAssociation(OCSpider):
    name = "ChinaMetrologyAssociation"

    start_urls_names = {
        "http://www.cma-cma.org.cn/xhdt/index.html" : "协会动态",
        "http://www.cma-cma.org.cn/new/index.html" : "行业新闻",
        "http://www.cma-cma.org.cn/zongjutongzhi/index.html" : "总局通知",
        "http://www.cma-cma.org.cn/peixuntongzhi/index.html" : "培训通知", 
        # "http://www.cma-cma.org.cn/notice/index.html" : "通知公告", # Ignoring this URL as it contains below two start URLs
        "http://www.cma-cma.org.cn/guojiajiliangguichengguifanzhitongzhi/index.html" : "通知", # Section of http://www.cma-cma.org.cn/notice/index.html
        "http://www.cma-cma.org.cn/bumendifangjiliangguichengguifanzhitongzhi/index.html" : "修订通知" # Section of http://www.cma-cma.org.cn/notice/index.html
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"

    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        self.extract_articles_with_dates(response)  # Call to store URLs and dates
        return response.xpath("//div[@class='list-body-content']//li//a/@href").getall()
    
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//div[@class='article-content']//p[@class='article-title']//text()").get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='article-text']//text()").getall())
    
    def get_images(self, response) -> list:
        return response.xpath("//div[@class='article-text']/img/@src").getall()
    
    def get_authors(self, response):
        return []
    
    def get_document_urls(self, response, entry=None) -> list:
        return []
    
    def date_format(self) -> str:
        return "%Y-%m-%d %H:%M:%S"   

    def get_date(self, response) -> str:
        date = response.xpath("//div[@class='article-content']//p[@class='article-author']//text()").re_first(r"\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}")  # Example: http://www.cma-cma.org.cn/xhdt/2025-02-15/5564.html
        if not date:
            if not hasattr(self, 'article_date_map') or not self.article_date_map:
                self.extract_articles_with_dates(response)  # Extract dates if not already stored
            else:    
                article_url = response.url
                date = self.article_date_map.get(article_url, None)
        if date:
            # Handle both 'YYYY-MM-DD' and 'YYYY-MM-DD HH:MM:SS' formats
            if len(date) == 10:  # If it's only 'YYYY-MM-DD'
                date += " 00:00:00"  # Append default time
            try:
                date_obj = datetime.strptime(date, "%Y-%m-%d %H:%M:%S")
                return date_obj.strftime("%Y-%m-%d %H:%M:%S")  # Ensure consistent format
            except ValueError as e:
                self.logger.error(f"Date parsing error: {e} for date {date}")
                return None  # Return None if the format is still incorrect
        else:    
            return None  # Return None if no date is found
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        next_page = response.xpath("//div[@class='list-page']/a[contains(text(), '下一页')]/@href").get()
        if next_page:
            return response.urljoin(next_page)
        else:
            return None
    
    # Dates are present on start URL page so extracting date from start URL
    def extract_articles_with_dates(self, response):
        self.article_date_map = {}  # Reset the mapping for each response
        for article in response.xpath("//div[@class='list-body-content']/ul/li"):
            url = article.xpath("./a/@href").get()
            date = article.xpath("./span[@class='list-item-date']/text()").get()
            if url and date:
                full_url = response.urljoin(url)  # Ensure absolute URL
                self.article_date_map[full_url] = date  # Store in dictionary