import json
import logging
import re
import warnings
from abc import ABCMeta, abstractmethod
from datetime import datetime, timedelta
from typing import Dict, List, Union

import pandas as pd
import pytz
import requests
import scrapy
from bs4 import BeautifulSoup
from scrapy.http import Request
from scrapy.statscollectors import StatsCollector
from scrapy.utils.deprecate import method_is_overridden
from scrapy.utils.project import get_project_settings

#from common.configuration import Config
#from common.elasticsearch.Utils import ElasticsearchUtils
#from common.gcp.bq.Utils import utils as bq_utils
#from common.gcs.Utils import gcs_utils
from scraper.exceptions import LogicalInconsistencyError, UnhandledCaseError
# from common.gcp.datasource.Utils import utils
#from scraper.pipelines.storage.scrapy_error import Pipeline as ErrorPipeline
from scraper.utils.helper import get_source_tier, is_valid_doc_url
from twisted.python.failure import Failure

from .utils.Website import Website
from .wrapper import *


class OCSpider(scrapy.Spider):
    __metaclass__ = ABCMeta

    def __init__(self, name=None, **kwargs):
        super().__init__(name, **kwargs)
        # self.error_pipeline: ErrorPipeline = ErrorPipeline()
        # self.es_utils = ElasticsearchUtils(Config.config.elasticsearch)
        self.start_urls = []
        self.csv_data_dict = {}
        self.threshold = 0.6
        self.page = None
        self.tier = get_source_tier(self.name, source=getattr(self, "source", None))
        # this is a flag we set while running a backfill for a spider
        self.backfill = bool(kwargs.get("backfill", False))
        self.debug = bool(kwargs.get("debug", False))
        self.articles_failed_to_scrape = set()
        self.overall_article_set = set()

        # we need to set the date as the spider's timezone
        spider_timezone = pytz.timezone(self.timezone)

        if "date" in kwargs:
            self.run_date = datetime.strptime(kwargs["date"], "%Y-%m-%d %H:%M:%S")
            utc_date = pytz.utc.localize(self.run_date)
            self.run_date = utc_date.astimezone(spider_timezone)

        else:
            # use the current date as the run date
            self.run_date = datetime.now(spider_timezone)

        self.csv_data_dict = {}
        # if "page" in kwargs:
        # self.page = int(kwargs.get("page", 1))
        # self.run_date = datetime(2023, 8, 24)

        if "csv" in kwargs:
            self.csv_file_name = kwargs["csv"]
            df = pd.read_csv(self.csv_file_name)
            self.csv_data_dict = df.set_index(df.columns[0]).to_dict(orient='index')

        if "csv" in kwargs:
            self.csv_file_name = kwargs["csv"]
            df = pd.read_csv(self.csv_file_name)
            self.csv_data_dict = df.set_index(df.columns[0]).to_dict(orient='index')

        if not self.start_urls_names:
            raise AttributeError(
                "OC Spider could not start: 'start_urls_names' not found. Please see readme.md")

        if "start_url" in kwargs:
            if kwargs["start_url"] == "":
                for start_url in self.start_urls_names:
                    self.start_urls.append(start_url)
            else:
                self.start_urls = json.loads(kwargs["start_url"])
        else:
            """
            try:
                if not self.backfill:
                    logging.info(f"Saving start urls to staging.starturl_dim_v2 table for {self.name}")
                    bq_utils.record_start_url(
                        spider=self,
                        run_date=self.run_date,
                    )
            except Exception:
                logging.error(f"Error in recording start url for {self.name} - {self.run_date}", exc_info=True)
                raise Exception(f"Error in recording start url for {self.name} - {self.run_date}")
            """
            self.start_urls = list(self.start_urls_names.keys())

    exclude_rules: List[str] = []
    include_rules: List[str] = []

    websites: Dict[str, Website] = {}

    regex_multiline = r"\n{1,}"
    regex_space_eachline = r"\s+\n"

    charset: Union[List[str], str] = 'utf-8'

    custom_settings = {}

    @property
    @abstractmethod
    def source_type(self) -> str:
        raise NotImplementedError

    @property
    @abstractmethod
    def timezone(self) -> str:
        raise NotImplementedError

    @abstractmethod
    def get_articles(self, response) -> list:
        pass

    @abstractmethod
    def get_page_flag(self) -> bool:
        pass

    @abstractmethod
    def get_href(self, entry) -> str:
        pass

    @abstractmethod
    def get_title(self, response, entry=None) -> str:
        pass

    @abstractmethod
    def get_body(self, response, entry=None) -> str:
        pass

    @abstractmethod
    def date_format(self) -> str:
        pass

    @abstractmethod
    def get_date(self, response, entry=None) -> str:
        pass

    @abstractmethod
    def get_authors(self, response, entry=None) -> Union[List[str], str]:
        pass

    @abstractmethod
    def get_images(self, response, entry=None) -> List[str]:
        pass

    @abstractmethod
    def get_next_page(self, response) -> List[str]:
        pass

    @abstractmethod
    def get_pdf(self, response):
        pass

    @abstractmethod
    def get_meta(self, response) -> list:
        pass

    @abstractmethod
    def get_pdf(self, response, entry=None):
        pass

    @abstractmethod
    def get_meta(self, response) -> list:
        pass

    @abstractmethod
    def get_document_urls(self, response, entry=None) -> list:
        """
        This function should return a list of pdf urls
        [
            pdf_url_1,
            pdf_url_2,
            pdf_url_3
        ]
        """
        return []

    def get_title_or_body_empty_flag(self, article) -> dict[str, bool]:
        """
        Utility function to determine if the title or body is empty for an article object.

        :param article: The article object, expected to be an object of ArticleItem or similar.
        :return: A dictionary with keys "title_is_empty" and "body_is_empty" with boolean values
        """
        article_title = article["title"]
        article_body = article["body"]

        # convert to str if it is NoneType
        if article_title is None:
            article_title = ""
        if article_body is None:
            article_body = ""

        return {"title_is_empty": article_title.strip() == "", "body_is_empty": article_body.strip() == ""}

    def determine_article_has_empty_body_title(self, item):
        """
        Utility function to determine the destination table for the article based on the presence of body, title and media files.
        Sets the "destination_table" key in the item object to the appropriate table name.
        This method also increments the appropriate stats counter based on the article type.

        :param spider: The spider object
        :param item: The item object
        """
        # check if document_urls or image exists
        doc_or_img_exists = False
        if any(item.get("document_urls", [])) or any(item.get("images", [])):
            doc_or_img_exists = True

        is_title_or_body_empty = self.get_title_or_body_empty_flag(item)
        if not any(is_title_or_body_empty.values()):
            # Both title and body are present
            self.crawler.stats.inc_value("content_summary__articles_with_body_and_title")
        elif any(is_title_or_body_empty.values()):
            # when either title or body or both is empty
            if doc_or_img_exists:
                # Articles missing either body or title, but have media (docs/images)
                self.crawler.stats.inc_value("content_summary__articles_without_title_or_body_but_media_files")
            else:
                # Missing either title or body (or both), and missing media (docs/images) urls.
                self.crawler.stats.inc_value("content_summary__articles_without_title_body_media")
        else:
            logging.error("Unable to determine the article type")
            raise UnhandledCaseError(
                f"Unable to determine the table to be pushed to. {is_title_or_body_empty=}; {doc_or_img_exists=}")


    def start_requests(self):
        cls = self.__class__
        self.crawler.stats.set_value("count_summary__articles_crawled", 0)
        self.crawler.stats.set_value("count_summary__articles_successfully_scraped", 0)
        self.crawler.stats.set_value("count_summary__articles_exist_in_db", 0)
        self.crawler.stats.set_value("count_summary__articles_failed_to_scrape", 0)
        self.crawler.stats.set_value("count_summary__articles_filtered_due_to_include_exclude_rule", 0)
        self.crawler.stats.set_value("count_summary__articles_failed_to_scrape_due_to_404", 0)
        self.crawler.stats.set_value("tier", self.tier)
        self.crawler.stats.set_value("pagination_stats__pages_crawled", 1)
        # stats for articles that are missing body or title, but have media files
        self.crawler.stats.set_value("content_summary__articles_without_title_or_body_but_media_files", 0)
        # stats for articles that are missing body or title and do not have media files
        self.crawler.stats.set_value("content_summary__articles_without_title_body_media", 0)
        # stats for articles that have both body and title
        self.crawler.stats.set_value("content_summary__articles_with_body_and_title", 0)

        if self.backfill:
            self.get_already_scraped_articles([])

        if not self.start_urls and hasattr(self, 'start_url'):
            raise AttributeError(
                "Crawling could not start: 'start_urls' not found "
                "or empty (but found 'start_url' attribute instead, "
                "did you miss an 's'?)")
        if method_is_overridden(cls, self, 'make_requests_from_url'):
            warnings.warn(
                "Spider.make_requests_from_url method is deprecated; it "
                "won't be called in future Scrapy releases. Please "
                "override Spider.start_requests method instead "
                f"(see {cls.__module__}.{cls.__name__}).",
            )
            for url in self.start_urls:
                request = self.make_requests_from_url(url)
                request.meta['start_url'] = url
                yield request
        else:
            for url in self.start_urls:
                if hasattr(self, "parse_intermediate"):
                    callback = self.parse_intermediate
                else:
                    callback = self.parse
                if url == 'http://renshi.people.com.cn':
                    header = {"Host": "renshi.people.com.cn", "Referer": "http://renshi.people.com.cn/"}
                    request = Request(url, dont_filter=True, headers=header, callback=callback)
                else:
                    request = Request(url, dont_filter=True, callback=callback)
                request.meta['start_url'] = url
                yield request

    def get_already_scraped_articles(self, articles):
        # This is okay, for dev environment
        return None

    def is_already_crawled(self, start_url, _id):
        return self.websites.get(_id)

    def handle_http_error(self, failure: Failure) -> None:
        """This is a callback function that is called when an error occurs during the request.

        If the error is a 404, we log the error and increment the count of articles that failed to scrape due to 404
        else we log the error and traceback

        params:
            failure: Failure object that contains the error information

        returns:
            None
        """
        response = getattr(failure.value, "response", None)
        if response is None:
            logging.warning(f"No response object (network error or DNS issue) for: {failure.request.url}")
            self.crawler.stats.inc_value("count_summary__articles_failed_to_scrape_due_to_404")
        if failure.value.response.status == 404:
            logging.info(f"404 Error for this article url: {failure.request.url}")
            # increment the count of articles that failed to scrape due to 404
            self.crawler.stats.inc_value("count_summary__articles_failed_to_scrape_due_to_404")
        else:
            logging.error(failure.getTraceback())

    def parse(self, response):

        start_url = response.request.meta['start_url']
        try:
            page_iteration_flag = True
            articles = self.get_articles(response)
            # get articles
            # Limit only 100 articles per page for Ministries.
            if articles:
                articles = articles[:100]

            # extract the article urls and query BQ to check if they are already scraped
            if not self.backfill:
                self.get_already_scraped_articles([response.urljoin(self.get_href(entry)) for entry in articles])

            current_page_articles = set()
            for article_index, entry in enumerate(articles):
                href = self.get_href(entry)
                full_url = response.urljoin(href)
                current_page_articles.add(full_url)

            if not self.backfill and len(self.overall_article_set.intersection(current_page_articles)) == len(
                    current_page_articles):
                return

            for article_index, entry in enumerate(articles):
                href = self.get_href(entry)

                if article_index == len(articles) - 1:
                    is_last_article_of_page = True
                else:
                    is_last_article_of_page = False

                full_url = response.urljoin(href)
                self.overall_article_set.add(full_url)

                # If full url is already in start_url, then skip it
                # Reason: because some websites provide start_url as list of articles to scrape but we do not need to scrape them
                if full_url in self.start_urls:
                    continue

                # Start incrementing if the article url is not in the start_urls
                self.crawler.stats.inc_value("count_summary__articles_crawled")

                if len(self.include_rules) == 0:
                    should_scrap: bool = True
                    for exclude_rule in self.exclude_rules:
                        if re.compile(exclude_rule).findall(full_url):
                            should_scrap = False
                            break
                else:
                    should_scrap: bool = False
                    for include_rule in self.include_rules:
                        if re.compile(include_rule).findall(full_url):
                            should_scrap = True
                            break

                if should_scrap:

                    full_url = response.urljoin(href)
                    meta = {'retry_times': 0, 'is_last_article_of_page': is_last_article_of_page,
                            'main_page_response': response, 'start_url': start_url, "entry": entry}
                    # to address cases where we extract dates from the start url page, we are looping through the article xpaths instead of article urls them selves
                    if getattr(self, "date_in_meta", None):
                        meta["date"] = self.get_date(entry)
                    if self.is_already_crawled(start_url, full_url) is None:
                        if is_last_article_of_page:
                            only_for_date = True
                            meta["only_for_date"] = only_for_date

                        request = Request(
                            url=full_url,
                            method='GET',
                            encoding=response.request.encoding,
                            cookies=response.request.cookies,
                            headers=response.request.headers,
                            callback=self.parse_article,
                            errback=self.handle_http_error,
                            meta=meta
                        )
                        yield request
                    else:
                        self.crawler.stats.inc_value(
                            "count_summary__articles_exist_in_db"
                        )
                        page_iteration_flag = False
                        yield None
                else:
                    self.crawler.stats.inc_value(
                        "count_summary__articles_filtered_due_to_include_exclude_rule"
                    )
                    yield None

            self.page = self.get_page_flag()

            # if the self.backfill flag is set to True, we keep going to the next page until we reach the last page
            if self.backfill and len(articles) > 0:
                current_page = response.request.meta.get('current_page', 1)
                logging.info(f"Backfill: Going to Page {current_page}")
                yield from self.go_to_next_page(response, start_url, current_page=current_page)
            # We keep going to the next page until we reach an articles that exists in our DB
            elif self.page and len(articles) > 0 and page_iteration_flag:
                current_page = response.request.meta.get('current_page', 1)
                logging.info(f"Realtime: Going to Page {current_page}")
                yield from self.go_to_next_page(response, start_url, current_page=current_page)
            # We reach this else block only when we exhaust all articles that needs to be scraped
            # or if pagination is disabled.
            else:
                logging.info("No more pages to crawl")

        except Exception as _:
            logging.error("An Exception occurred in parse method, please take a look!!", exc_info=True)

    def go_to_next_page(self, response, start_url, current_page=None):
        next_page = self.get_next_page(response)
        if next_page is not None:
            self.crawler.stats.inc_value("pagination_stats__pages_crawled")
            next_page = response.urljoin(next_page)
            request = response.request.replace(url=next_page, callback=self.parse)
            request.meta['start_url'] = start_url
            request.meta['current_page'] = int(response.request.meta.get('current_page', 1)) + 1
            yield request
        else:
            yield None

    def parse_article(self, response) -> ArticleItem:
        record_processed_flag = False
        try:
            _id = response.url

            start_url = response.meta["start_url"]

            article = build_article(start_url, _id, self, response, )

            scrap_article = self.scrap_article(article, response)

            article_info = scrap_article.get('value')

            print(article_info)

            # If we fail to scrape the article, we raise an exception so that it can be caught downstream in parse
            if not scrap_article.get('success'):
                record_processed_flag = True
                self.crawler.stats.inc_value("count_summary__articles_failed_to_scrape")
                self.articles_failed_to_scrape.add(response.url)
                logging.error(f"Error Scraping Article: {article['error']}")

            if article_info is not None and scrap_article.get("success") is True:
                article_date = article_info.get("date", None)

                article_date = datetime.fromtimestamp(article_date, pytz.timezone(self.timezone)).date()
                print(f"Article Date ----- {article_date}")

            if scrap_article.get("success") is True:
                self.crawler.stats.inc_value("count_summary__articles_successfully_scraped")
                self.determine_article_has_empty_body_title(article_info)
                yield scrap_article.get('value')
            else:
                article['error'] = str(scrap_article.get('value'))
                logging.error(f"Error Scraping Article: {article['error']}")
                return None
        except:
            # there is a possibility of double counting if the code errors out after the increment for this flag is done
            # above, hence the need to check if this flag is False and only then increment the stats
            if not record_processed_flag:
                self.crawler.stats.inc_value("count_summary__articles_failed_to_scrape")
                self.articles_failed_to_scrape.add(response.url)
                print(traceback.format_exc())
            else:
                pass
            return None

    @error_safe
    def scrap_article(self, article: ArticleItem, response) -> ArticleItem:
        entry = response.request.meta.get("entry", None)
        if getattr(self, "parse_entry", None):
            article['title'] = self.get_title(response, entry=entry).strip()
            body = BeautifulSoup(self.get_body(response, entry=entry, ), features="lxml").get_text(separator="\n")
            date = self.get_date(response, entry=entry)
            article['images'] = self.get_images(response, entry=entry)
            authors = self.get_authors(response, entry=entry)

            if hasattr(self, "get_meta"):
                article["meta"] = self.get_meta(response, entry=entry)
            if hasattr(self, "get_subhead"):
                article["subhead"] = self.get_subhead(response, entry=entry)
        else:
            article['title'] = self.get_title(response).strip()
            body = BeautifulSoup(self.get_body(response, ), features="lxml").get_text(separator="\n")
            if not getattr(self, "date_in_meta", None):
                date = self.get_date(response)
            else:
                date = response.meta["date"]
            article['images'] = self.get_images(response)
            authors = self.get_authors(response)

            if hasattr(self, "get_meta"):
                article["meta"] = self.get_meta(response)
            if hasattr(self, "get_subhead"):
                article["subhead"] = self.get_subhead(response)

        article['start_url'] = response.meta.get("start_url", "")

        # Clean body
        article['body'] = re.sub(self.regex_space_eachline, "\n",
                                 re.sub(self.regex_multiline, "\\n", body, 0, re.MULTILINE).lstrip())

        date_format = self.date_format()
        tz = pytz.timezone(self.timezone)
        date_obj = datetime.strptime(date, date_format)

        article['language'] = getattr(self, "language", "Chinese")

        date_of_article = tz.localize(
            datetime(date_obj.year, date_obj.month, date_obj.day, date_obj.hour, date_obj.minute, date_obj.second),
            is_dst=None)

        tz = pytz.timezone(self.timezone)

        # we need to check if we are backfilling as well, because when backfilling self.run_date could be
        # less than the date of article eg: we want to backfill until 2024-01-01 and the date of article being 2024-08-08
        if date_of_article.date() > self.run_date.date() and not self.backfill:
            article['date'] = self.run_date.timestamp()
            logging.debug(f"The parsed date ({date}) is greater than the current date - ({self.run_date})")
        else:
            # Converting datetime to timestamp
            article['date'] = date_of_article.timestamp()

        if isinstance(authors, list) and all(isinstance(author, str) for author in authors):
            article['authors'] = authors
        elif isinstance(authors, str) and authors:
            article['authors'] = [authors]
        else:  # anything other than str or list are ignored
            article['authors'] = []

        article['country'] = getattr(self, "country", "China")

        document_urls = self.get_document_urls(response, entry=entry)

        if document_urls:
            article['document_urls'] = document_urls #[is_valid_doc_url(i) for i in document_urls]
            print("==========", article['document_urls'])
        return article

    def is_url_already_scraped(self, url):
        # index = Config.config.elasticsearch.ministries_index
        # return self.es_utils.find_by_id(index, url)
        return None

    def close(self):
        """
        try:
            # Do not dump anything if the env is dev
            if os.getenv("ENV", '').lower() != "dev":
                gcs_utils.dump_scrapy_stats_to_gcs(
                    self.run_date,
                    self.name,
                    self.crawler.stats.get_stats()
                )

                bq_utils.record_failed_article_urls(self, self.run_date, self.articles_failed_to_scrape)

        except Exception as e:
            logging.error(f"Unable to dump stats in cloud storage for {self.name} - {self.run_date} e: {e}")
        """
        # If any of the stats for some reason is not present, log and set the defaults to 0
        # since the stats are being set in one place, if one does not exist, the others will also not exist
        # therefore we can check if one exists and set all to 0
        if self.crawler.stats.get_value("count_summary__articles_crawled") == None:
            self.crawler.stats.set_value("count_summary__articles_crawled", 0)
            self.crawler.stats.set_value("count_summary__articles_successfully_scraped", 0)
            self.crawler.stats.set_value("count_summary__articles_exist_in_db", 0)
            self.crawler.stats.set_value("count_summary__articles_failed_to_scrape", 0)
            self.crawler.stats.set_value("count_summary__articles_failed_to_scrape_due_to_404", 0)
            self.crawler.stats.set_value("count_summary__articles_filtered_due_to_include_exclude_rule", 0)
            self.crawler.stats.set_value("tier", self.tier)
            self.crawler.stats.set_value("pagination_stats__pages_crawled", 0)
            self.crawler.stats.set_value("scraper_run_is_a_success", False)
            logging.error("Stats not found!")
            raise Exception(f"Stats not found for {self.name} - {self.date}")

        articles_with_body_and_title = self.crawler.stats.get_value(
            "content_summary__articles_with_body_and_title") or 0
        articles_without_title_or_body = self.crawler.stats.get_value(
            "content_summary__articles_without_title_body_media") or 0
        articles_without_title_or_body_but_media_files = self.crawler.stats.get_value(
            "content_summary__articles_without_title_or_body_but_media_files") or 0
        articles_successfully_scraped = self.crawler.stats.get_value(
            "count_summary__articles_successfully_scraped") or 0

        # raise warning the counts of articles do not match the total articles crawled
        effective_tot_success = articles_with_body_and_title + articles_without_title_or_body + articles_without_title_or_body_but_media_files
        if effective_tot_success != articles_successfully_scraped:
            _warning_msg = (
                f"!!!!!!!!!!!! Articles crawled({effective_tot_success}) and articles"
                f"successfully scraped({articles_successfully_scraped}) do not match for {self.name} - {self.run_date} !!!!!!!!!!!!")
            logging.warning(_warning_msg)
            self.crawler.stats.set_value("scraper_run_is_a_success", False)
            raise LogicalInconsistencyError(_warning_msg)

        # the self.crawler.stats.get_value("dupefilter/filtered") is available only when there is a duplicate request is found
        # else it will be None
        requests_filtered_due_to_duplicate = self.crawler.stats.get_value("dupefilter/filtered") or 0

        articles_to_scrape = (
                                     self.crawler.stats.get_value("count_summary__articles_crawled")
                                     - self.crawler.stats.get_value("count_summary__articles_exist_in_db")
                                     - self.crawler.stats.get_value(
                                 "count_summary__articles_filtered_due_to_include_exclude_rule")
                                     - self.crawler.stats.get_value(
                                 "count_summary__articles_failed_to_scrape_due_to_404")
                             ) - requests_filtered_due_to_duplicate

        # this means that no articles were crawled, which is there is something wrong with scraper
        if self.crawler.stats.get_value("count_summary__articles_crawled") == 0:
            self.crawler.stats.set_value("scraper_run_is_a_success", False)
            logging.error(f"Zero articles were crawled for {self.name} - {self.run_date}")
            raise Exception(f"Zero articles were crawled for {self.name} - {self.run_date}")
        elif articles_to_scrape == 0:
            self.crawler.stats.set_value("scraper_run_is_a_success", True)
            logging.info("No new articles to scrape")
        # the condition of >5 is kept because at low count it's very easy to go below threshold, example: 2/5
        elif (self.crawler.stats.get_value(
                "count_summary__articles_successfully_scraped") / articles_to_scrape) < self.threshold and articles_to_scrape > 5:
            self.crawler.stats.set_value("scraper_run_is_a_success", False)
            logging.error(f"Articles failed to scrape for {self.name} - {self.run_date}")
            raise Exception(f"Articles failed to scrape for {self.name} - {self.run_date}")
        elif (self.crawler.stats.get_value(
                "count_summary__articles_successfully_scraped") / articles_to_scrape) <= 0.2 and articles_to_scrape <= 5:
            self.crawler.stats.set_value("scraper_run_is_a_success", False)
            logging.error(f"Articles failed to scrape for {self.name} - {self.run_date}")
            raise Exception(f"Articles failed to scrape for {self.name} - {self.run_date}")
        else:
            self.crawler.stats.set_value("scraper_run_is_a_success", True)
            logging.info(
                f"Articles scraped successfully for {self.name} - {self.run_date} with {self.crawler.stats.get_value('count_summary__articles_crawled')} articles crawled and {self.crawler.stats.get_value('count_summary__articles_successfully_scraped')} articles successfully scraped")