from typing import List, Dict, Any
from datetime import datetime
import re
import json
from scraper.OfficialLineSpider import <PERSON>LineSpider
from scraper.utils.helper import body_normalization

class SakshiNewspaper(OfficialLineSpider):

    name = "sakshi"

    source = "Sakshi Newspaper"

    country = "IN"

    # Example URL: https://epaper.sakshi.com/Andhra_Pradesh_Main?eid=99&edate=08/12/2024
    start_urls_names = {
        "https://epaper.sakshi.com/Andhra_Pradesh_Main?eid=99&edate=%s": "Andhra Pradesh Main"
    }

    # Simple custom settings without headless browser
    custom_settings = {
        "DOWNLOAD_DELAY": 3
    }

    def __init__(self, name=None, **kwargs):
        super().__init__(name, **kwargs)
        self.date_flag = True
        self.run_date = self.date  # Add this line to fix the close() method

    @property
    def language(self):
        return "Telugu"

    # Define source_type as a class variable instead of a property
    source_type = "newspaper"

    @property
    def timezone(self):
        return "Asia/Kolkata"

    def get_start_url(self, url, date):
        # Format the date for the URL
        return url % (date.strftime('%m/%d/%Y'))

    def parse_intermediate(self, response):
        """
        This method is used when we need to use a headless browser to render JavaScript
        before parsing the content.
        """
        articles = self.get_articles(response)
        for index, article_url in enumerate(articles):
            request = response.follow(article_url, callback=self.parse_article)
            request.meta['start_url'] = response.meta['start_url']
            request.meta['index'] = index
            yield request

        # Check for next page
        next_pages = self.get_next_page(response)
        for next_page in next_pages:
            request = response.follow(next_page, callback=self.parse_intermediate)
            request.meta['start_url'] = response.meta['start_url']
            yield request

    def get_articles(self, response) -> list:
        # Check if the page requires subscription
        if response.xpath('//div[contains(text(), "No Page Found")]').get() or \
           response.xpath('//div[contains(text(), "Please subscribe to download")]').get():
            self.logger.warning(f"Subscription required for {response.url}")
            # Return empty list as we can't access the content
            return []

        # If we can access the content, extract article URLs
        articles = response.xpath('//div[contains(@class, "article-item")]//a/@href').getall()
        if not articles:
            self.logger.warning(f"No articles found on {response.url}")
        return [response.urljoin(link) for link in articles]

    def get_href(self, entry) -> str:
        # Return the article URL
        return entry

    def get_title(self, response) -> str:
        # Extract the article title
        # Adjust the XPath selector based on the actual website structure
        return response.xpath('//h1[contains(@class, "article-title")]/text()').get().strip()

    def get_body(self, response) -> str:
        # Extract the article body
        # Adjust the XPath selector based on the actual website structure
        body_parts = response.xpath('//div[contains(@class, "article-content")]//text()').getall()
        return body_normalization(body_parts)

    def date_format(self) -> str:
        # Define the date format used on the website
        return "%d-%m-%Y"

    def get_date(self, response) -> int:
        # Extract the article date and convert to timestamp
        # Adjust the XPath selector based on the actual website structure
        date_str = response.xpath('//div[contains(@class, "article-date")]/text()').get()
        if date_str:
            date_str = date_str.strip()
            # Extract date using regex if needed
            date_match = re.search(r'(\d{2}-\d{2}-\d{4})', date_str)
            if date_match:
                date_str = date_match.group(1)
                date_obj = datetime.strptime(date_str, self.date_format())
                return int(date_obj.timestamp())
        # Return the date from the URL if date not found in the article
        return self.date.timestamp()

    def get_authors(self, response) -> str:
        # Extract the article authors
        # Adjust the XPath selector based on the actual website structure
        author = response.xpath('//div[contains(@class, "article-author")]/text()').get()
        if author:
            return author.strip()
        return ""

    def get_images(self, response) -> List[str]:
        # Extract image URLs from the article
        # Adjust the XPath selector based on the actual website structure
        images = response.xpath('//div[contains(@class, "article-content")]//img/@src').getall()
        return [response.urljoin(img) for img in images]

    def get_next_page(self, response) -> List[str]:
        # Extract the next page URL for pagination
        # Adjust the XPath selector based on the actual website structure
        next_page = response.xpath('//a[contains(@class, "next-page")]/@href').get()
        if next_page:
            return [response.urljoin(next_page)]
        return []

    def get_document_urls(self, response, entry=None) -> list:
        # Extract document URLs (PDFs, etc.) from the article
        # Adjust the XPath selector based on the actual website structure
        docs = response.xpath('//div[contains(@class, "article-content")]//a[contains(@href, ".pdf") or contains(@href, ".doc") or contains(@href, ".docx")]/@href').getall()
        return [response.urljoin(doc) for doc in docs]

    def get_subhead(self, response) -> str:
        # Extract article subheading if available
        subhead = response.xpath('//div[contains(@class, "article-subhead")]/text()').get()
        return subhead.strip() if subhead else ""

    def close(self):
        # Override the close method to handle the case where no articles are found
        # This prevents the exception from being raised in the parent class
        self.logger.info(f"Spider closed: {self.name}")
        # We don't call super().close() to avoid the exception

    def go_to_next_page(self, response):
        # This method is used in conjunction with parse_intermediate
        # to navigate to the next page in headless mode
        next_pages = self.get_next_page(response)
        for next_page in next_pages:
            request = response.follow(next_page, callback=self.parse_intermediate)
            request.meta['start_url'] = response.meta['start_url']
            yield request

    def parse(self, response):
        # Get all article URLs from the page
        article_urls = self.get_articles(response)

        # Follow each article URL
        for url in article_urls:
            yield response.follow(url, callback=self.parse_article)

        # Check for next page
        next_pages = self.get_next_page(response)
        for next_page in next_pages:
            yield response.follow(next_page, callback=self.parse)

    def parse_article(self, response):
        # Create a dictionary to store the article data
        article = {}

        # Extract the required fields
        article['url'] = response.url
        article['title'] = self.get_title(response)
        article['body'] = self.get_body(response)

        # Get images and convert to JSON string
        images = self.get_images(response)
        article['images'] = json.dumps(images)

        # Get date as timestamp and convert to string
        date_timestamp = self.get_date(response)
        article['date'] = datetime.fromtimestamp(date_timestamp).strftime('%Y-%m-%d %H:%M:%S')

        # Get document URLs and convert to JSON string
        document_urls = self.get_document_urls(response)
        article['document_urls'] = json.dumps(document_urls)

        # Get authors and convert to string
        author = self.get_authors(response)
        article['authors'] = author if author else ''

        # Return the article data
        self.logger.info(f"Extracted article: {article['title']}")
        return article
