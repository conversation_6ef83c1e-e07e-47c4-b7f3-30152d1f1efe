from scraper.OCSpider import OCSpider
import scrapy
from datetime import datetime
from scraper.utils.helper import body_normalization

class JiangsuChangjiang(OCSpider):
    name = "JiangsuChangjiang"

    start_urls_names = {
       "https://www.jcetglobal.com/site/news" : "长电科技",
        # "https://www.jcetglobal.com/site/Report" : "长电科技",      ##It does not have date
        "https://www.jcetglobal.com/site/Finance_news" : "长电科技",
        }

    charset = "utf-8"
    
    @property
    def source_type(self) -> str:
        return "private_enterprise"
    
    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list: 
        return response.xpath('//li[@style="position: relative"]/a[@class="link"]/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1//text()').get().strip()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="item"]//p//text()').getall())
    
    def date_format(self) -> str:
        return "%B %d, %Y"

    def get_date(self, response) -> str:
        date_str = response.xpath('//strong/text()').get()
        if date_str:
            try:
                return datetime.strptime(date_str.strip(), "%Y-%m-%d").strftime("%B %d, %Y")
            except ValueError:
                return None
        return None

    def get_authors(self, response):
        return []

    def get_images(self, response) -> list:
        return []

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        next_page = response.xpath('//li[@class="next"]/a/@href').get()
        if next_page:
            return response.urljoin(next_page)
        return None