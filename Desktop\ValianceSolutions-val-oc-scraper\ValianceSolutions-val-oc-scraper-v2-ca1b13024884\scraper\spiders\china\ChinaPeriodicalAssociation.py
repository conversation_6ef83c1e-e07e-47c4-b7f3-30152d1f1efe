from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ChinaPeriodicalAssociation(OCSpider):
    name = "ChinaPeriodicalAssociation"

    start_urls_names = {
        "http://www.cpa.chinajournal.net.cn/WKE2/WebPublication/wkList.aspx?columnID=c1ab6d3a-d380-471c-83a7-b300a43e27fe": "中国期刊协会"
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"
    
    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        # Calling article_date_mapping function to update "article url : date" mapping
        self.article_date_mapping(response)
        return response.xpath('//div[@id="a5"]//div[@class="listform"]/table[1]/tr/td/a/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="column_titbox"]//h3//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="column_contbox_text"]//p//text()').getall())
    
    def get_images(self, response) -> list:
        return [
            response.urljoin(url)
            for url in response.xpath('//div[@class="column_contbox_text"]//p//img/@src').getall()
        ]
    
    def date_format(self) -> str:
        return "%Y/%m/%d"
    
    # Date is not present in the article page, but present in the start_url page along with article urls
    def get_date(self, response) -> str:
        article_url = response.url
        article_date = self.article_to_date_mapping.get(article_url, None)
        if article_date:
            return article_date
        else:
            self.logger.error(f"No date found for URL: {article_url}")
            return None
    
    def get_authors(self, response):
        return []
    
    article_to_date_mapping = {}  # Variable to store the article URL to date mapping

    # Date is only present in the start_url page, So creating a hashmap to store the article URL to date mapping
    def article_date_mapping(self, response):
        mapping = {}
        entries = response.xpath('//div[@id="a5"]//div[@class="listform"]/table[1]/tr/td')
        for entry in entries: 
            url = entry.xpath('.//a/@href').get()
            # Eg date formats present: 2024/9/11 or 2024/11/7 2024/1/7 or 2024/11/17
            date = entry.xpath('./em/text()').re_first(r'\[(\d{4}/\d{1,2}/\d{1,2})\]')    # \d{1,2}: Matches one or two digits for the month and day, accommodating single-digit and double-digit numbers.
            if not date:
                self.logger.debug(f"Missing date for URL: {url}, entry content: {entry.get()}")
            full_url = response.urljoin(url)
            mapping[full_url] = date
        self.article_to_date_mapping.update(mapping)
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        try:
            next_page=response.xpath("//td/a[contains(text(), ' 下页')]/@href").get()
            if next_page:
                next_page_url = response.urljoin(next_page)
                if next_page_url == response.url:
                    return None
                return next_page_url
            return None
        except Exception as e:
            self.logger.error(f"Error extracting next page link from page {response.url}: {e}")
            return None