from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from typing import List, Union, Optional
from datetime import datetime
import scrapy

class NewJerseyEconomicDevelopmentAuthority(OCSpider):

    name = "NewJerseyEconomicDevelopmentAuthority"

    country = "US"

    charset = "utf-8"

    start_urls_names = {
        "https://www.njeda.gov/press-room/": "Press Releases"
    }

    @property
    def language(self) -> str:
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self) -> str:
        return "US/Eastern"

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url", response.url)
        current_page = response.meta.get("current_page", 1)
        for article in self.get_articles(response):
            yield scrapy.Request(
                url=self.get_href(article),
                callback=self.parse,
                meta={'start_url': start_url, 'current_page': current_page}
            )
        yield from self.go_to_next_page(response, start_url, current_page)

    def get_articles(self, response) -> List[str]:
        return response.xpath("//a[contains(text(), 'Read More')]/@href").getall()

    def get_href(self, entry: str) -> str:
        return entry

    def get_title(self, response) -> Union[str, None]:
        return response.xpath("//h1/text()").get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//p//text()").getall())

    def get_images(self, response) -> List[str]:
        return []

    def date_format(self) -> str:
        return "%B %d, %Y"

    def get_date(self, response) -> str:
        date_text = response.xpath("//div[@class='text-blue-100'][1]/text()").get()
        return date_text.strip() if date_text else datetime.today().strftime(self.date_format())

    def get_authors(self, response) -> List[str]:
        return []

    def get_page_flag(self) -> bool:
        return False

    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        pagination_links = response.xpath("//a[contains(@class, 'py-2 px-4')]/@href").getall()
        next_page = None
        for link in pagination_links:
            if f"paged={current_page}" in link:
                next_page = link.replace(f"paged={current_page}", f"paged={current_page + 1}")
                break
        if next_page:
            next_page_url = f"https://www.njeda.gov/press-room/?{next_page}"
            yield scrapy.Request(
                url=next_page_url,
                method='GET',
                callback=self.parse_intermediate,
                meta={'current_page': current_page + 1, 'start_url': start_url}
            )
        else:
            self.logger.info("No more pages to fetch.")