from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from scraper.middlewares import HeadlessBrowserProxy
import scrapy

class OhioDevelopmentServicesAgency(OCSpider):
    name = "OhioDevelopmentServicesAgency"

    country = "US"
    
    charset="utf-8"

    start_urls_names = {
        "https://development.ohio.gov/home/<USER>/all-news/" : "News"
    }

    def parse_intermediate(self, response):
        hbp = HeadlessBrowserProxy()
        request = scrapy.Request(hbp.get_proxy(response.url, timeout=10000), callback=self.parse
        )
        request.meta['start_url'] = response.request.meta['start_url']
        yield request

    @property
    def language(self) -> str:
        return "English"
    
    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self) -> str:
        return "US/Eastern"
    
    def get_articles(self, response) :
        return response.xpath('//a[@class="core-news__item js-iop-item js-iop-item--visible"]/@href').getall()
    
    def get_href(self, entry: str) -> str:
        return entry

    def get_title(self, response) :
        return response.xpath('//h1[@class="odx-content__title"]//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//section[@class="odx-content__body"]//p//text()').getall())

    def get_images(self, response) :
        return response.xpath('//div[@class="ct-container sectionheader-container"]//img/@src').getall()

    def date_format(self) -> str:
        return "%B %d, %Y"

    def get_date(self, response) -> str:
        return response.xpath('//div[@class="odx-content__info margin-bottom-sm"]//span/text()').get()
    
    def get_authors(self, response) :
        return []
    
    def get_document_urls(self, response, entry=None):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        #All article_urls present in 1st page only
        return None

    
    
    