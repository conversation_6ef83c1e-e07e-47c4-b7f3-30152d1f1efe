from datetime import datetime
from typing import Optional
import scrapy
from scraper.OCSpider import <PERSON><PERSON>pider
from scraper.utils.helper import body_normalization

class ConnecticutDepartmentOfLabor(OCSpider):
    name = 'ConnecticutDepartmentOfLabor'
    
    country = "US"

    year = datetime.now().year #This Line is added to fetch the data starting from Current Year 

    start_urls_names = {
    f'https://portal.ct.gov/dolcommunications/news/press-room/{year}': 'Press Room'
    }
    
    include_rules = [
    r"^https://portal\.ct\.gov/dolcommunications/news/press-room/\d{4}/.*",
    r"^https://portal\.ct\.gov/governor/news/press-releases/\d{4}/.*"
    ]
    
    custom_settings = {
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }
     
    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "America/New_York"
    
    def get_articles(self, response) -> list:
        self.get_articles_with_dates(response)
        return list(self.article_date_map.keys())
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self,response) -> str:
        return response.xpath('//div[@class="content"]/h3/text()|//h1[@class="cg-c-article__main-title"]/text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="content"]/p//text() | //div[@class="content"]/ul[not(@class)]//li//text()').getall())
    
    def get_images(self, response) -> list[str]:
        return []
    
    def date_format(self) -> str:
        return "%m/%d/%Y"
    
    def get_date(self, response) -> Optional[str]:
        response_url = response.url.strip().lower()
        if response_url in self.article_date_map:
            return self.article_date_map[response_url]
        response_filename = response_url.split("/")[-1]
        for link, date in self.article_date_map.items():
            if response_filename == link.strip().lower().split("/")[-1]:
                return date

    def get_authors(self, response):
        return []
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        next_page = response.xpath('//div[@class="cg-c-pagination cg-c-pagination--align-center"]//a[contains(text(), "Next")]/@href').get()
        return response.urljoin(next_page) if next_page else None
                
    def go_to_next_page(self, response, start_url, current_page=None):
        start_url = response.meta.get("start_url", response.url)
        current_year = response.meta.get("current_year", datetime.now().year)
        next_page = self.get_next_page(response)
        if next_page:
            yield scrapy.Request(
                url=next_page,
                callback=self.parse,
                meta={
                    "start_url": start_url,
                    "current_year": current_year
                }
            )
        else:
            previous_year = current_year - 1
            url = f"https://portal.ct.gov/dolcommunications/news/press-room/{previous_year}/"
            yield scrapy.Request(
                url=url,
                callback=self.parse,
                meta={
                    "start_url": start_url,
                    "current_year": previous_year
                }
            )
    def get_articles_with_dates(self, response) -> list:
        items = response.xpath('//ul[@class="list--desc"]/li')
        if not hasattr(self, "article_date_map") or not isinstance(self.article_date_map, dict):
            self.article_date_map = {}
        for item in items:
            date = item.xpath('.//span[@class="date"]/text()').get()
            link = item.xpath('.//a/@href').get()
            if link and date:
                full_link = response.urljoin(link.strip())
                self.article_date_map[full_link] = date.strip()
        return list(self.article_date_map.keys())