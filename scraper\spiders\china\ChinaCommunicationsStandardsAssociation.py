import time
from typing import Optional
import requests
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
from scrapy.http import Request
from dotenv import load_dotenv
import json
load_dotenv()


class ChinaCommunicationsStandardsAssociation(OCSpider):
    name = "ChinaCommunicationsStandardsAssociation"

    start_urls_names = {
        "https://ccsa.org.cn/more/?classifyId=13&title=%E9%80%9A%E7%9F%A5%E5%85%AC%E5%91%8A": "通知公告",
        "https://ccsa.org.cn/more/?classifyId=17&title=%E5%B7%A5%E4%BD%9C%E5%8A%A8%E6%80%81": "工作动态",
        "https://ccsa.org.cn/more/?classifyId=18&title=%E4%BA%A7%E4%B8%9A%E8%B5%84%E8%AE%AF": "产业资讯"
    }

    api_start_urls = {
        "https://ccsa.org.cn/more/?classifyId=13&title=%E9%80%9A%E7%9F%A5%E5%85%AC%E5%91%8A": 
            "https://www.ccsa.org.cn/api/portals/news/list?page={page}&limit=20&classifyId=13&status=1&t={timestamp}",
        "https://ccsa.org.cn/more/?classifyId=17&title=%E5%B7%A5%E4%BD%9C%E5%8A%A8%E6%80%81": 
            "https://www.ccsa.org.cn/api/portals/news/list?page={page}&limit=20&classifyId=17&status=1&t={timestamp}",
        "https://ccsa.org.cn/more/?classifyId=18&title=%E4%BA%A7%E4%B8%9A%E8%B5%84%E8%AE%AF": 
            "https://www.ccsa.org.cn/api/portals/news/list?page={page}&limit=20&classifyId=18&status=1&t={timestamp}"
    }

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350,
        },
        "DOWNLOAD_DELAY": 2,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    HEADLESS_BROWSER_WAIT_TIME = 50000    # 50 Seconds wait time

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        current_page = response.meta.get("current_page", 1)
        timestamp = int(time.time() * 1000)  # Current Unix timestamp in milliseconds
        api_start_url = self.api_start_urls[start_url]
        api_url = api_start_url.format(page=current_page, timestamp=timestamp)
        yield Request(
            url=api_url,
            callback=self.parse,
            meta={
                "current_page": current_page,
                "start_url": start_url,
            },
        )
    
    def get_articles(self, response) -> list:
        try:
            data = json.loads(response.text)
            records = data.get("page", {}).get("list", [])
            articles = [
                f"https://ccsa.org.cn/detail/?id={record.get('id')}&title={record.get('articleName')}&classifyName"
                for record in records if record.get("id") and record.get("articleName")
            ]
            return articles
        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to decode JSON from response: {e}")
            return []
    
    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"

    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_href(self, entry) -> str:
        href = entry if isinstance(entry, str) else str(entry)
        return href
    
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_title(self, response) -> str:
        return response.xpath('//h1//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@id="content"]//p//text()').getall())
    
    def get_images(self, response) -> list:
        images = response.xpath('//div[@class="Section0"]/p//img/@src').getall()
        if not images:
            images = response.xpath('//div[@id="content"]/p//img/@src').getall()
        return images
    
    # Document urls are not present in xpaths, so fetching them from API response
    def get_document_urls(self, response, entry=None) -> list:
        id_param = response.url.split("?id=")[-1].split("&")[0]
        timestamp = int(time.time() * 1000)
        api_url = f"https://www.ccsa.org.cn/api/portals/news/info/{id_param}?t={timestamp}"
        try:
            api_response = requests.get(api_url)
            if api_response.status_code != 200:
                return []
            api_data = api_response.json()
            attach_url_json = json.loads(api_data.get("newsArticle", {}).get("attachUrlJson", []))
            if len(attach_url_json) > 0:
                document_urls = [attach_url_json[0].get("response", {}).get("data", {}).get("url", None)]
            else:
                document_urls = []
            return document_urls
        except requests.RequestException as e:
            self.logger.error(f"Error occurred while fetching API data: {e}")
            return []
        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to decode API JSON response: {e}")
            return []
    
    def get_date(self, response) -> str:
        return response.xpath('//div[@class="article-time"]//text()').re_first(r"\d{4}-\d{2}-\d{2}")
    
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response, current_page: Optional[int] = 1) -> Optional[str]:
        try:
            data = json.loads(response.text)
            max_pages = data.get("page", {}).get("totalPage", 1)
            self.logger.info(f"Extracted max_pages: {max_pages} from API response.")
        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to decode JSON from response: {e}")
            return None
        next_page = current_page + 1
        if next_page > max_pages:
            self.logger.info(f"Reached maximum page limit ({max_pages}).")
            return None
        start_url = response.meta.get("start_url")
        api_start_url = self.api_start_urls[start_url]
        timestamp = int(time.time() * 1000)
        next_page_url = api_start_url.format(page=next_page, timestamp=timestamp)
        self.logger.debug(f"Constructed next page URL: {next_page_url}")
        return next_page_url

    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        next_page_url = self.get_next_page(response, current_page)
        if next_page_url:
            yield Request(
                url=next_page_url,
                callback=self.parse_intermediate,
                meta={
                    "start_url": start_url,
                    "current_page": current_page + 1,
                },
            )
        else:
            self.logger.info(f"No more pages to crawl for start_url: {start_url}")