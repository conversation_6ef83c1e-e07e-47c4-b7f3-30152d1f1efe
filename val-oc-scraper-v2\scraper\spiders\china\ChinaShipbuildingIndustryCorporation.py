from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import re

class ChinaShipbuildingIndustryCorporation(OCSpider):
    name = "ChinaShipbuildingIndustryCorporation"

    start_urls_names = {
    "http://www.china-csicpower.com.cn/n377/n754/index.html": "中国动力",
    "http://www.china-csicpower.com.cn/n377/n378/index.html": "中国动力",
    "http://www.china-csicpower.com.cn/n377/n766/index.html": "中国动力",
    "http://www.china-csicpower.com.cn/n377/n379/index.html": "中国动力",
    "http://www.china-csicpower.com.cn/n381/n383/index.html": "中国动力",
    "http://www.china-csicpower.com.cn/n400/index.html": "中国动力",
}

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "SOE"
    
    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_articles(self, response) -> list:
        articles = response.xpath("//a[contains(@href, 'content.html')]/@href").getall()
        return articles
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:  
        return response.xpath('//div[@class="article_title"]//text()').get()
    
    def get_body(self,response) -> str:
        return body_normalization(response.xpath('//div[@class="article_text" and @id="zoom"]//text()').getall())
    
    def date_format(self) -> str:
        return '%Y-%m-%d'

    def get_date(self, response) -> str:
        text = response.xpath('//div[@class="article_attr"]//text()').getall()
        full_text = " ".join(text)
        date = re.search(r"\d{4}-\d{2}-\d{2}", full_text)
        return date.group(0) if date else None

    def get_authors(self, response):
        return []
    
    def get_document_urls(self, response, entry=None):
        return response.xpath('//a[contains(@href, ".pdf")]/@href').getall()
    
    def get_images(self, response) -> list:
        return response.xpath("//div//p//img//@src").getall()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        current_url = response.url
        match = re.search(r'index_(\d+)_(\d+)\.html', current_url)
        if match:
            prefix, current_num = match.groups()
            next_num = int(current_num) - 1
            if next_num < 1:
                return None
            return current_url.replace(f'index_{prefix}_{current_num}.html', f'index_{prefix}_{next_num}.html')
        elif "index.html" in current_url:
            hrefs = response.xpath("//a[contains(@href, 'index_')]/@href").getall()
            max_num = 0
            prefix = None
            for href in hrefs:
                page_match = re.search(r'index_(\d+)_(\d+)\.html', href)
                if page_match:
                    pfx, num = page_match.groups()
                    if int(num) > max_num:
                        max_num = int(num)
                        prefix = pfx
            if prefix and max_num:
                return current_url.replace("index.html", f"index_{prefix}_{max_num}.html")
        return None