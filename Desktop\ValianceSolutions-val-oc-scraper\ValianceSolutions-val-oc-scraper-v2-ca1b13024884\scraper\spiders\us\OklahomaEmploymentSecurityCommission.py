from typing import Optional
from scraper.OCSpider import <PERSON><PERSON>pider
from scraper.utils.helper import body_normalization
from datetime import datetime
import urllib.parse
import scrapy
import json
import re

class OklahomaEmploymentSecurityCommission(OCSpider):
    name = "OklahomaEmploymentSecurityCommission"

    country = "US"

    start_urls_names = {
        "https://oklahoma.gov/oesc/about/newsroom.html": "Latest News",
    }

    api_start_urls = {
        "https://oklahoma.gov/oesc/about/newsroom.html": {
            "url": "https://oklahoma.gov/content/sok-wcm/en/oesc/about/newsroom/jcr:content/responsivegrid-second/newslisting.json?",
            "payload": {
                "page": "1"
            }
        }
    }

    custom_settings = {
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
        "DOWNLOADER_MIDDLEWARES": {'scraper.middlewares.HeadlessBrowserProxy': 350},
        "DOWNLOAD_DELAY": 5,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    HEADLESS_BROWSER_WAIT_TIME = 30000

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36"
    }

    charset = "utf-8"

    @property
    def language(self): 
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self):
        return "America/Chicago"

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            return
        payload = response.meta.get("payload", api_data["payload"].copy())
        api_url = api_data["url"].rstrip("?")
        full_api_url = f"{api_url}?{urllib.parse.urlencode(payload)}"
        yield scrapy.Request(
            url=full_api_url,
            method="GET",
            headers=self.headers,
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "payload": payload,
                "current_page": payload["page"]
            }
        )

    def get_articles(self, response) -> list:
        try:
            match = re.search(r"<pre.*?>(.*?)</pre>", response.text, re.DOTALL)
            if not match:
                return []
            json_str = match.group(1)
            data = json.loads(json_str)
        except Exception:
            raise
        base_url = "https://oklahoma.gov"
        items = data.get("items", [])
        full_links = []
        for item in items:
            relative_url = item.get("newsUrl")
            if relative_url:
                full_links.append(base_url + relative_url)
        return full_links

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        title = response.xpath('//h1[@class="cmp-title__text"]/text()').get()
        return title

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@id="text-fc19f6ff38"]/p | //div[@id="text-fc19f6ff38"]/ul/li').getall()) 

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%m-%d-%Y"

    def get_date(self, response) -> str:
        clean_date = response.xpath("//div[@aria-label='created-date']/span/text()").get().split(', ', 1)[1]
        return datetime.strptime(clean_date, "%B %d, %Y").strftime("%m-%d-%Y") 

    def get_authors(self, response):
        return []

    def get_next_page(self, response, current_page: Optional[int] = 1) -> Optional[int]:
        next_page = int(current_page) + 1
        articles = self.get_articles(response)
        return next_page

    def get_page_flag(self) -> bool:
        return False

    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        api_data = self.api_start_urls.get(start_url)
        api_url = api_data["url"]
        payload = response.meta.get("payload", {}).copy()
        next_page = self.get_next_page(response, current_page)
        payload["page"] = next_page  
        full_api_url = f"{api_url}?page={payload['page']}"
        yield scrapy.Request(
            url=full_api_url,
            method="GET",
            headers=self.headers,
            callback=self.parse_intermediate,
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "payload": payload,
                "current_page": next_page
            },
        )
