import json
from scraper.OCSpider import OCSpider
from scraper.middlewares import HeadlessBrowserProxy
import scrapy
from typing import List
from dotenv import load_dotenv
from scraper.utils.helper import body_normalization
from typing import Optional
import logging
load_dotenv()

class ChinaContainerIndustryAssociation(OCSpider):
    name = "ChinaContainerIndustryAssociation"

    charset = "utf-8"

    start_urls_names = {
        "https://www.chinaccia.com/industrynotice.html" : "行业资讯",
        "https://www.chinaccia.com/industrynotice/109.html" : "行业资讯",
        "https://www.chinaccia.com/industrynotice/9.html" : "行业资讯",
        "https://www.chinaccia.com/industrynotice/8.html" : "行业资讯"
    }

    api_start_urls = {
        "https://www.chinaccia.com/industrynotice.html": {
            "url": "https://www.chinaccia.com/api/article/index?catid=80&size=10&page=1",
            "payload": {
                "catid": "80",
                "size": "10",
                "page": "1",
            },
        },
        "https://www.chinaccia.com/industrynotice/109.html": {
            "url": "https://www.chinaccia.com/api/article/index?catid=109&size=10&page=1",
           "payload": {
                "catid": "109",
                "size": "10",
                "page": "1",
            },
        },
        "https://www.chinaccia.com/industrynotice/9.html": {
            "url": "https://www.chinaccia.com/api/article/index?catid=9&size=10&page=1",
            "payload": {
                "catid": "9",
                "size": "10",
                "page": "1",
            },
        },
        "https://www.chinaccia.com/industrynotice/8.html": {
            "url":"https://www.chinaccia.com/api/article/index?catid=8&size=10&page=1",
            "payload": {
                "catid": "8",
                "size": "10",
                "page": "1",
            },
        },
    }

    custom_settings = {
        "DOWNLOAD_DELAY": 3,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_data=self.api_start_urls.get(start_url)
        payload = api_data["payload"]
        catid=payload.get("catid")
        page=payload.get("page")
        url=f"https://www.chinaccia.com/api/article/index?catid={catid}&size=10&page={page}"
        yield scrapy.Request(
            url = url,
            method = "GET",
            body = json.dumps(payload),
            callback = self.parse,   
            meta={
                "start_url": start_url,
                "api_url": api_data["url"],
                "payload": payload,
                "catid": catid,
                "currentpage":page
            },
        )
        
    @property
    def source_type(self) -> str:
        return 'IndustryAssociation'

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_articles(self, response):
        data = json.loads(response.text)
        article_urls=[]
        hbp=HeadlessBrowserProxy()
        for item in data.get('data',{}):
            link=item.get('link')
            url=hbp.get_proxy(f"https://www.chinaccia.com/{link}",timeout=30000)
            article_urls.append(url)
        return article_urls
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//div[@class='news-detail']//h1/text()").get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='list-r-n']//p//text()").getall())
    
    def get_images(self, response, entry=None) -> List[str]:
        return response.xpath("//div[@class='list-r-n']//p//img/@src").getall()
    
    def get_authors(self, response, entry=None) -> list[str]:
        return []
    
    def get_document_urls(self, response, entry=None) -> list:
        return response.xpath("//div[@class='list-r-n']//p/a/@href").getall() 
    
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self,response):
        return response.xpath("//div[@class='news-detail']//p/span[1]/text()").re(r'(\d{4}-\d{2}-\d{2})')[0]
        
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response,current_page ):
        max_pages = response.json().get('totalPage', 1)
        current_page = int(response.meta.get('currentpage'))+1
        return str(current_page) if current_page<max_pages else None
    
    def go_to_next_page(self, response, start_url, current_page: Optional[str] = "1"):
        api_url = response.meta.get("api_url")
        if not api_url:
            logging.info("API URL not found in meta data.")
            return None
        next_page = self.get_next_page(response, current_page)
        if next_page:
            payload = response.meta.get('payload')
            payload["page"]=next_page
            request = response.request.replace(
                url=api_url,
                callback=self.parse_intermediate   
            )
            request.meta.update({
                'start_url': start_url,
                'current_page': str(int(current_page) + 1),
                'api_url': response.meta.get('api_url'),
            })
            yield request
        else:
            self.logger.info(f"No more pages to crawl for start_url: {start_url}")
       