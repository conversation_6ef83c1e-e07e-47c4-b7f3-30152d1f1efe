from typing import List
from scraper.OCSpider import <PERSON><PERSON>pider
from scraper.utils.helper import body_normalization
import re
from datetime import datetime
import json
from urllib.parse import urljoin

class AVICIndustryFinanceFocused(OCSpider):
    name = "avicindustryfinancefocused"
    
    country = "CN"
    
    # Start URL for the news page
    start_urls_names = {
        "https://www.avicindustry-finance.com/sycd/xwzx/zhcrxw/": "AVIC Industry Finance News",
    }
    
    custom_settings = {
        "DOWNLOAD_DELAY": 2,
        # Define the fields we want to output in the CSV
        "FEED_EXPORT_FIELDS": ["title", "body", "images"],
    }
    
    charset = "utf-8"
    
    @property
    def source_type(self) -> str:
        return "Corporate"
    
    @property
    def language(self) -> str:
        return "Chinese"
    
    @property
    def timezone(self) -> str:
        return "Asia/Shanghai"
    
    def get_page_flag(self) -> bool:
        # We'll handle pagination
        return True
    
    def get_articles(self, response) -> list:
        # Extract article URLs from the page using multiple selectors to be robust
        articles = response.xpath(
            '//div[@class="right-content"]/ul/li/a/@href | '
            '//ul[@class="news-list"]/li/a/@href | '
            '//a[contains(@href, "/c/")]/@href'
        ).getall()
        
        # Make sure all URLs are absolute
        return [response.urljoin(link) for link in articles]
    
    def get_href(self, entry) -> str:
        # Return the article URL
        return entry
    
    def get_title(self, response, entry=None) -> str:
        # Extract the article title using multiple selectors
        title = response.xpath(
            '//div[@class="article-title"]/text() | '
            '//h1/text() | '
            '//title/text()'
        ).get()
        
        return title.strip() if title else ""
    
    def get_body(self, response, entry=None) -> str:
        # Extract the article body using multiple selectors
        body_parts = response.xpath(
            '//div[@class="article-content"]//text() | '
            '//div[@class="content"]//text()'
        ).getall()
        
        # Clean and normalize the body text
        return body_normalization(body_parts)
    
    def get_images(self, response, entry=None) -> List[str]:
        # Extract image URLs from the article
        img_urls = response.xpath(
            '//div[@class="article-content"]//img/@src | '
            '//div[@class="content"]//img/@src'
        ).getall()
        
        # Make sure all URLs are absolute
        return [urljoin(response.url, img) for img in img_urls]
    
    def get_next_page(self, response) -> List[str]:
        # Extract the next page URL for pagination
        # First try to find the current page number
        m = re.search(r'/page/(\d+)/', response.url)
        current = int(m.group(1)) if m else 1
        
        # Try to find the total number of pages
        info = response.xpath('//div[@class="page-info"]/text()').get()
        if info:
            m = re.search(r'共(\d+)页', info)
            if m and current < int(m.group(1)):
                base = re.sub(r'/page/\d+/?', '/', response.url)
                return [f"{base.rstrip('/')}/page/{current+1}/"]
        
        # Alternative pagination detection
        next_link = response.xpath('//a[contains(@class, "next")]/@href').get()
        if next_link:
            return [response.urljoin(next_link)]
            
        return []
    
    def parse(self, response):
        # Get all article URLs from the page
        article_urls = self.get_articles(response)
        
        # Follow each article URL
        for url in article_urls:
            yield response.follow(url, callback=self.parse_article)
            
        # Check for next page
        next_pages = self.get_next_page(response)
        for next_page in next_pages:
            yield response.follow(next_page, callback=self.parse)
    
    def parse_article(self, response):
        # Create a dictionary to store the article data
        article = {}
        
        # Get title (if available)
        title = self.get_title(response)
        if title:
            article['title'] = title
        else:
            article['title'] = ""
        
        # Get body (if available)
        body = self.get_body(response)
        if body:
            article['body'] = body
        else:
            article['body'] = ""
        
        # Get images (if available) and convert to JSON string
        images = self.get_images(response)
        article['images'] = json.dumps(images) if images else "[]"
        
        # Increment the articles_crawled counter
        self.crawler.stats.inc_value('articles_crawled')
        self.crawler.stats.inc_value('articles_successfully_scraped')
        
        # Return the article data
        return article
