from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class SANAN(OCSpider):
    name = "SANAN"

    start_urls_names = {
        'https://www.sanan-e.com/news': '三安光电',
    }

    @property
    def source_type(self) -> str:
        return 'private_enterprise'

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_articles(self, response) -> list:
        return response.xpath("//ul[@class='info_cont_list']//a//@href").getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//h1//text()").get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='detail_cont_top']//p//text()").getall())

    def get_images(self, response) -> list:
        return [response.urljoin(url) for url in response.xpath('//div[@class="detail_cont_top"]//p//img//@src').getall()]
    
    def get_document_urls(self, response, entry=None):
        return [response.urljoin(url) for url in response.xpath('//*[@class="news-detail-cont"]//a/@href').getall()]

    def get_authors(self, response):
        return []

    def date_format(self) -> str:
        return '%Y-%m-%d'

    def get_date(self, response) -> str:
        return response.xpath("//div[@class='detail_top_date']//text()").get()

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        next_page = response.xpath("//div[@class='info_paging']//a[@class='next']//@href").get()
        if next_page:
            return next_page
        else:
            return None