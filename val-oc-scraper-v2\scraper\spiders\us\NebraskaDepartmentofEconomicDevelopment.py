from scraper.utils.helper import body_normalization
from scraper.OCSpider import <PERSON>CSpider
from datetime import datetime

class NebraskaDepartmentOfEconomicDevelopment(OCSpider):
    name = 'NebraskaDepartmentOfEconomicDevelopment'

    country = "US"

    start_urls_names = {
        "https://opportunity.nebraska.gov/news/":"News",
    }

    charset = "utf-8"
    
    @property
    def language(self):
        return "English"

    @property
    def source_type(self) -> str:
        return 'ministry'
    
    @property
    def timezone(self):
        return "US/Central"
    
    def get_articles(self, response) -> list:
        return response.xpath("//h2[@class='entry-title']/a/@href").getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//h1[@class='entry-title']/text()").get()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//p/text()").getall())
  
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%A, %B %d, %Y"
    
    def get_date(self, response) -> str:
        date_str = response.xpath("//p[@class='post-meta']/span[@class='published']/text()").get()
        if date_str:
            date_obj = datetime.strptime(date_str, "%b %d, %Y")
            return date_obj.strftime("%A, %B %d, %Y")
        else:
            return None
    
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        return response.xpath("//div[@class='pagination clearfix']/div[@class='alignleft']/a/@href").get()