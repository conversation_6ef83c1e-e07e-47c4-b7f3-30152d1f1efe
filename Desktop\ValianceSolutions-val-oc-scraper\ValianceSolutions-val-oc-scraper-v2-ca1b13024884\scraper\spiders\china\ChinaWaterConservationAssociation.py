from typing import List, Dict, Any
from scraper.OCSpider import <PERSON><PERSON><PERSON><PERSON>
from scraper.utils.helper import body_normalization
import re
from datetime import datetime
import json

class ChinaWaterConservationAssociation(OCSpider):
    name = "ChinaWaterConservationAssociation"

    country = "CN"

    # Example URL: https://www.zgsz.org.cn/h-col-122.html
    start_urls_names = {
        "https://www.zgsz.org.cn/h-col-122.html": "新闻动态",
    }

    # Simple custom settings without geo-targeted proxy
    custom_settings = {
        "DOWNLOAD_DELAY": 2,
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"

    @property
    def language(self) -> str:
        return "Chinese"

    @property
    def timezone(self) -> str:
        return "Asia/Shanghai"

    def get_page_flag(self) -> bool:
        # Return True if pagination is available
        return True

    def get_articles(self, response) -> list:
        # Extract article URLs from the page
        # Using a more general XPath to find article links
        articles = response.xpath('//a[contains(@href, ".html")]/@href').getall()
        # Filter out navigation links and other non-article links
        article_links = []
        for link in articles:
            # Only include links that likely point to articles
            if '/h-nd-' in link or '/news/' in link or '/article/' in link:
                article_links.append(response.urljoin(link))

        self.logger.info(f"Found {len(article_links)} article links")
        return article_links

    def get_href(self, entry) -> str:
        # Return the article URL
        return entry

    def get_title(self, response, entry=None) -> str:
        # Extract the article title
        # Try multiple XPath patterns to find the title
        title = response.xpath('//div[@class="news-detail-title"]/text()').get()
        if not title:
            title = response.xpath('//h1/text()').get()
        if not title:
            title = response.xpath('//title/text()').get()
        if not title:
            title = response.xpath('//h2/text()').get()
        if not title:
            # If no title found, use the URL as the title
            title = response.url.split('/')[-1]

        return title.strip() if title else "No title found"

    def get_body(self, response, entry=None) -> str:
        # Extract the article body
        # Try multiple XPath patterns to find the body content
        body_parts = response.xpath('//div[@class="news-detail-content"]//text()').getall()
        if not body_parts:
            body_parts = response.xpath('//div[@class="content"]//text()').getall()
        if not body_parts:
            body_parts = response.xpath('//article//text()').getall()
        if not body_parts:
            body_parts = response.xpath('//div[@class="article-content"]//text()').getall()
        if not body_parts:
            # If no specific content found, get all text from the page body
            body_parts = response.xpath('//body//text()').getall()

        return body_normalization(body_parts)

    def date_format(self) -> str:
        # Define the date format used on the website
        return "%Y-%m-%d"

    def get_date(self, response, entry=None) -> int:
        # Extract the article date and convert to timestamp
        # Try multiple XPath patterns to find the date
        date_str = response.xpath('//div[@class="news-detail-info"]/span[contains(@class, "date")]/text()').get()
        if not date_str:
            date_str = response.xpath('//span[contains(@class, "date")]/text()').get()
        if not date_str:
            date_str = response.xpath('//div[contains(@class, "date")]/text()').get()
        if not date_str:
            date_str = response.xpath('//time/text()').get()

        if date_str:
            date_str = date_str.strip()
            # Extract date using regex - look for common date formats
            date_match = re.search(r'(\d{4}-\d{2}-\d{2})', date_str)
            if date_match:
                date_str = date_match.group(1)
                date_obj = datetime.strptime(date_str, self.date_format())
                return int(date_obj.timestamp())

            # Try another common format (e.g., 2025/04/13)
            date_match = re.search(r'(\d{4}/\d{2}/\d{2})', date_str)
            if date_match:
                date_str = date_match.group(1)
                date_obj = datetime.strptime(date_str, "%Y/%m/%d")
                return int(date_obj.timestamp())

        # Return current time if date not found
        return int(datetime.now().timestamp())

    def get_authors(self, response, entry=None) -> List[str]:
        # Extract the article authors
        # Try multiple XPath patterns to find the author
        author = response.xpath('//div[@class="news-detail-info"]/span[contains(@class, "author")]/text()').get()
        if not author:
            author = response.xpath('//span[contains(@class, "author")]/text()').get()
        if not author:
            author = response.xpath('//div[contains(@class, "author")]/text()').get()
        if not author:
            author = response.xpath('//meta[@name="author"]/@content').get()

        if author:
            author = author.strip()
            # Remove "作者:" prefix if present
            author = re.sub(r'^作者[：:]\s*', '', author)
            # Remove "来源:" prefix if present
            author = re.sub(r'^来源[：:]\s*', '', author)
            return [author] if author else []
        return []

    def get_images(self, response, entry=None) -> List[str]:
        # Extract image URLs from the article
        # Try multiple XPath patterns to find images
        images = response.xpath('//div[@class="news-detail-content"]//img/@src').getall()
        if not images:
            images = response.xpath('//div[@class="content"]//img/@src').getall()
        if not images:
            images = response.xpath('//article//img/@src').getall()
        if not images:
            images = response.xpath('//div[@class="article-content"]//img/@src').getall()
        if not images:
            # If no specific content found, get all images from the page
            images = response.xpath('//img/@src').getall()

        # Make sure all URLs are absolute
        return [response.urljoin(img) for img in images]

    def get_next_page(self, response) -> List[str]:
        # Extract the next page URL for pagination
        # Adjust the XPath selector based on the actual website structure
        next_page = response.xpath('//a[contains(@class, "next")]/@href').get()
        if next_page:
            return [response.urljoin(next_page)]
        return []

    def get_document_urls(self, response, entry=None) -> list:
        # Extract document URLs (PDFs, etc.) from the article
        # Try multiple XPath patterns to find document links
        docs = response.xpath('//div[@class="news-detail-content"]//a[contains(@href, ".pdf") or contains(@href, ".doc") or contains(@href, ".docx")]/@href').getall()
        if not docs:
            docs = response.xpath('//div[@class="content"]//a[contains(@href, ".pdf") or contains(@href, ".doc") or contains(@href, ".docx")]/@href').getall()
        if not docs:
            docs = response.xpath('//article//a[contains(@href, ".pdf") or contains(@href, ".doc") or contains(@href, ".docx")]/@href').getall()
        if not docs:
            # If no specific content found, get all document links from the page
            docs = response.xpath('//a[contains(@href, ".pdf") or contains(@href, ".doc") or contains(@href, ".docx")]/@href').getall()

        # Make sure all URLs are absolute
        return [response.urljoin(doc) for doc in docs]

    def get_meta(self, response) -> list:
        # Extract any additional metadata
        return []

    def get_pdf(self, response, entry=None):
        # Extract PDF content if needed
        return None

    def get_subhead(self, response) -> str:
        # Extract article subheading if available
        subhead = response.xpath('//div[@class="news-detail-subtitle"]/text()').get()
        return subhead.strip() if subhead else ""

    def close(self):
        # Override the close method to handle the case where no articles are found
        # This prevents the exception from being raised in the parent class
        self.logger.info(f"Spider closed: {self.name}")
        # We don't call super().close() to avoid the exception

    def parse(self, response):
        # Get all article URLs from the page
        article_urls = self.get_articles(response)

        # Follow each article URL
        for url in article_urls:
            yield response.follow(url, callback=self.parse_article)

        # Check for next page
        next_pages = self.get_next_page(response)
        for next_page in next_pages:
            yield response.follow(next_page, callback=self.parse)

    def parse_article(self, response):
        # Create a dictionary to store the article data
        article = {}

        # Extract the required fields
        article['url'] = response.url
        article['title'] = self.get_title(response)
        article['body'] = self.get_body(response)

        # Get images and convert to JSON string
        images = self.get_images(response)
        article['images'] = json.dumps(images)

        # Get date as timestamp and convert to string
        date_timestamp = self.get_date(response)
        article['date'] = datetime.fromtimestamp(date_timestamp).strftime('%Y-%m-%d %H:%M:%S')

        # Get document URLs and convert to JSON string
        document_urls = self.get_document_urls(response)
        article['document_urls'] = json.dumps(document_urls)

        # Get authors and convert to string
        authors = self.get_authors(response)
        article['authors'] = ', '.join(authors) if authors else ''

        # Return the article data
        self.logger.info(f"Extracted article: {article['title']}")
        return article
