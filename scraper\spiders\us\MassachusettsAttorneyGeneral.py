from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization

class MassachusettsAttorneyGeneral(OCSpider):
    name = 'MassachusettsAttorneyGeneral'

    country = "US"
    
    start_urls_names = {
        'https://www.mass.gov/press-releases/recent': "Press Release"
    }
    
    custom_settings = {
		"DOWNLOADER_MIDDLEWARES" : {
			'scraper.middlewares.HeadlessBrowserProxy': 350,
		},
		"DOWNLOAD_DELAY" : 4,
	}
    
    HEADLESS_BROWSER_WAIT_TIME = 10000  #10 Seconds wait time

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "US/Central"

    @property
    def language(self):
        return "English"

    def get_articles(self, response) -> list:
        return response.xpath("//section[@class='page-content']//h3//a//@href").getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//li[@class='ma__breadcrumbs__item--current ma__visually-hidden']//text()").get()
    
    def get_body(self, response) -> str:
        body = body_normalization(response.xpath("//div[@class='page-content ma__announcement__page-content ma__announcement__page-content--press_release']//p//text()").getall())
        return body
    
    def get_images(self, response) -> list:
        return response.xpath("//div[@class='ma__figure ma__figure-image']//img//@src").getall()
    
    def date_format(self) -> str:
        return '%m/%d/%Y'

    def get_date(self, response) -> str:
        date = response.xpath("//div[@class='ma__press-status__date']/text()").get()
        return date.strip()
    
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        next_page = response.xpath("//div[@class='ma__pagination__container']//a[contains(text(),'Next')]/@href").get()
        if next_page:
            return next_page
        else:
            return None