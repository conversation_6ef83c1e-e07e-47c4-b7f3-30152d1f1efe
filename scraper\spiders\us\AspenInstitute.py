from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class AspenInstitute(OCSpider):
    name = "AspenInstitute"
    
    country = "US"

    start_urls_names = {
        "https://www.aspeninstitute.org/publications/" : "Publications"
    }
    
    charset = "utf-8"

    @property
    def language(self): 
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "US/Eastern"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="card__inner-wrapper"]/a/@href').getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h3[@class="post__header__title"]//text()').get().strip()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="post__article__content"]//p//text()').getall()) 
        
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%B %d, %Y"
    
    def get_date(self, response) -> str:
        date = response.xpath('//span[@class="post__header__meta__date"]//text()').get().strip()
        date = date.split('•')[0].strip()
        return date
        
    def get_authors(self, response):
        return response.xpath('//span[@class="post__header__meta__author"]//text()').getall()
    
    def get_page_flag(self) -> bool:
        return False
     
    def get_next_page(self, response): 
        return response.xpath('//li[@data-label="Load More"]//a/@href').get()