from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization

class RhodeIslandCommerceCorporation(OCSpider):
    name = "RhodeIslandCommerceCorporation"

    country = "US"
    
    charset="utf-8"

    start_urls_names = {
        "https://commerceri.com/press/" : "Press"
    }

    HEADLESS_BROWSER_WAIT_TIME = 30000 

    custom_settings = { 
		"DOWNLOADER_MIDDLEWARES" : {
			'scraper.middlewares.HeadlessBrowserProxy': 350,
		}
	}

    @property
    def language(self) -> str:
        return "English"
    
    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self) -> str:
        return "US/Eastern"
    
    def get_articles(self, response) :
        return response.xpath('//h2[@class="entry-title"]//a/@href').getall()
    
    def get_href(self, entry: str) -> str:
        return entry

    def get_title(self, response) :
        return response.xpath('//h1[@class="entry-title"]//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="entry-content"]//p//text()').getall())

    def get_images(self, response) :
        return []

    def date_format(self) -> str:
        return "%B %d, %Y"

    def get_date(self, response) -> str:
        return response.xpath('//span[@class="published"]/text()').get()
    
    def get_authors(self, response) :
        return []
    
    def get_document_urls(self, response, entry=None):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        #All article_urls present in 1st page only
        return None

    
    
    