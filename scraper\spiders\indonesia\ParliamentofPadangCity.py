from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ParliamentofPadangCity(OCSpider):
    name = "ParliamentofPadangCity"
    
    country = "ID"

    start_urls_names = {
        "Artikel": "https://dprd.padang.go.id/category/artikel"
    }
    
    charset = "utf-8"

    @property
    def language(self): 
        return "Indonesian"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    def get_articles(self, response) -> list:
        return response.xpath("//div[@class='margin-min10']/a/@href").getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1//text()').get().strip()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("").getall()) 
        
    def get_images(self, response) -> list:
        return response.xpath('//div[@class="image-konten-default"]//img//@src').getall()
    
    def date_format(self) -> str:
        return "%d %B %Y"
    
    def get_date(self, response) -> str:
        return response.xpath("//div[@class='content-date']//p//text()").get().strip()
    
    def get_authors(self, response):
        return response.xpath("").getall()
    
    def get_page_flag(self) -> bool:
        return False
     
    def get_next_page(self, response): 
        return response.xpath("").get()
    
# from scraper.OCSpider import OCSpider
# from scraper.utils.helper import body_normalization

# class ParliamentofPadangCity(OCSpider):
#     name = "ParliamentofPadangCity"
    
#     country = "ID"

#     start_urls_names = {
#         "Artikel": "https://dprd.padang.go.id/category/artikel"
#     }
    
#     charset = "utf-8"

#     @property
#     def language(self): 
#         return "Indonesian"

#     @property
#     def source_type(self) -> str:
#         return "ministry"
    
#     @property
#     def timezone(self):
#         return "Asia/Jakarta"
    
#     def get_articles(self, response) -> list:
#         urls = response.xpath("//div[contains(@class, 'margin-min10')]//a/@href").getall()
#         print(f"Found article URLs: {urls}")  # Debug print to check URLs
#         return urls

#     def get_href(self, entry) -> str:
#         return entry
    
#     def get_title(self, response) -> str:
#         return response.xpath('//h1//text()').get().strip()
        
#     def get_body(self, response) -> str:
#         # No body present, so returning empty string or could keep it empty
#         return ""
        
#     def get_images(self, response) -> list:
#         return response.xpath('//div[@class="image-konten-default"]//img//@src').getall()
    
#     def date_format(self) -> str:
#         return "%d %B %Y"
    
#     def get_date(self, response) -> str:
#         return response.xpath("//div[@class='content-date']//p//text()").get().strip()
    
#     def get_authors(self, response):
#         # No author info present, return empty list
#         return []
    
#     def get_page_flag(self) -> bool:
#         return False
     
#     def get_next_page(self, response): 
#         # No pagination, so no next page URL
#         return None
