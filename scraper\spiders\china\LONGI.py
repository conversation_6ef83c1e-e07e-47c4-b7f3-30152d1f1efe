import scrapy
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from typing import List
from datetime import datetime

class LONGI(OCSpider):
    name = "<PERSON><PERSON><PERSON>"

    start_urls_names = {
        "https://www.longi.com/cn/news/" : "隆基股份", 
        "https://www.longi.com/cn/bulletin/" : "隆基股份",
        "https://www.longi.com/cn/investor/" : "隆基股份",
    }

    api_start_urls = {
        'https://www.longi.com/cn/news/': {
            "url": "https://website-console.longi.com/news?_sort=publishDate:DESC&_where[0][recommendNews]=default&_locale=zh-CN",
            "payload": {
                "_start": "0",
                "_limit": "9"
            },
        },    
        'https://www.longi.com/cn/bulletin/' : {
            "url": "https://website-console.longi.com/notices?_sort=publishDate:DESC,+id:DESC&_locale=zh-CN",
            "payload": {
                "_start": "0",
                "_limit": "8"
            },
        },  
        'https://www.longi.com/cn/investor/' : {
            "url": "https://website-console.longi.com/investor-reports?_sort=date:DESC,+id:DESC&type=InterimAnnouncement&_locale=zh-CN",
            "payload": {
                "_start": "0",
                "_limit": "10"
            },
        }
    }

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            self.logger.error(f"No API configuration found for start_url: {start_url}")
            return
        api_url = api_data["url"]
        payload = api_data["payload"]
        current_page = payload["_start"]
        yield scrapy.Request(
            url=api_url,
            method="GET",
            headers={"Content-Type": "application/json; charset=utf-8"},
            # formdata=payload,
            callback=self.parse,
            dont_filter=True,
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "payload": payload,
                "current_page": current_page
            },
        )

    charset = "iso-8859-1"

    article_data_map ={}

    @property
    def source_type(self) -> str:
        return 'IndustryAssociation'

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_articles(self, response) -> list:
        articles = []
        for article in response.xpath("//div[@class='news-box']//div[@class='news-list'] | //div[@class='announcement-items'] | //div[@class='list min-container']"):
            url = article.xpath(".//a//@href").get()
            title = article.xpath(".//div[@class='announce-title']//text() | .//h2//text() | .//p[@class='title']//text()").get()
            date = article.xpath(".//span[@class='date']//text() | .//div[@class='announce-time']//text()").get()
            print("aaa",url,title,date)
            if url and title and date:
                full_url = url
                title = title.strip()
                if '.pdf' in full_url.lower():
                    pdf = full_url
                else:
                    pdf = "None"
                clean_date = date.strip()
                self.article_data_map[full_url] = {"title": title, "date": clean_date, "pdf": pdf}
                articles.append(full_url) 
        print("hey",articles)
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response, entry=None) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")

    def get_body(self, response, entry=None) -> str:
        if ".pdf" in response.url.lower():
            return ""
        return body_normalization(response.xpath("//article[@class='main-box article-content center']//p//text()").getall())
        
    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response, entry=None) -> int:
        date = self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        date = date.replace(".", "-")
        try:
            parsed_date = datetime.strptime(date, "%Y-%m-%d")
            return parsed_date.strftime("%Y-%m-%d")
        except ValueError:
            return "" 

    def get_authors(self, response, entry=None) -> List[str]:
        return []
    
    def get_images(self, response) -> list:
        if ".pdf" in response.url.lower():
            return ""
        return response.xpath("//img//@src").getall()
    
    def get_document_urls(self, response, entry=None) -> list:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("pdf", "")
    
    def get_page_flag(self) -> bool:
        return False 

    def get_next_page(self, response):
        current_page = int(response.meta.get("current_page"))
        total_articles = response.json().get('data', {}).get('count', 1)
        page_size = 10
        max_pages = int((total_articles / page_size) + 1)
        if current_page < max_pages:
            current_page = current_page + 1
            return str(current_page)
        else:
            return None

    def go_to_next_page(self, response, start_url=None, current_page=None):
        api_url = response.meta.get("api_url")
        payload = response.meta.get("payload")
        next_page = self.get_next_page(response)
        payload["start"] = next_page
        if next_page:
            yield scrapy.Request(
                url=api_url,
                method='POST',
                # formdata=payload,
                headers={"Content-Type": "application/json; charset=utf-8"},
                callback=self.parse_intermediate,
                meta={
                    "start_url": start_url,
                    "api_url": api_url,
                    "payload": payload,
                    "current_page": next_page
                }
            )
        else:
            self.logger.info(f"No more pages to crawl for start_url: {start_url}") 