from typing import List
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
import datetime
import scrapy

class NewHampshireEmploymentSecurity(OCSpider):
    name = 'NewHampshireEmploymentSecurity'
    
    country = "US"

    start_urls_names = {
        'https://www.nhes.nh.gov/media/press/2024/index.htm': 'News'
    }
    
    
    custom_settings = {
		"DOWNLOADER_MIDDLEWARES" : {
			'scraper.middlewares.HeadlessBrowserProxy': 350,
		},
		"DOWNLOAD_DELAY" : 5,
	}
    
    HEADLESS_BROWSER_WAIT_TIME = 30000 

    charset = "iso-8859-1"

    article_data_map={}

    def parse_intermediate(self, response):
        current_year = datetime.datetime.now().year
        for year in range(current_year, 2008, -1):
            news_url = f"https://www.nhes.nh.gov/media/press/{year}/index.htm"
            yield scrapy.Request(
                url=news_url,
                callback=self.parse,
                meta={'start_url': response.url},
                dont_filter=True
            )

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Central"
    
    def get_articles(self, response) -> list:
        self.extract_articles_with_dates(response)
        return [response.urljoin(link) for link in response.xpath("//*[@id='contentarea']/div/table/tbody/tr/td/a/@href").getall()]
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self,response) -> str:
        return self.article_data_map[response.request.meta.get('entry')].get("title")      
        
    def get_body(self, response) -> str:
        body=body_normalization(response.xpath("//div[@class='press-subtitle']//p//text() | //div[@class='field-formatter--text-default field-text-format--wysiwyg text-formatted field_body']//p//text()").getall())
        if not body:
            return ""    
        return body
    
    def get_images(self, response) -> list[str]:
        return []

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response) -> str:
        raw_date = self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "").strip()
        for date_format in ("%m/%d/%Y", "%m/%d/%y"):
            try:
                return datetime.datetime.strptime(raw_date, date_format).strftime("%Y-%m-%d")
            except ValueError:
                continue  
        return raw_date


    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None) -> List[str]:
        return self.article_data_map[response.request.meta.get('entry')].get("pdf")
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        # No next page is there to scrap
        return None
    
    def extract_articles_with_dates(self, response):
        # Function to extract dates of respective articles from start URL
        mapping = {}
        for article in response.xpath("//*[@id='contentarea']/div/table/tbody/tr"):
            url = article.xpath("./td/a/@href").get()
            title = article.xpath("./td/a/text()").get()
            date = article.xpath("./td[1]//text()").get()
            if url and title and date:
                full_url = response.urljoin(url.strip())
                clean_date=date.strip()
                mapping[full_url] = {"title": title.strip(), "date": clean_date, "pdf": [full_url]}
            self.article_data_map.update(mapping)
        return self.article_data_map