from typing import List
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class CHNews(OCSpider):
    name = "CHNews"

    start_urls_names = {
        "https://news.ch.com/": "春秋航空",
    }

    charset = "utf-8"

    current_page =2

    @property
    def source_type(self) -> str:
        return "private_enterprise"
    
    @property
    def timezone(self) -> str:
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        return response.xpath("//div[@class='list-content']//h1//a//@href").getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response, entry=None) -> str:
        return response.xpath('//h1//text()').get().strip()
    
    def get_body(self, response, entry=None) -> str:
        return body_normalization(response.xpath("//div[@class='subject-content']//p//text()").getall())
    
    def date_format(self) -> str:
        return "%Y年%m月%d日"
    
    def get_date(self, response, entry=None) -> int:
        return response.xpath("//span[@class='date']//text()").get()
    
    def get_authors(self, response, entry=None) -> List[str]:
        return []
    
    def get_document_urls(self, response, entry=None) -> list:
        return []
    
    def get_images(self, response, entry=None) -> List[str]:
        return response.xpath("//div[@class='subject-content']//img//@src").getall()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> List[str]:
        if self.current_page :
            next_page = f"https://news.ch.com/?pageIndex={self.current_page}"
            self.current_page += 1
            return next_page
        return None    