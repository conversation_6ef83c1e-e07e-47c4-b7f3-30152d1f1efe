from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
      
class OklahomaDepartmentOfCommerce(OCSpider):
    name = "OklahomaDepartmentOfCommerce"

    country = "US"
    
    charset="utf-8"

    start_urls_names = {
        "https://www.okcommerce.gov/news/" : "Press Release"
    }

    @property
    def language(self) -> str:
        return "English"
    
    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self) -> str:
        return "US/Eastern"
    
    def get_articles(self, response) :
        return response.xpath('//h2[@class="elementor-post__title"]//a/@href').getall()
    
    def get_href(self, entry: str) -> str:
        return entry

    def get_title(self, response) :
        return response.xpath('//div[@class="elementor-widget-container"]/h1/text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="elementor-widget-container"]//p/text()').getall())

    def get_images(self, response) :
        return response.xpath('//div[@class="elementor-widget-container"]//img/@src').getall()

    def date_format(self) -> str:
        return "%B %d, %Y"

    def get_date(self, response) -> str:
        return response.xpath('//time/text()').get().strip()
    
    def get_authors(self, response) :
        return []
    
    def get_document_urls(self, response, entry=None):
        return []
    
    def get_page_flag(self) -> bool:
        return True
    
    def get_next_page(self, response) :
        return response.xpath('//a[@class="page-numbers next"]/@href').get()

    
    
    