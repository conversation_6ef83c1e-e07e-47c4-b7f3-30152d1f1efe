from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ChinaCultureEntertainmentIndustryAssociation(OCSpider):
    name = "ChinaCultureEntertainmentIndustryAssociation"

    start_urls_names = {
        "http://www.cnccea.com/index.php?m=news&id=408": "协会新闻",
        "http://www.cnccea.com/index.php?m=news&id=409": "行业动态",
        "http://www.cnccea.com/index.php?m=news&id=411": "通知公告"
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return 'IndustryAssociation'
    
    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="zxzx-07"]//a/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="main-17"]//strong//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="main-18 nr-z"]//text()').getall())
    
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        return response.xpath('//div[@class="main-16"]//div//text()').re_first(r"\d{4}-\d{2}-\d{2}")
    
    def get_images(self, response) -> list[str]:
        relative_images = response.xpath('//div[@class="main-18 nr-z"]//img/@src').getall()
        images = [response.urljoin(img) for img in relative_images]
        return images
    
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        try:
            next_page = response.xpath('//div[@class="fy2"]//a[contains(text(), "下一页")]/@href').get()
            if next_page:
                next_page_url = response.urljoin(next_page)
                if next_page_url == response.url:
                    return None
                return next_page_url
            return None
        except Exception as e:
            self.logger.error(f"Error extracting next page link from page {response.url}: {e}")
            return None