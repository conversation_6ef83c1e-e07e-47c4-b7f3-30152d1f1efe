from typing import List
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import re
from datetime import datetime
from urllib.parse import urljoin

class AVICIndustryFinanceMinimal(OCSpider):
    name = "AVICIndustryFinanceMinimal"
    
    country = "CN"
    
    # Start URL for the news page
    start_urls_names = {
        "https://www.avicindustry-finance.com/sycd/xwzx/zhcrxw/": "AVIC Industry Finance News",
    }
    
    custom_settings = {
        "DOWNLOAD_DELAY": 2,
    }
    
    charset = "utf-8"
    
    @property
    def source_type(self) -> str:
        return "Corporate"
    
    @property
    def language(self) -> str:
        return "Chinese"
    
    @property
    def timezone(self) -> str:
        return "Asia/Shanghai"
    
    def get_page_flag(self) -> bool:
        return True
    
    def get_articles(self, response) -> list:
        # Extract article URLs from the page
        articles = response.xpath('//a[contains(@href, "/c/")]/@href').getall()
        return [response.urljoin(link) for link in articles]
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response, entry=None) -> str:
        title = response.xpath('//title/text()').get()
        return title.strip() if title else ""
    
    def get_body(self, response, entry=None) -> str:
        body_parts = response.xpath('//div[@class="article-content"]//text()').getall()
        return body_normalization(body_parts)
    
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response, entry=None) -> int:
        # Try to extract date from URL
        url = response.url
        date_match = re.search(r'/c/(\d{4})-(\d{2})-(\d{2})/', url)
        if date_match:
            year, month, day = date_match.groups()
            date_obj = datetime(int(year), int(month), int(day))
            return int(date_obj.timestamp())
        
        # Return current time if date not found
        return int(datetime.now().timestamp())
    
    def get_authors(self, response, entry=None) -> List[str]:
        return []
    
    def get_images(self, response, entry=None) -> List[str]:
        img_urls = response.xpath('//img/@src').getall()
        return [urljoin(response.url, img) for img in img_urls]
    
    def get_next_page(self, response) -> List[str]:
        # For simplicity, we're not implementing pagination
        return []
    
    def get_document_urls(self, response, entry=None) -> list:
        docs = response.xpath('//a[contains(@href, ".pdf")]/@href').getall()
        return [urljoin(response.url, doc) for doc in docs]
    
    def get_meta(self, response, entry=None) -> list:
        # Required by OCSpider but not used in our implementation
        return []
    
    def get_pdf(self, response, entry=None):
        # Required by OCSpider but not used in our implementation
        return None