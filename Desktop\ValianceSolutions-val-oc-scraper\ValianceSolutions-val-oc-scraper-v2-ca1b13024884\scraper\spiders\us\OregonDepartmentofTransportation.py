from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
import re 

class OregonDepartmentOfTransportation(OCSpider):
    name = 'OregonDepartmentOfTransportation'

    country = "US"

    HEADLESS_BROWSER_WAIT_TIME = 30000  # 30 Seconds wait time
    
    custom_settings = {
		"DOWNLOADER_MIDDLEWARES" : {
			'scraper.middlewares.HeadlessBrowserProxy': 350,
		},
		"DOWNLOAD_DELAY" : 5,
	}
    
    start_urls_names = {
        'https://www.oregon.gov/odot/Pages/News-Releases.aspx': "News",
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Central"

    def get_articles(self, response) -> list:
        articles = response.xpath("//div[@class='gdw_story_title']//a//@href").getall()
        return [response.urljoin(link) for link in articles]
    
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//h1[@class='bulletin_subject']//text()").get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//td[@id='main-body']//p//text() | //div[@data-block-id='137163643']//table[@class='abe-section-table']//text() | //div[@class='abe-column-block']//td[@class='abe-column-block-padding']//p//text()").getall())
    
    def get_images(self, response) -> list:
        return response.xpath("//div[@class='abe-column-block']//img//@src").getall()
    
    def date_format(self) -> str:
        return '%m/%d/%Y'

    def get_date(self, response) -> str:
        date = response.xpath("//span[@class='dateline rs_skip']//text()").get(default="").strip()
        pattern = r'(\d{2}/\d{2}/\d{4})'
        match = re.search(pattern, date)
        if match:
            return match.group(1)
        
    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        return None