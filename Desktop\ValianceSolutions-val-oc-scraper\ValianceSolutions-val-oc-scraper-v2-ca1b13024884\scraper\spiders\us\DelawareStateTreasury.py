from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from typing import List, Union
import re 

class DelawareStateTreasury(OCSpider):
    name = 'DelawareStateTreasury'

    country = "US"

    start_urls_names = {
        'https://news.delaware.gov/category/ost/':'Delaware News',
    }

    charset= "utf-8"

    @property
    def source_type(self) -> str:
        return 'ministry'
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Eastern"

    def get_articles(self, response) -> list:
        return response.xpath("//div[@class='container-fluid']//div[@id ='main_content']//h3//a//@href").getall()
        
    def get_href(self, entry) -> str:
        return entry

    def get_title(self,response) -> str:
        return response.xpath("//header//h1//text()").get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@id ='main_content']//p//text()").getall()) 
    
    def get_images(self, response) -> List[str]:
        return response.xpath("//div[@id ='main_content']//img//@src").get()
            
    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response) -> str:
        match = re.search(r"/(\d{4})/(\d{2})/(\d{2})/", response.url)
        if match:
            year, month, day = match.groups()
            return f"{year}-{month}-{day}"
        return None
    
    def get_authors(self, response):
        return []
    
    def get_next_page(self, response) -> Union[None, str]:
        next_page = response.xpath("//div[@class='normal-format']//div[@class='pagination']//a[span[contains(text(), 'Go to next page')]]/@href").get()        
        if next_page:
            return response.urljoin(next_page)
        else:
            return None
            
    def get_page_flag(self) -> bool:
        return False   