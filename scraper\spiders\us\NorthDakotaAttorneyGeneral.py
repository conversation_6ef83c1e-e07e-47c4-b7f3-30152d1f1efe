from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class NorthDakotaAttorneyGeneral(OCSpider):
    name = "NorthDakotaAttorneyGeneral"

    country = "US"

    start_urls_names = {
        "https://attorneygeneral.nd.gov/news" : "News",
    }

    charset = "utf-8"

    @property
    def language(self) :
        return "English"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "US/Central"
    
    article_to_date_mapping = {}
    
    def get_articles(self, response) -> list:
        article_urls =[]
        mapping = {}
        entries = response.xpath('//header[@class="wp-show-posts-entry-header"]')
        for items in entries: 
            url = items.xpath('.//h5/a/@href').get()
            article_urls.append(url)
            date = items.xpath('.//time[@itemprop="datePublished"]//text()').get()
            mapping[url] = date
        self.article_to_date_mapping.update(mapping)
        return article_urls
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h2[@class="entry_title"]/text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="post_text_inner"]//p/text()').getall())

    def get_images(self, response, entry=None) :
        return []
    
    def date_format(self) -> str:
        return "%B %d, %Y"
    
    def get_date(self, response) -> str:
        article_url = response.url
        article_date = self.article_to_date_mapping.get(article_url, None)
        if article_date:
            return article_date
        else:
            return None
        
    def get_authors(self, response, entry=None) -> list[str]:
        return []
    
    def get_document_urls(self, response, entry=None):
        return response.xpath('//p[contains(text(), "You can view this opinion at:")]//a/@href').getall()
    
    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        return response.xpath('//a[@class="next page-numbers"]/@href').get()