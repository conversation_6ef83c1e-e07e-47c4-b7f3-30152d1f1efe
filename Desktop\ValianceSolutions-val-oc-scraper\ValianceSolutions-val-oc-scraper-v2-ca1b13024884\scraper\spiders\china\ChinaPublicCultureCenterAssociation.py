from typing import Optional
from scraper.OCSpider import <PERSON><PERSON>pider
from scraper.utils.helper import body_normalization
from scrapy.http import Request
from dotenv import load_dotenv
load_dotenv()
import json
from scraper.middlewares import HeadlessBrowserProxy


class ChinaPublicCultureCenterAssociation(OCSpider):
    name = "ChinaPublicCultureCenterAssociation"

    custom_settings = {
        "DOWNLOAD_DELAY": 5,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    start_urls_names = {
        "https://www.cpcca.org.cn/news": "新闻中心"
    }

    # To match the encoded article url from the proxy with the domain "www.cpcca.org.cn/news/"
    include_rules = [r'.*https%3A%2F%2Fwww.cpcca.org.cn%2Fnews%2F.*']

    api_start_urls = [
        "https://website.cpcca.org.cn/web/all/list?type=31bc71d101d8432083b12453b43c0814",
        "https://website.cpcca.org.cn/web/all/list?type=3867333acb53496bab2d629c6e4888f1"
    ]

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        current_page = response.meta.get("current_page", 1)
        for api_url in self.api_start_urls:
            paginated_api_url = f"{api_url}&current={current_page}"
            self.logger.info(f"Fetching API URL: {paginated_api_url}")
            yield Request(
                url = paginated_api_url,
                callback = self.parse,   # callback to 'parse'
                meta={
                    "current_page": current_page,
                    "api_url": api_url,
                    "start_url": start_url,
                },
            )
    
    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_articles(self, response) -> list:
        try:
            data = json.loads(response.text)
            records = data.get("data", {}).get("records", [])
            articles = [
                record.get("url") or f"https://www.cpcca.org.cn/news/info?id={record.get('jid')}"
                for record in records if record.get("url") or record.get("jid")
            ]
            proxy_articles = self.get_proxy_articles(articles)
            return proxy_articles
        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to decode JSON from response: {e}")
            return []
    
    def get_href(self, entry) -> str:
        href = entry if isinstance(entry, str) else str(entry)
        return href

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_title(self, response) -> str:
        title = response.xpath('//div[@id="app"]//div[@class="title"]/text()').get()
        return title

    def get_body(self, response) -> str:
        body = body_normalization(response.xpath('//div[@class="left"]//div[4]//p//text()').getall())
        return body

    def get_images(self, response) -> list:
        images = response.xpath("//div[@class='left']//div[4]//img/@src").getall()
        return images

    def get_document_urls(self, response, entry=None) -> list:
        documents = response.xpath('//div[@class="left"]//div[4]//p//a/@href').getall()
        return [response.urljoin(doc) for doc in documents]

    def get_date(self, response) -> str:
        date = response.xpath('//div[@class="time"]//text()').re_first(r"\d{4}-\d{2}-\d{2}")
        return date

    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response, current_page: Optional[int] = 1) -> Optional[str]:
        api_url = response.meta.get('api_url')
        if not api_url:
            self.logger.error("API URL not found in response metadata.")
            return None        
        try:
            data = json.loads(response.text)
            max_pages = data.get("data", {}).get("pages")
            self.logger.info(f"Extracted max_pages: {max_pages} from API response.")
        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to decode JSON from response: {e}")
            return None
        next_page = current_page + 1   # increment the current page number
        if next_page > max_pages:
            return None
        paginated_url = f"{api_url}&current={next_page}"   #  API page URL including "current" parameter
        return paginated_url
    
    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        next_page_url = self.get_next_page(response, current_page)
        if next_page_url:
            request = response.request.replace(
                url=next_page_url,
                callback=self.parse_intermediate   # callback to parse_intermediate
            )
            # Update metadata for the next request
            request.meta.update({
                'start_url': start_url,
                'current_page': current_page + 1,
                'api_url': response.meta.get('api_url')
            })
            yield request
        else:
            self.logger.info(f"No more pages to crawl for start_url: {start_url}")
    
    def get_proxy_articles(self, articles):
        try:
            hbp = HeadlessBrowserProxy()
            proxy_urls = [hbp.get_proxy(url, timeout = 50000) for url in articles]    # Article URLs to include the proxy
            return proxy_urls
        except Exception as e:
            self.logger.error(f"Failed to fetch proxy articles: {e}")
            return []