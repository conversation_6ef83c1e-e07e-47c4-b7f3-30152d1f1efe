from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class TennesseeDepartmentOfEconomic(OCSpider):
    name = "TennesseeDepartmentOfEconomic"

    country = "US"
    
    charset="utf-8"

    start_urls_names = {
        "https://tnecd.com/media/newsroom/" : "Newsroom"
    }

    @property
    def language(self) -> str:
        return "English"
    
    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self) -> str:
        return "US/Eastern"
    
    def get_articles(self, response) :
        #Some child article have differnet structure like on first page 3 url have different xpath for date and author
        return response.xpath('//a[@class="newsCardLink"]/@href').getall()
    
    def get_href(self, entry: str) -> str:
        return entry

    def get_title(self, response) :
        return response.xpath('//div[@class="large-10 medium-10 cell text-center"]/h1/text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="grid-container"]//p//text()').getall())

    def get_images(self, response) :
        return []

    def date_format(self) -> str:
        return "%B %d, %Y"

    def get_date(self, response) -> str:
        date = response.xpath('//p[@class="date"]/text()').getall()
        return date[1].strip()
    
    def get_authors(self, response) :
        author = response.xpath('//p[@class="author"]/text()').getall()
        return author[1].strip()
    
    def get_document_urls(self, response, entry=None):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        return response.xpath('//a[@class="nextpostslink"]/@href').get()

    
    
    