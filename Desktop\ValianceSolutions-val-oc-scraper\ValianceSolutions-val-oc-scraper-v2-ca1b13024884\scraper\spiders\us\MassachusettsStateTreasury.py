from typing import Optional
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
from datetime import datetime
import scrapy
import urllib.parse
import math

class MassachusettsStateTreasury(OCSpider):
    name = "MassachusettsStateTreasury"

    country = "US"

    start_urls_names = {
        "https://www.masstreasury.org/news": "News"
    }

    custom_settings = {
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter'
    }
    
    api_start_urls = {
        "https://www.masstreasury.org/news": {
            "url": "https://www.masstreasury.org/_api/blog-frontend-adapter-public/v2/post-feed-page",
            "payload": {
                "includeContent": "false", 
                "languageCode": "en", 
                "locale": "en", 
                "page": "1", 
                "pageSize": "10", 
                "type": "ALL_POSTS"
            }
        }
    }
    
    headers = {
        "Content-Type": "application/json;charset=UTF-8",
        "Authorization" : "kqfIsA2HHMbRf1eqeQj6woDnoAwCcKxMzT5JdT0FXaw.eyJpbnN0YW5jZUlkIjoiZGI3ZjhhYTItOTA4My00MWE2LTk2YmMtODZkNDVlMzVkZjkwIiwiYXBwRGVmSWQiOiIyMmJlZjM0NS0zYzViLTRjMTgtYjc4Mi03NGQ0MDg1MTEyZmYiLCJtZXRhU2l0ZUlkIjoiZGI3ZjhhYTItOTA4My00MWE2LTk2YmMtODZkNDVlMzVkZjkwIiwic2lnbkRhdGUiOiIyMDI1LTAzLTI1VDA0OjMxOjI2LjYwMloiLCJkZW1vTW9kZSI6ZmFsc2UsImFpZCI6ImY4YWE0YjM2LWU5MzUtNDcyZC1iYzIwLWI1YTdiODRiNjEzYiIsInNpdGVPd25lcklkIjoiMWNkYWVjYzgtN2U5Yi00MDA5LTlkZDgtOTY0Njg1MGY4ZWZiIiwiYnMiOiJRMzNzRDlLZVJ2RGMzRzlFNzB5Q1hJZUEwUVJ3cjNBVmU4dFRrc3FnMEpZLklqUTNOVE14WW1Nd0xXUXpaamN0TkdNd1l5MDRZalF5TFdOa1lqWmxORGs0TnpJNE9DSSIsInNjZCI6IjIwMTgtMDItMjdUMTc6MzE6MjkuODI1WiJ9"

    }
    
    charset = "utf-8"

    @property
    def source_type(self): 
        return "ministry"
    
    @property
    def language(self): 
        return "English"
    
    @property
    def timezone(self): 
        return "America/New_York"

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_data = self.api_start_urls.get(start_url)
        if not api_data: 
            return
        payload = response.meta.get("payload", api_data["payload"].copy())
        api_url = api_data["url"]
        full_api_url = f"{api_url}?{urllib.parse.urlencode(payload)}"
        yield scrapy.Request(
            url=full_api_url, 
            method="GET", 
            headers=self.headers, 
            callback=self.parse,
            meta={
                "start_url": start_url, 
                "api_url": api_url, 
                "payload": payload, 
                "current_page": payload["page"]
            }
        )

    def get_articles(self, response) -> list:
        try:
            data = response.json()
            return [post.get("url", {}).get("base", "") + post.get("url", {}).get("path", "") 
                    for post in data.get("postFeedPage", {}).get("posts", {}).get("posts", [])]
        except:
            return []

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//p[@class="j-JU7 WOBLT iRNRX KdLlf"]/span/strong/span/text()').get(default="").strip()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='zr1cm']//p//text()").getall())

    def get_images(self, response) -> list:
        return response.xpath('//div[@class="YD7ZH bUMKC gwUOq"]//img/@src').getall()

    def date_format(self) -> str:
        return "%m-%d-%Y"
    
    def get_date(self, response) -> Optional[str]:
        date_text = response.xpath('//span[contains(@class, "post-metadata__date")]/text()').get()
        if not date_text: 
            return None
        for fmt in ("%B %d, %Y", "%b %d, %Y", "%B %d", "%b %d"):
            try:
                date_obj = datetime.strptime(date_text.strip(), fmt)
                if fmt in ("%B %d", "%b %d"): date_obj = date_obj.replace(year=2025)
                return date_obj.strftime("%m-%d-%Y")
            except ValueError:
                continue
        return None

    def get_authors(self, response):
        return []
    
    def get_next_page(self, response, current_page: Optional[int] = 1) -> Optional[int]:
        try:
            data = response.json()
            total = data.get("postFeedPage", {}).get("posts", {}).get("metaData", {}).get("total", 0)
            if not total:
                total = data.get("postFeedPage", {}).get("pagingMetaData", {}).get("total", 0)
            page_size = int(response.meta.get("payload", {}).get("pageSize", "10"))
            max_pages = math.ceil(total / page_size)
        except:
            return None
        next_page = int(current_page) + 1
        return next_page if next_page <= max_pages else None

    def get_page_flag(self) -> bool:
        return False

    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        next_page = self.get_next_page(response, current_page)
        if next_page:
            api_url = response.meta.get('api_url')
            payload = response.meta.get('payload', {}).copy()
            payload['page'] = str(next_page)
            full_api_url = f"{api_url}?{urllib.parse.urlencode(payload)}"
            yield scrapy.Request(
                url=full_api_url, method='GET', headers=self.headers, callback=self.parse_intermediate,
                meta={'start_url': start_url, 'current_page': next_page, 'api_url': api_url, 'payload': payload}
            )