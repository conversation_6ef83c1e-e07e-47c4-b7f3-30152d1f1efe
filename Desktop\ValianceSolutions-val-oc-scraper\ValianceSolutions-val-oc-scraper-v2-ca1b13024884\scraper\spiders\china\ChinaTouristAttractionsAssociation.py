from datetime import datetime
import json
import scrapy
from scraper.OCSpider import <PERSON><PERSON>pider
from dotenv import load_dotenv
from scraper.middlewares import HeadlessBrowserProxy
from typing import Optional
from scraper.utils.helper import body_normalization
load_dotenv()

class ChinaTouristAttractionsAssociation(OCSpider):
    name = "ChinaTouristAttractionsAssociation"

    custom_settings = {
        "DOWNLOAD_DELAY": 2,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    start_urls_names = {
        "http://www.chinataa.org/xhdtlist.aspx": "行业要闻",
        # "http://www.chinataa.org/xhdtlist.aspx#":"行业要闻",    # This start url is same as first start URL
        "http://www.chinataa.org/Party_affairs_knowledge.aspx" : "党务专栏"
    }

    api_start_urls = {
        'http://www.chinataa.org/xhdtlist.aspx': {
            "url": "http://www.chinataa.org/xhdtlist.aspx/getdemo",
            "payload": {
                "lx": "45",
                "num": "1",
            },
        },
        'http://www.chinataa.org/Party_affairs_knowledge.aspx': [
            {
                "url": "http://www.chinataa.org/Party_affairs_knowledge.aspx/getlist",
                "payload": {"lx": "0", "num": "1"}
            },
            {
                "url": "http://www.chinataa.org/Party_affairs_knowledge.aspx/getlist1",
                "payload": {"lx": "0", "num": "1"}
            }
        
        ]
    }     

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_data_list = self.api_start_urls.get(start_url, [])
        if isinstance(api_data_list, dict):
            api_data_list = [api_data_list]
        if not api_data_list:
            self.logger.error(f"No API configuration found for start_url: {start_url}")
            return
        for api_data in api_data_list:
            if not isinstance(api_data, dict):  # Ensure api_data is a dictionary
                self.logger.error(f"Invalid API data format for {start_url}: {api_data}")
                continue
            api_url = api_data.get("url")
            payload = api_data.get("payload", {}).copy()
            payload["num"] = payload.get("num", 1)
            if not api_url:
                self.logger.error(f"Missing 'url' key in API data: {api_data}")
                continue
            headers = {
                "Content-Type": "application/json;charset=UTF-8",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36"
            }
            yield scrapy.Request(
                url=api_url,
                method="POST",
                headers=headers,
                body=json.dumps(payload),
                callback=self.parse,
                meta={
                    "start_url": start_url,
                    "api_url": api_url,
                    "payload": payload,
                    "current_page": payload["num"]
                },
            )

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"
    
    @property
    def timezone(self) -> str:
        return "Asia/Shanghai"        

    def get_articles(self, response) -> list:
        try:
            start_url = response.meta.get("start_url")
            api_url = response.meta.get("api_url")  # Get API URL
            outer_data = json.loads(response.text)
            inner_data = json.loads(outer_data.get("d", "{}"))
            records = inner_data.get("data", [])

            if start_url in ["http://www.chinataa.org/xhdtlist.aspx"]:
                articles_urls = [
                    response.urljoin(f"/xhdtcontent.aspx?id={record.get('id')}&tp=xhdt")
                    for record in records if record.get("id")
                ]
            elif start_url == "http://www.chinataa.org/Party_affairs_knowledge.aspx":
                if api_url == "http://www.chinataa.org/Party_affairs_knowledge.aspx/getlist1":
                    articles_urls = [
                        response.urljoin(f"/about_dangwuzhishishow.aspx?id={record.get('id')}")
                        for record in records if record.get("id")
                    ]
                else:
                    articles_urls = [
                        response.urljoin(f"/about_dangjianshow.aspx?id={record.get('id')}")
                        for record in records if record.get("id")
                    ]
            else:
                self.logger.error(f"Unexpected start_url: {start_url}")
                return []
            return self.get_proxy_articles(articles_urls)
        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to decode JSON from response: {e}")
            return []
        except Exception as e:
            self.logger.error(f"Unexpected error while processing articles: {e}")
            return []
        
    def get_href(self, entry) -> str:
        return entry    

    def get_title(self, response) -> str:
        return response.xpath("//h1/text()").get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='wzcon']//text()").getall())

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response) -> str:
        date = response.xpath('//div[@class="time"]//text()').re_first(r"\d{4}-\d{1,2}-\d{1,2}")
        if not date:
            date = response.xpath('//div[@class="time"]//text()').re_first(r"\d{4}/\d{1,2}/\d{1,2}")
            date = date.replace("/", "-")
            date_obj = datetime.strptime(date, "%Y-%m-%d")
            return date_obj.strftime("%Y-%m-%d")
        return date

    def get_authors(self, response) -> list:
        return []

    def get_images(self, response) -> list:
        images = response.xpath('//div[@class="wzcon"]/p/img/@src').getall()
        if not images:
            images = response.xpath('//div[@class="wzcon"]//img/@src').getall()
        return images

    def get_next_page(self, response, current_page):
        try:
            outer_data = json.loads(response.text)
            inner_data = json.loads(outer_data.get("d", "{}"))
            max_pages = int(inner_data.get("zy", 1))
            current_page = int(current_page) 
            next_page = current_page + 1 if current_page < max_pages else None
            return next_page
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON parsing error in pagination: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Unexpected error in get_next_page(): {e}")
            return None
        
    def get_page_flag(self) -> bool:
        return False    

    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        api_url = response.meta.get("api_url")
        if not api_url:
            self.logger.error("API URL not found in meta data.")
            return
        next_page = self.get_next_page(response, current_page)
        if next_page:
            payload = response.meta.get("payload").copy()
            payload["num"] = next_page
            yield scrapy.Request(
                url=api_url,
                method="POST",
                body=json.dumps(payload),
                headers={"Content-Type": "application/json;charset=UTF-8"},
                callback=self.parse,
                meta={
                    "current_page": next_page, 
                    "start_url": start_url,
                    "payload": payload,
                    "api_url": api_url
                },
            )
        else:
            self.logger.info("No more pages to fetch, stopping pagination.")

    def get_proxy_articles(self, articles):
        try:
            hbp = HeadlessBrowserProxy()
            proxy_urls = [hbp.get_proxy(url, timeout = 10000) for url in articles]
            return proxy_urls
        except Exception as e:
            return []        