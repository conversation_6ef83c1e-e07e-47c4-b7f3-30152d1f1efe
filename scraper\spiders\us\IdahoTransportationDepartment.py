from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime

class IdahoTransportationDepartment(OCSpider):
    name = "IdahoTransportationDepartment"

    country = "US"

    start_urls_names = {
        "https://itd.idaho.gov/news-info/": "News & Information",
    }
        
    charset = "utf-8"

    @property
    def language(self): 
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "America/Denver"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="wpnaw-news-grid-content"]//h2[@class="wpnaw-news-title"]/a/@href').getall()
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//header[@class="entry-header"]/h1[@class="entry-title"]/text()').get()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="entry-content"]/p/text()').getall()) 
        
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return"%m-%d-%Y"
    
    def get_date(self, response) -> str:
        date_str = response.xpath('//div[@class="entry-meta"]//time[@class="entry-date published updated"]/text()').get() \
                or response.xpath('//time[@class="entry-date published"]/text()').get()
        return datetime.strptime(date_str.strip(), "%B %d, %Y").strftime("%m-%d-%Y")
        
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        next_page = response.xpath('//a[@class="next page-numbers"]/@href').get()
        if not next_page:
           return None
        else:
            return next_page