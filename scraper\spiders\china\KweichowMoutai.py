from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from typing import List

class KweichowMoutai(OCSpider):
    name = "KweichowMoutai"

    start_urls_names = {
        "https://www.moutaichina.com/mtgf/xwzx/gsxw/index.html": "贵州茅台",
        "https://www.moutaichina.com/mtgf/shzr/shzrbg/index.html" : "贵州茅台", # No articles, 2 pdfs found with no dates
        "https://www.moutaichina.com/mtgf/tzzgx/cwbg/index.html" : "贵州茅台",
    }

    custom_settings = {
    "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }
    
    charset = "iso-8859-1"

    @property
    def source_type(self) -> str:
        return "SOE"
    
    @property
    def timezone(self):
        return "Asia/Shanghai"

    article_data_map = {}

    def get_articles(self, response) -> list:
        self.extract_articles_with_dates(response)
        return list(self.article_data_map.keys())

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")

    def get_body(self, response) -> str:
        if '.pdf' in response.url.lower():
            return ''
        return body_normalization(response.xpath("//p//text()").getall())

    def date_format(self) -> str:
        return '%Y-%m-%d'
    
    def get_date(self, response) -> str:
        if 'mtjt' in response.url:
            return "2025-04-02"
        return self.article_data_map[response.request.meta.get('entry')].get("date")
    
    def get_images(self, response) -> list:
        return []
    
    def get_authors(self, response):
        if '.pdf' in response.url.lower():
            return ''
        return response.xpath("//font[contains(text(), 'Author:')]/text()").getall()
    
    def get_document_urls(self, response, entry=None) -> List[str]:
        return self.article_data_map[response.request.meta.get('entry')].get("pdf")
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        next_link = response.xpath("//div[@class='mt_wrap']/a[contains(text(), '>')]")
        if next_link:
            tagname = next_link.xpath("./@tagname").get()
            if tagname:
                return response.urljoin(tagname)
        return None
    
    def extract_articles_with_dates(self, response):
        mapping = {}
        articles_type1 = response.xpath("//div[@class='second-news-item']")
        for article in articles_type1:
            a_tag = article.xpath(".//div[@class='second-news-item-title']/a")
            url = a_tag.xpath("./@href").get()
            title = a_tag.xpath("./@title").get()
            date = article.xpath(".//div[@class='second-news-item-date']//text()").get()
            if url and title and date:
                full_url = response.urljoin(url.strip())
                clean_date = date.strip()
                mapping[full_url] = {
                    "title": title.strip(),
                    "date": clean_date,
                    "pdf": [full_url] if url.endswith(".pdf") else []
                }
        articles_type2 = response.xpath("//li[contains(@class, 'clearfix')]/a | //li/a[@class='clearfix'] | //div[@class='conW']//ul//li//a")
        for a_tag in articles_type2:
            url = a_tag.xpath(".//@href").get()
            title = a_tag.xpath("./@title | .//span[@class='shzrbgtitle']//text() | .//span[@class='newTitle']//text() | .//font/font/text()").get()
            date = a_tag.xpath("../span//text() | .//span[@class='newData']//text()").get()
            if not date:
                date = "None"
            print(url,title,date)
            if url and title and date:
                full_url = response.urljoin(url.strip())
                clean_date = date.strip()
                mapping[full_url] = {
                    "title": title.strip(),
                    "date": clean_date,
                    "pdf": [full_url] if url.endswith(".pdf") else []
                }
        self.article_data_map.update(mapping)
        return self.article_data_map