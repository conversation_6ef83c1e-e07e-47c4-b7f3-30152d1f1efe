from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from urllib.parse import urljoin
import datetime

class XingyuCoLtd(OCSpider):
    name = "XingyuCoLtd"

    start_urls_names = {
        "https://en.xyl.cn/News/2.html": "星宇股份",
    }

    custom_settings = {
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350,
    },
    }
    
    HEADLESS_BROWSER_WAIT_TIME = 10000

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "SOE"
    
    @property
    def language(self):
        return "English"
    
    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:   
        articles = list(set(response.xpath('//div[@class="p_list"]//div[contains(@class,"p_loopitem")]//div[contains(@class,"e_image-12")]//a[starts-with(@href, "/News_dt")]/@href | //div[@class="cbox-11-0 p_item"]//a//@href').getall()))
        full_urls = [urljoin(response.url, href) for href in articles]  
        return full_urls
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//div[@class='cbox-18-0 p_item']/h1[@class='e_h1-29 s_subtitle response-transition']/text()| //h1[contains(@class, 'e_h1-29')]/text() |//h1[@class='e_h1-29 s_subtitle response-transition']/text() | //h1[@class='rich_media_title ']//text()").get().strip()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="e_richText-21 s_title clearfix fa_details"]//text()').getall())
    
    def get_images(self, response) -> list:
        return response.xpath("//div[@class='e_richText-21 s_title clearfix fa_details']//img/@src").getall()
       
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date=response.xpath("//p[contains(@class, 'e_timeFormat-23')]/text()").get()
        if not date:
            date_str= response.xpath("//em[@id='publish_time']//text())").get()
            date_obj = datetime.strptime(date_str, "%B %d, %Y %H:%M")
            date = date_obj.strftime("%Y-%m-%d")
            return date
        return date
    
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]: 
        next_page = response.xpath("//div[@class='p_page']//a[@class='page_a page_next ']//@href").get()
        if next_page:
            return response.urljoin(next_page)
        return None