from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from typing import List, Union

class MaineOfficeOfTheStateTreasurer(OCSpider):
    name = 'MaineOfficeOfTheStateTreasurer'

    country = "US"

    start_urls_names = {
        'https://www.maine.gov/treasurer/news/':'News',
    }

    charset="utf-8"

    @property
    def source_type(self) -> str:
        return 'ministry'
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Eastern"
    
    def get_articles(self, response) -> list:
        return response.xpath("//span[@class = 'field-content']//a//@href").getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self,response) -> str:
        return response.xpath("//div[@id='block-treasurer-page-title']//h1//text()[normalize-space()]").get() 
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@id="block-treasurer-content"]//text()').getall()) 
    
    def get_images(self, response) -> List[str]:
        return response.xpath("//div[@id='block-treasurer-content']//img//@src").getall() 
        
    def date_format(self) -> str:
        return '%B %d, %Y'

    def get_date(self, response) -> str:
        return response.xpath("//div[@id='block-treasurer-content']//time/text()").re_first(r"([A-Za-z]+ \d{1,2}, \d{4})") 
    
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Union[None, str]:
        next_page = response.xpath("//li[@class='pager__item pager__item--next']//a[@title ='Go to next page']//@href").get()
        if next_page:
            return response.urljoin(next_page)
        else:
            return None