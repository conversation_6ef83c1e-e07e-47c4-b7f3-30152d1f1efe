import scrapy
from typing import Optional
from scraper.OCSpider import <PERSON><PERSON><PERSON><PERSON>
from scraper.utils.helper import body_normalization

class AlaskaAttorneyGeneral(OCSpider):
    name = 'AlaskaAttorneyGeneral'
    
    country = "US"

    start_urls_names = {
        'https://law.alaska.gov/press/news.html': 'Press Releases',
    }
    
    include_rules = [r'^https://law\.alaska\.gov/press/releases/.*']

    visited_links = set()  # Keep track of visited URLs to avoid reprocessing

    def parse_intermediate(self, response):
        articles = response.xpath('//ul/li/a/@href').getall()
        total_articles = len(articles)
        start_url = response.meta.get("start_url")
        # Have more than 100 articles on start URL
        for start_idx in range(0, total_articles, 100):
            yield scrapy.Request(
                    url=start_url,
                    callback=self.parse,
                    meta={
                        'start_idx': start_idx, 
                        'start_url': start_url
                    },
                    dont_filter=True
            )
    
    charset = "utf-8" 
    
    @property
    def language(self):
        return "English"
    
    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self):
        return "US/Central"
    
    def get_articles(self, response) -> list:
        articles = response.xpath('//ul/li/a/@href').getall()
        unique_articles = list(set(articles)) 
        start_idx = response.meta.get('start_idx', 0)
        end_idx = start_idx + 100   # Have more than 100 articles on start URL
        return unique_articles[start_idx:end_idx]   
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//article/h2/text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//article/p[not(@class="bold")]//text()').getall())
    
    def get_images(self, response) -> list:
         return response.xpath('//figure[@class="right"]/img/@src').getall()
    
    def date_format(self) -> str:
        return "%B %d, %Y"
    
    def get_date(self, response) -> str:
        return response.xpath('//article/p[@class="bold"]/text()').get()
    
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False 
    
    def get_next_page(self, response) -> Optional[str]: 
         # Extract all archive links
        next_pages = response.xpath('//ul[@class="list_center"]/li/a/@href').getall()
        filtered_pages = next_pages[1:] # Skip the first link
        for next_page in filtered_pages:
            if next_page not in self.visited_links:
                self.visited_links.add(next_page)
                return next_page 
        else:    
            return None