import json
import re
from scraper.OCSpider import OCSpider
from scraper.middlewares import HeadlessBrowserProxy
import scrapy
from dotenv import load_dotenv
from scraper.utils.helper import body_normalization
from typing import Optional
import logging
load_dotenv()

class ChinaAssociationforEducationalTechnology(OCSpider):
    name = "ChinaAssociationforEducationalTechnology"

    start_urls_names = {
        "http://www.caet.org.cn/news" : "新闻动态",                      
        "http://www.caet.org.cn/notice?category=0" : "通知公告",  
        "http://www.caet.org.cn/notice?category=6" : "会议资料",  
        "http://www.caet.org.cn/notice?category=5" : "通知公告",
    }

    charset = "utf-8"

    custom_settings = {
        "DOWNLOAD_DELAY": 2,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    article_to_pdf_urls_mapping={}

    api_start_urls = {
        "http://www.caet.org.cn/news": {
            "url": "http://www.caet.org.cn/news/GetNewsList?pageSize=10&Type=0&SchoolId=0&pageIndex=1",
            "payload": {
                "pageSize": "10",
                "Type" : "0",
                "SchoolId" : "0",
                "pageIndex": "1",
            },
        },
        "http://www.caet.org.cn/notice?category=0": {
            "url": "http://www.caet.org.cn/Notice/GetNoticeList?pageSize=10&Category=0&OrgId=0&pageIndex=1",
            "payload": {
                "pageSize": "10",
                "Category" : "0",
                "OrgId" : "0",
                "pageIndex": "1",
            },
        },
        "http://www.caet.org.cn/notice?category=6": {
            "url": "http://www.caet.org.cn/Notice/GetNoticeList?pageSize=10&Category=6&OrgId=0&pageIndex=1",
            "payload": {
                "pageSize": "10",
                "Category" : "6",
                "OrgId" : "0",
                "pageIndex": "1",
            },
        },
        "http://www.caet.org.cn/notice?category=5": {
            "url": "http://www.caet.org.cn/Notice/GetNoticeList?pageSize=10&Category=5&OrgId=0&pageIndex=1",
            "payload": {
                "pageSize": "10",
                "Category" : "5",
                "OrgId" : "0",
                "pageIndex": "1",
            },
        },
    }

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")      
        api_data=self.api_start_urls.get(start_url)
        payload = api_data["payload"]
        payload["pageIndex"] = payload.get("pageIndex")
        # Check if the start_url is the first start URL
        if start_url == "http://www.caet.org.cn/news":
            # Handling the first URL's special case logic
            url = f"http://www.caet.org.cn/news/GetNewsList?pageSize=10&Type=0&SchoolId=0&pageIndex={payload['pageIndex']}"
        else:
            # Handling the remaining the Start URLs
            Category=payload.get("Category")
            url=f"http://www.caet.org.cn/Notice/GetNoticeList?pageSize=10&Category={Category}&OrgId=0&pageIndex={payload['pageIndex']}"    
        yield scrapy.Request(
            url=url,
            method = "GET",
                headers={
                    'Content-Type': 'application/json',
                },
                body = json.dumps(payload),
                callback = self.parse,   
                meta={
                    "start_url": start_url,
                    "api_url": api_data["url"],
                    "payload": payload,
                    "current_page": payload["pageIndex"]
                }
        )
    
    @property
    def source_type(self) -> str:
        return "IndustryAssociation"
    
    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response):
        start_url = response.meta.get("start_url")
        data = json.loads(response.text)
        article_urls = []
        mapping = {}
        hbp = HeadlessBrowserProxy() 
        for item in data['items']:
            article_id = item.get("id")
            pdf_urls = item.get('filepath')
            # Determining the base URL corresponding to the start_url
            if start_url == "http://www.caet.org.cn/news":
                url = hbp.get_proxy(f"http://www.caet.org.cn/HtmlNews/1/{article_id}.html", timeout=50000)
            elif start_url in ["http://www.caet.org.cn/notice?category=0", "http://www.caet.org.cn/notice?category=5"]:
                url = hbp.get_proxy(f"http://www.caet.org.cn/Notice/detail?id={article_id}", timeout=50000)
            elif start_url == "http://www.caet.org.cn/notice?category=6":
                url = hbp.get_proxy(f"http://www.caet.org.cn/Notice/FileShow?id={article_id}", timeout=50000)
            # Map the URL to its corresponding PDF URL and append to local mapping variable
            if url:
                article_urls.append(url)
                if pdf_urls:
                    mapping[url] = pdf_urls
        # Update pdf_urls mapping after looping through all items 
        if mapping:
            self.article_to_pdf_urls_mapping.update(mapping)
        return article_urls
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//article[@class="article at01"]//h1//text()').get()
    
    def get_body(self, response) -> str:
        return  body_normalization(response.xpath('//section[@class="content"]//p//text()').getall())
    
    def get_images(self, response, entry=None):
        return response.xpath('//section[@class="content"]//img/@src').getall()
    
    def get_authors(self, response, entry=None) -> list[str]:
        return []
    
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response, entry=None):
        date_text= response.xpath('//article[@class="article at01"]//dd[2]//text()').get()
        match = re.search(r'(\d{4}-\d{1,2}-\d{1,2})', date_text)
        if match:
            full_date = match.group(1)  
            return full_date
        else:
            return None
    
    def get_document_urls(self, response, entry=None)->list :
        # Child articles do not have direct pdf hrefs
        article_url = response.url
        pdf_urls = self.article_to_pdf_urls_mapping.get(article_url, None)
        if pdf_urls:
            return [pdf_urls]
        else:
            return None
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response,current_page ):
        max_pages=response.json().get('totalPages',{})
        current_page = int(response.meta.get('current_page'))+1
        return str(current_page) if current_page < max_pages else None
    
    def go_to_next_page(self, response, start_url, current_page: Optional[str] = "1"):
        api_url = response.meta.get("api_url")
        if not api_url:
            logging.info("API URL not found in meta data.")
            return None
        next_page = self.get_next_page(response, current_page)
        if next_page:
            payload = response.meta.get('payload')
            payload["pageIndex"] = next_page 
            yield scrapy.Request(
            url = api_url,
            method = "GET",
            dont_filter=True,
            body = json.dumps(payload),
            callback=self.parse_intermediate,
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "payload": payload,
                "current_page": payload["pageIndex"]
            },
        )
        else:
            self.logger.info(f"No more pages to crawl for start_url: {start_url}")