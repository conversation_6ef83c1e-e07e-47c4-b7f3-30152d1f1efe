import scrapy
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import datetime
import re

class MarylandDepartmentOfLabour(OCSpider):
    name = 'MarylandDepartmentOfLabour'
    
    country = "US"

    start_urls_names = {
        'http://www.dllr.state.md.us/whatsnews/': 'News',
    }
    
    def parse_intermediate(self, response):
        articles = response.xpath('//div[@id = "accordion"]//div/ul/li/a/@href').getall()
        total_articles = len(articles)
        start_url = response.meta.get("start_url")
        for start_idx in range(0, total_articles, 100):
            yield scrapy.Request(
                    url=start_url,
                    callback=self.parse,
                    meta={
                        'start_idx': start_idx, 
                        'start_url': start_url
                    },
                    dont_filter=True
            )
    
    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Central"
    
    def get_articles(self, response) -> list:
        articles =  response.xpath('//div[@id = "accordion"]//div/ul/li/a/@href').getall()
        unique_articles = list(set(articles)) 
        start_idx = response.meta.get('start_idx', 0)
        end_idx = start_idx + 100
        return unique_articles[start_idx:end_idx]
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self,response) -> str:
        return response.xpath('//*[@id="mdgovMain"]/h1/text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//*[@id="mdgovMain"]/p/text()').getall())
    
    def get_images(self, response) -> list[str]:
        return []

    def date_format(self) -> str:
        return "%m-%d-%Y"

    def get_date(self, response) -> str:
        paragraphs = response.xpath('//p//text()').getall()
        text = " ".join(paragraphs).strip()
        date_patterns = [
        (r"\(([A-Za-z]+\.? \d{1,2}, \d{4})\)", "%B %d, %Y"),
        (r"(\d{1,2} [A-Za-z]+\.?, \d{4})", "%d %B, %Y"),
        (r"(\d{1,2}/\d{1,2}/\d{4})", "%m/%d/%Y"),
        (r"(\d{1,2}-\d{1,2}-\d{4})", "%m-%d-%Y"),
        (r"(\d{4}-\d{1,2}-\d{1,2})", "%Y-%m-%d"),
        (r"(\d{1,2}\.\d{1,2}\.\d{4})", "%m.%d.%Y"),
        (r"(\d{4}\.\d{1,2}\.\d{1,2})", "%Y.%m.%d")
        ]
        for pattern, fmt in date_patterns:
            match = re.search(pattern, text)
            if match:
                date_string = match.group(1)
                clean_date = date_string.replace(".", "")
                try:
                    date_object = datetime.datetime.strptime(clean_date, fmt)
                    return date_object.strftime("%m-%d-%Y")
                except ValueError:
                    try:
                        if "%B" in fmt:
                            date_object = datetime.datetime.strptime(clean_date, fmt.replace("%B", "%b"))
                            return date_object.strftime("%m-%d-%Y")
                        elif "%b" in fmt:
                            date_object = datetime.datetime.strptime(clean_date, fmt.replace("%b", "%B"))
                            return date_object.strftime("%m-%d-%Y")
                    except ValueError:
                        continue
        return None

    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        # No next page is there to scrap
        return None