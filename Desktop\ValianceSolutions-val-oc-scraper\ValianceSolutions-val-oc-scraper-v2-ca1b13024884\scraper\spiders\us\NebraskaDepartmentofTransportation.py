from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
from typing import List

class NebraskaDepartmentofTransportation(OCSpider):
    name = 'NebraskaDepartmentofTransportation'

    country = "US"
    
    custom_settings = {
		"DOWNLOADER_MIDDLEWARES" : {
			'scraper.middlewares.HeadlessBrowserProxy': 350,
		},
		"DOWNLOAD_DELAY" : 2,
	}
    
    HEADLESS_BROWSER_WAIT_TIME = 5000 
    
    start_urls_names = {
        'https://dot.nebraska.gov/news-events/': "News-Events"
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Central"

    article_data_map = {}  # Mapping date with articles from start URL

    def get_articles(self, response) -> list:
        self.extract_articles_with_dates(response)
        return [response.urljoin(link) for link in response.xpath("//div[@class='blog-post k-listview-item']//h5//a//@href").getall()]

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_data_map[response.request.meta.get('entry')].get("title")
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='ms-rtestate-field']//p//text() | //div[@class='ms-rtestate-field']//ul//li//text() | //div[@class='ms-rtestate-field']//div//text()").getall())
    
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return '%d %B %Y'

    def get_date(self, response) -> str:
        return self.article_data_map[response.request.meta.get('entry')].get("date")
    
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None) -> List[str]:
        return self.article_data_map[response.request.meta.get('entry')].get("pdf")

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        #pagination remaining
        return None
    
    def extract_articles_with_dates(self, response):
        # Function to extract dates of respective articles from start URL
        mapping = {}
        for article in response.xpath("//div[@class='blog-post k-listview-item']"):
            url = article.xpath(".//h5//a/@href").get()
            title = article.xpath(".//h5//a/text()").get()
            date = article.xpath(".//ul//li//text()").get()
            if url and title and date:
                full_url = response.urljoin(url.strip())
                clean_date=date.strip()
                mapping[full_url] = {"title": title.strip(), "date": clean_date, "pdf": [full_url]}
            self.article_data_map.update(mapping)
        return self.article_data_map