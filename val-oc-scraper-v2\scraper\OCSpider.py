import re
import warnings
from abc import abstractmethod, ABCMeta
from datetime import datetime
from typing import Dict, List
import json
import pytz
from scraper.utils.helper import get_source_tier
import scrapy
from bs4 import BeautifulSoup
from scrapy.http import Request
from scrapy.utils.deprecate import method_is_overridden
from .utils.Website import Website
from .wrapper import *

class OCSpider(scrapy.Spider):
    __metaclass__ = ABCMeta

    def __init__(self, name=None, **kwargs):
        super().__init__(name, **kwargs)
        self.start_urls = []
        self.threshold = 0.3
        self.page = None
        self.tier = get_source_tier(self.name, source=getattr(self, "source", None))
        # this is a flag we set while running a backfill for a spider
        self.backfill = bool(kwargs.get("backfill", False))
        self.articles_failed_to_scrape = []
        self.overall_article_set = set()

        # we need to set the date as the spider's timezone
        spider_timezone = pytz.timezone(self.timezone)
        
        if "date" in kwargs:
            self.run_date = datetime.strptime(kwargs["date"], "%Y-%m-%d %H:%M:%S")
            utc_date = pytz.utc.localize(self.run_date)
            self.run_date = utc_date.astimezone(spider_timezone)

        else:
            # use the current date as the run date
            self.run_date = datetime.now(spider_timezone)

        self.status = kwargs.get("status", None)

        if not self.start_urls_names:
            raise AttributeError(
                "OC Spider could not start: 'start_urls_names' not found. Please see readme.md")

        if "start_url" in kwargs:
            if kwargs["start_url"] == "":
                for start_url in self.start_urls_names:
                    self.start_urls.append(start_url)
            else:
                self.start_urls = json.loads(kwargs["start_url"])
        else:
            for start_url in self.start_urls_names:
                try:
                    self.start_urls.append(start_url)
                except:
                    logging.error(f"Error recording start url for {start_url}")
                
    exclude_rules: List[str] = []
    include_rules: List[str] = []

    websites: Dict[str, Website] = {}

    regex_multiline = r"\n{1,}"
    regex_space_eachline = r"\s+\n"

    # You can override this var to the desired charset
    charset = 'utf-8'

    @property
    @abstractmethod
    def source_type(self) -> str:
        raise NotImplementedError

    @property
    @abstractmethod
    def timezone(self) -> str:
        raise NotImplementedError

    @abstractmethod
    def get_articles(self, response) -> list:
        pass
    @abstractmethod
    def get_page_flag(self) -> bool:
        pass

    @abstractmethod
    def get_href(self, entry) -> str:
        pass

    @abstractmethod
    def get_title(self, response, entry=None) -> str:
        pass

    @abstractmethod
    def get_body(self, response, entry=None) -> str:
        pass

    @abstractmethod
    def date_format(self) -> str:
        pass

    @abstractmethod
    def get_date(self, response, entry=None) -> int:
        pass

    @abstractmethod
    def get_authors(self, response, entry=None) -> List[str]:
        pass

    @abstractmethod
    def get_images(self, response, entry=None) -> List[str]:
        pass

    @abstractmethod
    def get_next_page(self, response) -> List[str]:
        pass

    @abstractmethod
    def get_meta(self, response) -> list:
        pass

    @abstractmethod
    def get_pdf(self, response, entry=None):
        pass

    @abstractmethod
    def get_document_urls(self, response, entry=None) -> list:
        """
        This function should return a list of pdf urls
        [
            pdf_url_1,
            pdf_url_2,
            pdf_url_3
        ]
        """
        pass
    
    def start_requests(self):
        cls = self.__class__
        self.crawler.stats.set_value("articles_crawled", 0)
        self.crawler.stats.set_value("articles_successfully_scraped", 0)
        self.crawler.stats.set_value("articles_exist_in_db", 0)
        self.crawler.stats.set_value("articles_failed_to_scrape", 0)
        self.crawler.stats.set_value("articles_filtered_due_to_include_exclude_rule", 0)
        self.crawler.stats.set_value("tier", self.tier)
        self.crawler.stats.set_value("articles_with_no_dates", 0)

        
        if self.backfill:
            self.get_already_scraped_articles([])    
        
        if not self.start_urls and hasattr(self, 'start_url'):
            raise AttributeError(
                "Crawling could not start: 'start_urls' not found "
                "or empty (but found 'start_url' attribute instead, "
                "did you miss an 's'?)")
        if method_is_overridden(cls, self, 'make_requests_from_url'):
            warnings.warn(
                "Spider.make_requests_from_url method is deprecated; it "
                "won't be called in future Scrapy releases. Please "
                "override Spider.start_requests method instead "
                f"(see {cls.__module__}.{cls.__name__}).",
            )
            for url in self.start_urls:
                request = self.make_requests_from_url(url)
                request.meta['start_url'] = url
                yield request
        else:
            for url in self.start_urls:
                if hasattr(self, "parse_intermediate"):
                    callback = self.parse_intermediate
                else:
                    callback = self.parse
                if url == 'http://renshi.people.com.cn':
                    header = {"Host": "renshi.people.com.cn", "Referer":"http://renshi.people.com.cn/"}
                    request = Request(url, dont_filter=True, headers=header,callback=callback)
                else:
                    request = Request(url, dont_filter=True, callback=callback)
                request.meta['start_url'] = url
                yield request

    def get_already_scraped_articles(self, articles):
        # This is okay, for dev environment
        return None

    def is_already_crawled(self, start_url, _id):
        return self.websites.get(_id)

    first_articles_id: Dict[str, str] = {}

    should_go_to_next_page: bool = True

    def parse(self, response):

        start_url = response.request.meta['start_url']
        try:
            
            articles = self.get_articles(response)
            # get articles 
            # Limit only 100 articles per page for Ministries.
            if articles:
                articles = articles[:100]
            
            # extract the article urls and query BQ to check if they are already scraped
            if not self.backfill:
                self.get_already_scraped_articles([response.urljoin(self.get_href(entry)) for entry in articles])

            current_page_articles = set()
            for article_index, entry in enumerate(articles):

                href = self.get_href(entry)
                full_url = response.urljoin(href)
                current_page_articles.add(full_url)

            if len(self.overall_article_set.intersection(current_page_articles)) == len(current_page_articles):
                return

            for article_index, entry in enumerate(articles):
                href = self.get_href(entry)
                
                if article_index == len(articles) - 1:
                    is_last_article_of_page = True
                else:
                    is_last_article_of_page = False

                full_url = response.urljoin(href)
                self.overall_article_set.add(full_url)
                
                # If full url is already in start_url, then skip it
                # Reason: because some websites provide start_url as list of articles to scrape but we do not need to scrape them
                if full_url in self.start_urls: 
                    continue

                # Start incrementing if the article url is not in the start_urls
                self.crawler.stats.inc_value("articles_crawled")

                if len(self.include_rules) == 0:
                    should_scrap: bool = True
                    for exclude_rule in self.exclude_rules:
                        if re.compile(exclude_rule).findall(full_url):
                            should_scrap = False
                            break
                else:
                    should_scrap: bool = False
                    for include_rule in self.include_rules:
                        if re.compile(include_rule).findall(full_url):
                            should_scrap = True
                            break

                if should_scrap:

                    if self.first_articles_id.get(start_url) is None:
                        self.first_articles_id[start_url] = full_url
                    
                    if self.is_already_crawled(start_url, full_url) and self.name == 'ccgp_procurement' and self.status is None:
                        self.should_go_to_next_page = False
                        self.crawler.stats.inc_value("articles_exist_in_db")
                        break
                    
                    full_url = response.urljoin(href)
                    meta = {'retry_times': 0, 'is_last_article_of_page' : is_last_article_of_page, 'main_page_response' : response, 'start_url' : start_url, "entry": entry}
                    # to address cases where we extract dates from the start url page, we are looping through the article xpaths instead of article urls them selves
                    if getattr(self, "date_in_meta", None):
                        meta["date"] = self.get_date(entry)

                    if self.is_already_crawled(start_url, full_url) is None:
                        if is_last_article_of_page:
                            only_for_date = True
                            meta["only_for_date"] = only_for_date
                        
                        request = Request(
                            url=full_url,
                            method='GET',
                            encoding=response.request.encoding,
                            cookies=response.request.cookies,
                            headers=response.request.headers,
                            callback=self.parse_article,
                            meta=meta

                        )
                        yield request
                    else:
                        self.crawler.stats.inc_value("articles_exist_in_db")
                        yield None
                else:
                    self.crawler.stats.inc_value("articles_filtered_due_to_include_exclude_rule")
                    yield None

            self.page = self.get_page_flag()
            if self.page and len(articles) > 0 and self.should_go_to_next_page:
                current_page = response.request.meta.get('current_page', 1)
                if current_page:
                    logging.info(f"Going to Page {current_page}")
                    yield from self.go_to_next_page(response, start_url, current_page=current_page)
            
            if self.name == 'ccgp_procurement' and self.should_go_to_next_page is True and len(articles) > 0:
                current_page = response.request.meta.get('current_page', 1)
                yield from self.go_to_next_page(response, start_url)
        except:
            logging.error(traceback.format_exc())

    def go_to_next_page(self, response, start_url, current_page=None):
        next_page = self.get_next_page(response)
        if next_page is not None:
            next_page = response.urljoin(next_page)
            request = response.request.replace(url=next_page, callback=self.parse)
            request.meta['start_url'] = start_url
            request.meta['current_page'] = int(response.request.meta.get('current_page', 1)) + 1
            yield request
        else:
            yield None

    def parse_article(self, response) -> ArticleItem:

        _id = response.url

        start_url = response.meta["start_url"]

        article = build_article(start_url, _id, self, response, )
        
        scrap_article = self.scrap_article(article, response)

        article_info = scrap_article.get('value')

        print(article_info)
        url = response.url
        try:
            date = self.get_date(response)
        except Exception as e:
            self.logger.warning(f"Error extracting date from {url}: {e}")
            self.crawler.stats.inc_value("articles_with_no_dates")
            return

        if not date:
            self.logger.warning(f"Missing date for {url}")
            self.crawler.stats.inc_value("articles_with_no_dates")
            return

        # If we fail to scrape the article, we raise an exception so that it can be caught downstream in parse
        if scrap_article.get('success'):
            self.crawler.stats.inc_value("articles_successfully_scraped")
        else:
            self.crawler.stats.inc_value("articles_failed_to_scrape")
            self.articles_failed_to_scrape.append(response.url)
            logging.error(f"Error Scraping Article: {article['error']}")

        if article_info is not None and scrap_article.get("success") is True:
            article_date = article_info.get("date", None)

            article_date = datetime.fromtimestamp(article_date, pytz.timezone(self.timezone)).date()
            print(f"Article Date ----- {article_date}")

            if response.meta.get('is_last_article_of_page'):
                if self.page is None and (article_date.strftime('%Y-%m-%d') >= self.run_date.strftime('%Y-%m-%d') or self.name == "StateCouncil2"):
                    yield from self.go_to_next_page(response.meta['main_page_response'], response.meta['start_url'])
 
        if scrap_article.get("success") is True:
            if response.meta.get('only_for_date') == True:
                yield None
            else:
                yield scrap_article.get('value')
        else:
            article['error'] = str(scrap_article.get('value'))
            logging.error(f"Error Scraping Article: {article['error']}")
            return None

    
    @error_safe
    def scrap_article(self, article: ArticleItem, response) -> ArticleItem:
        entry = response.request.meta.get("entry", None)
        if getattr(self, "parse_entry", None):
            article['title'] = self.get_title(response, entry=entry).strip()
            body = BeautifulSoup(self.get_body(response, entry=entry,), features="lxml").get_text(separator="\n")
            date = self.get_date(response, entry=entry)
            article['images'] = self.get_images(response, entry=entry)
            authors = self.get_authors(response, entry=entry)
            
            if hasattr(self, "get_meta"):
                article["meta"] = self.get_meta(response, entry=entry)
            if hasattr(self, "get_subhead"):            
                article["subhead"] = self.get_subhead(response, entry=entry)
        else:
            article['title'] = self.get_title(response).strip()
            body = BeautifulSoup(self.get_body(response,), features="lxml").get_text(separator="\n")
            if not getattr(self, "date_in_meta", None):
                date = self.get_date(response)
            else:
                date = response.meta["date"]
            article['images'] = self.get_images(response)
            authors = self.get_authors(response)
            
            if hasattr(self, "get_meta"):
                article["meta"] = self.get_meta(response)
            if hasattr(self, "get_subhead"):            
                article["subhead"] = self.get_subhead(response)
            
        article['start_url'] = response.meta.get("start_url", "")

        # Clean body
        article['body'] = re.sub(self.regex_space_eachline, "\n",
                                 re.sub(self.regex_multiline, "\\n", body, 0, re.MULTILINE).lstrip())

        date_format = self.date_format()
        tz = pytz.timezone(self.timezone)
        date_obj = datetime.strptime(date, date_format)
        
        article['language'] = getattr(self, "language", "Chinese")

        date_of_article = tz.localize(datetime(date_obj.year, date_obj.month, date_obj.day, date_obj.hour, date_obj.minute, date_obj.second), is_dst=None)
        
        tz = pytz.timezone(self.timezone)
        
        # we need to check if we are backfilling as well, because when backfilling self.run_date could be
        # less than the date of article eg: we want to backfill until 2024-01-01 and the date of article being 2024-08-08
        if date_of_article.date() > self.run_date.date() and not self.backfill:
            article['date'] = self.run_date.timestamp()
            logging.debug(f"The parsed date ({date}) is greater than the current date - ({self.run_date})")
        else:
            # Converting datetime to timestamp
            article['date'] = date_of_article.timestamp()

        try:
            if authors:
                if not isinstance(authors, list):
                    authors = [authors]
            article['authors'] = authors
        except:
            article['authors'] = []

        article['country'] = getattr(self, "country", "China")
        
        # This part of the code has been updated to not include file upload, 
        # but in in production pdfs get uploaded to a storage bucket
        article["pdf_urls"] = self.get_document_urls(response, entry=entry)

        return article

    def is_url_already_scraped(self, url):
        # index = Config.config.elasticsearch.ministries_index
        # return self.es_utils.find_by_id(index, url)
        return None

    def close(self):
        # If any of the stats for some reason is not present, log and set the defaults to 0
        # since the stats are being set in one place, if one does not exist, the others will also not exist
        # therefore we can check if one exists and set all to 0
        if self.crawler.stats.get_value("articles_crawled") == None:
            self.crawler.stats.set_value("articles_crawled", 0)
            self.crawler.stats.set_value("articles_successfully_scraped", 0)
            self.crawler.stats.set_value("articles_exist_in_db", 0)
            self.crawler.stats.set_value("articles_failed_to_scrape", 0)
            self.crawler.stats.set_value("articles_filtered_due_to_include_exclude_rule", 0)
            self.crawler.stats.set_value("tier", self.tier)
            raise Exception(f"Stats not found for {self.name} - {self.date}")

        articles_to_scrape = (self.crawler.stats.get_value("articles_crawled") - self.crawler.stats.get_value("articles_exist_in_db") - \
            self.crawler.stats.get_value("articles_filtered_due_to_include_exclude_rule"))
        
        # this means that no articles were crawled, which is there is something wrong with scraper
        if self.crawler.stats.get_value("articles_crawled") == 0:
            logging.error(f"Zero articles were crawled for {self.name} - {self.run_date}")
            raise Exception(f"Zero articles were crawled for {self.name} - {self.run_date}")
        elif articles_to_scrape > 0 and \
            (self.crawler.stats.get_value("articles_failed_to_scrape")/ articles_to_scrape) > self.threshold:
            logging.error(f"Articles failed to scrape for {self.name} - {self.run_date}")
            raise Exception(f"Articles failed to scrape for {self.name} - {self.run_date}")
        else:
            logging.info(f"Articles scraped successfully for {self.name} - {self.run_date} with {self.crawler.stats.get_value('articles_crawled')} articles crawled and {self.crawler.stats.get_value('articles_successfully_scraped')} articles successfully scraped")