from datetime import datetime
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class GeorgiaOfficeOfTheStateTreasurer(OCSpider):
    name = "GeorgiaOfficeOfTheStateTreasurer"
    
    country="US"

    start_urls_names = {
        "https://ost.georgia.gov/news-events":"News and Events",
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return 'ministry'
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Mountain"
    
    article_to_date_mapping = {}
    
    def get_articles(self, response) -> list:
       mapping ={}
       article_urls=[]
       entries = response.xpath('//*[contains(@class,"contextual-region")]')
       for entry in entries :
            url = entry.xpath('.//h3/a/@href').get()
            article_urls.append(url)
            date = entry.xpath('.//time/text()').get()
            date_obj = datetime.strptime(date, "%B %d, %Y")
            formatted_date = date_obj.strftime("%Y-%m-%d")
            mapping[url] = formatted_date
       self.article_to_date_mapping.update(mapping)
       return article_urls
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        # title = response.xpath('//h1[@class="heading-lockup__title"]/text()').get()
        # if title is None:
        #     return response.xpath('//h1[@class="heading-lockup__title heading-lockup__title--has-line"]//text()').get().strip()
        title = response.xpath('//h1//text()').get()
        return title
       
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//*[@class="content-page__main"]//p//text()').getall())

    def get_images(self, response, entry=None) :
        return []

    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        article_url = response.url
        article_date = self.article_to_date_mapping.get(article_url, None)
        if article_date:
            return article_date
        else:
            self.logger.error(f"No date found for URL: {article_url}")
            return None
    
    def get_document_urls(self, response, entry=None):
        return response.xpath('//*[contains(@class,"content-page__main-primary")]//span[contains(@class,"document-link__content")]//a//@href').getall()
    
    def get_authors(self, response, entry=None) -> list[str]:
        return []
    
    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        next = "news-events"+ response.xpath('//*[@id="main-content"]//li[contains(@class, "pager__item--next")]/a[contains(text(), "next")]/@href').get().replace("https://ost.georgia.gov", "")
        return response.urljoin(next)