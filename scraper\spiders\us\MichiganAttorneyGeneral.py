from scraper.OCSpider import OCSpider
import scrapy
from typing import List
from scraper.utils.helper import body_normalization
from scraper.middlewares import HeadlessBrowserProxy

class MichiganAttorneyGeneral(OCSpider):
    name = "MichiganAttorneyGeneral"

    country = "US"

    start_urls_names={
        "https://www.michigan.gov/ag/news/press-releases" : "Press Releases",
    }

    def parse_intermediate(self, response):
        hbp = HeadlessBrowserProxy()
        request = scrapy.Request(hbp.get_proxy(response.url, timeout=10000), callback=self.parse
        )
        request.meta['start_url'] = response.request.meta['start_url']
        yield request
        
    charset = "utf-8"
    
    @property
    def language(self):
        return "English"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "US/Eastern"
    
    def get_articles(self, response) -> list:
        urls = response.xpath('//a[@class="content-title-link"]/@href').getall()
        article_urls=[]
        for url in urls:
            article_urls.append(response.urljoin(url).replace("https://proxy.scrapeops.io","https://www.michigan.gov/"))
        return article_urls
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="title__content"]/h1/text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="news-item__section-content"]//p/text()').getall())
    
    def get_images(self, response, entry=None) -> List[str]:
        return []
    
    def date_format(self) -> str:
        return '%B %d, %Y'
    
    def get_date(self, response) -> str:
        return response.xpath('//div[@class="news-item__section-date"]/p/text()').get().strip()
    
    def get_authors(self, response, entry=None) -> list[str]:
        return response.xpath('//p[@class="news-item__section-author"]//text()').get()
    
    def get_document_urls(self, response, entry=None):
        return response.xpath('//div[@class="news-item__section-content"]//p/a/@href').getall()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response, current_page):
       # No next page to scrape
       return None