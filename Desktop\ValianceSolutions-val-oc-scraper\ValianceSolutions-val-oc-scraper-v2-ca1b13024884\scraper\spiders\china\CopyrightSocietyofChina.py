import scrapy
from typing import Optional
from scraper.OCSpider import <PERSON><PERSON><PERSON><PERSON>
from urllib.parse import urljoin
from scraper.middlewares import HeadlessBrowserProxy
from bs4 import BeautifulSoup

class CopyrightSocietyofChina(OCSpider):
    name = "CopyrightSocietyofChina"
    
    start_urls_names = {
        "https://www.csccn.org.cn/contentList?type=associationUpdatesList": "associationUpdatesList",
        "https://www.csccn.org.cn/contentList?type=industryTrendsList": "industryTrendsList",
        "https://www.csccn.org.cn/contentList?type=partyConstruction": "partyConstruction"
    }

    charset = "utf-8"
    
    @property
    def source_type(self) -> str:
        return "CopyrightSociety"
    
    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def parse_intermediate(self, response):
        # Pattern for websites where the landing page alone has JS rendering
        hbp = HeadlessBrowserProxy()
        request = scrapy.Request(hbp.get_proxy(response.url, timeout=10000), callback=self.parse
        )
        request.meta['start_url'] = response.request.meta['start_url']
        yield request
    
    def get_articles(self, response) -> list:
        """
        Extracts titles, dates, and links (if available) for articles on the page.
        """

        # Step 1: Parse the response
        soup = BeautifulSoup(response.text, 'html.parser')

        # Step 2: Extract article elements
        articles = []
        for item in soup.select('div.main-item'):  # Adjust the selector based on your HTML structure
            title = item.select_one('span:nth-child(1)').get_text(strip=True)  # Title from the first span
            date = item.select_one('span:nth-child(2)').get_text(strip=True)   # Date from the second span

            # Extract link if it's hidden in an attribute like data-url
            link = item.get('data-url')  # Replace with the actual attribute or logic for finding the URL

            if link:
                # Resolve relative URLs
                link = urljoin(response.url, link)

            articles.append({'title': title, 'date': date, 'url': link})

        return articles
