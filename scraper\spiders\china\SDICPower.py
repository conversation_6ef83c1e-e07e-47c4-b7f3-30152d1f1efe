from typing import List
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import re

class SDICPower(OCSpider):
    name = "SDICPower"

    start_urls_names = {
        "https://www.sdicpower.com/gtdl/xwzx/gsxw/A110302index_1.htm": "国投电力",
        "https://www.sdicpower.com/gtdl/xwzx/tzkgqy/A110303index_1.htm": "国投电力", 
        "https://www.sdicpower.com/gtdl/xwzx/tzgg/A110304index_1.htm": "国投电力", #only 1 article
        "https://www.sdicpower.com/gtdl/tzzgx/lsgg/A110501index_1.htm": "国投电力", #No articles
        "https://www.sdicpower.com/gtdl/tzzgx/dqbg/A110502index_1.htm": "国投电力", #pdfs
    }

    article_data_map ={}

    charset = "iso-8859-1"

    @property
    def source_type(self) -> str:
        return "SOE"
    
    @property
    def timezone(self) -> str:
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
            articles = []
            for article in response.xpath("//div[@class='news-txt']//h4 | //div[@class='report-wrap']//ul[@class='report-ul clearfix']//li"):
                url = article.xpath(".//a/@href ").get()
                title = article.xpath(".//a//text()").get()
                if url and title:
                    full_url = url
                    title = title.strip()
                    if '.pdf' in full_url.lower() or '.docx' in full_url.lower():
                        pdf = f'https://www.sdicpower.com{full_url}'
                    else:
                        pdf = "None"
                    self.article_data_map[full_url] = {"title": title, "pdf": pdf}
                    articles.append(full_url) 
            article_list= set(articles)
            return list(article_list)
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response, entry=None) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response, entry=None) -> str:
        if ".pdf" in response.url.lower() or ".docx" in response.url.lower():
            return ""
        return body_normalization(response.xpath("//div[@id='BodyLabel']//p//text()").getall())
        
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response, entry=None) -> int:
        if '.pdf' in response.url.lower():
            match = re.search(r"/rootfiles/(\d{4})/(\d{2})/(\d{2})", response.url)
            if match:
                year, month, day = match.groups()
                return f"{year}-{month}-{day}"
            return None
        else:
            date=response.xpath("//div[@class='news-details-l']//span[@class='sj']//text()").get()
            date = date.replace("日期：\n","").strip()
            return date
        
    def get_authors(self, response, entry=None) -> List[str]:
        return []
    
    def get_document_urls(self, response, entry=None) -> list:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("pdf", "")
    
    def get_images(self, response, entry=None) -> List[str]:
        if ".pdf" in response.url.lower() or ".docx" in response.url.lower():
            return ""
        return response.xpath("//div[@id='BodyLabel']//img//@src").getall()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> List[str]:
        next_page= response.xpath("//div[@class='page-number']//div[@class='page-next']//a[contains(text(),'下一页')]//@href").get()
        if next_page:
            return next_page
        return None    