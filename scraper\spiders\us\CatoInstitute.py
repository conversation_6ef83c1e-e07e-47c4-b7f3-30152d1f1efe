from typing import Optional
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
from datetime import datetime

class CatoInstitute(OCSpider):
    name = "CatoInstitute"

    country = "US"

    start_urls_names = {
        "https://www.cato.org/search/category/news-releases": "News Releases",
    }
        
    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350
        },
        "DOWNLOAD_DELAY": 5,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }
    
    HEADLESS_BROWSER_WAIT_TIME = 30000  # 30 seconds wait time
    
    charset = "utf-8"

    @property
    def language(self): 
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "America/New_York"
    
    def get_articles(self, response) -> list:
        return response.xpath('//li[@class="results-list__item"]//h5[@class="mb-3 heading search-result__title"]/a/@href').getall()
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="entry-title"]/text() | //h1[@class="article-title__heading h1 mb-3 spacer--nomargin--last-child heading-top-line-height-compensator"]/text()').get().strip()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="position-relative main-content js-read-depth-tracking-container"]//div[contains(@class, "long-form__wrapper")]//p//text()').getall()) 
        
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return"%m-%d-%Y"
    
    def get_date(self, response) -> str:
        date_only = response.xpath('//div[@class="meta meta--default p-mb-last-child-0"]/text()').getall()[0].strip().split('\n')[0].strip() 
        return datetime.strptime(date_only, "%B %d, %Y").strftime("%m-%d-%Y")

    def get_authors(self, response):
        return response.xpath('normalize-space(//div[@class="authors"]/a/text())').get()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        return response.xpath('//li[@class="pager-item"]/a[span[text()="Next page"]]/@href').get()