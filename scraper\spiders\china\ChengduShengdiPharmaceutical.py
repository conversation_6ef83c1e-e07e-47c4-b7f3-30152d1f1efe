from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ChengduShengdiPharmaceutical(OCSpider):
    name = "ChengduShengdiPharmaceutical"

    start_urls_names = {
        'https://www.hrssd.com.cn/media/NewsType-0.html': "恒瑞医药",
        'https://www.hrssd.com.cn/media/NewsType-1.html': "恒瑞医药",
    }
    
    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "private_enterprise"

    @property
    def timezone(self) -> str:
        return "Asia/Shanghai"

    def get_articles(self, response) -> list:
        return response.xpath('///div[@class="row15 cf list_box"]//ul//li//a[@href]/@href').getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//h3[@class="color1e55a8 paddingtop20"]/text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="padding40 bgfff mo_padding20"]//div[@class="paddingtop30 imgstyle"]//text()').getall())

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response) -> str:
        return response.xpath('//div[@class="padding40 bgfff mo_padding20"]/p[@class="color1e55a8"]/text()').get()

    def get_images(self, response) -> list:
        return response.xpath('//div[@class="padding40 bgfff mo_padding20"]//img/@src').getall()

    def get_authors(self, response) -> list:
        return []
    
    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        #No Pagination
        return None 