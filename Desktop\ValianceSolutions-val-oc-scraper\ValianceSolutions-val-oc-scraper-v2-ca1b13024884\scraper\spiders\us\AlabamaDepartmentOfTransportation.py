# from typing import Optional
# from scraper.OCSpider import OCSpider
# from scraper.utils.helper import body_normalization
# from datetime import datetime
# import re


# class AlabamaDepartmentOfTransportation(OCSpider):
#     name = "AlabamaDepartmentOfTransportation"

#     country = "US"

#     start_urls_names = {
#         "https://aldotnews.com/": "News",
#     }

#     proxy_country = "us"

#     custom_settings = {
#         "DOWNLOADER_MIDDLEWARES": {
#             # Using Geoproxy
#             'scraper.middlewares.GeoProxyMiddleware': 350,
#         },
#         "DOWNLOAD_DELAY": 2,
#     }

#     charset = "utf-8"

#     @property
#     def language(self): 
#         return "English"

#     @property
#     def source_type(self) -> str:
#         return "ministry"

#     @property
#     def timezone(self):
#         return "America/Chicago"

#     def get_articles(self, response) -> list:
#         return response.xpath('//article//h2/a/@href').getall()

#     def get_href(self, entry) -> str:
#         return entry

#     def get_title(self, response) -> str:
#         return response.xpath('//h1[@class="entry-title"]/text()').get()

#     def get_body(self, response) -> str:
#         paragraphs = response.xpath('//div[@class="entry-content prose prose-gray max-w-none prose-a:text-primary"]/p/text()').getall()
#         return body_normalization(paragraphs)

#     def get_images(self, response) -> list:
#         return response.xpath('//figure[contains(@class, "wp-block-image")]//img/@src').getall()

#     def date_format(self) -> str:
#         return "%m-%d-%Y"

#     def get_date(self, response) -> str:
#         date_day = response.xpath('//div[contains(@class, "relative z-0 text-white")]/span/text()').get()
#         date_month = response.xpath('//div[contains(@class, "relative z-0 text-white")]/text()').get()
        
#         if not date_day or not date_month:
#             logger.warning("Missing date on page: %s", response.url)
#             return ""

#         match = re.search(r'/(\d{4})/', response.url)
#         year = match.group(1) if match else "2024"

#         try:
#             date_str = f"{date_day.strip()} {date_month.strip()} {year}"
#             formatted_date = datetime.strptime(date_str, "%d %b %Y").strftime("%m-%d-%Y")
#             return formatted_date
#         except ValueError:
#             logger.error(f"Date parsing failed: '{date_str}' on page {response.url}")
#             return ""

#     def get_authors(self, response):
#         return []

#     def get_page_flag(self) -> bool:
#         return False

#     def get_next_page(self, response) -> Optional[str]:
#         return response.xpath('//a[@class="next"]/@href').get()
from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime
import re
class AlabamaDepartmentOfTransportation(OCSpider):
    name = "AlabamaDepartmentOfTransportation"
    country = "US"
    start_urls_names = {
        "https://aldotnews.com/": "News",
    }
    proxy_country = "us"
    custom_settings = {
        "DOWNLOADER_MIDDLEWARES":
            {
                #Using Geoproxy
                'scraper.middlewares.GeoProxyMiddleware': 350,
            },
        "DOWNLOAD_DELAY": 2,
    }
    charset = "utf-8"
    @property
    def language(self):
        return "English"
    @property
    def source_type(self) -> str:
        return "ministry"
    @property
    def timezone(self):
        return "America/Chicago"
    def get_articles(self, response) -> list:
        return response.xpath('//article//h2/a/@href').getall()
    def get_href(self, entry) -> str:
        return entry
    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="entry-title"]/text()').get()
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="entry-content prose prose-gray max-w-none prose-a:text-primary"]/p/text()').getall())
    def get_images(self, response) -> list:
        return response.xpath('//figure[contains(@class, "wp-block-image")]//img/@src').getall()
    def date_format(self) -> str:
        return "%m-%d-%Y"
    def get_date(self, response) -> str:
        date_day = response.xpath('//div[contains(@class, "relative z-0 text-white")]/span/text()').get()
        date_month = response.xpath('//div[contains(@class, "relative z-0 text-white")]/text()').get()
        url = response.url # Extract the year from the URL
        match = re.search(r'/(\d{4})/', url)
        year = match.group(1)
        date_str = f"{date_day.strip()} {date_month.strip()} {year}"
        formatted_date = datetime.strptime(date_str, "%d %b %Y").strftime("%m-%d-%Y")
        return formatted_date
    def get_authors(self, response):
        return []
    def get_page_flag(self) -> bool:
        return False
    def get_next_page(self, response) -> Optional[str]:
        next_page = response.xpath('//a[@class="next"]/@href').get()
        if not next_page:
           return None
        else:
           return next_page