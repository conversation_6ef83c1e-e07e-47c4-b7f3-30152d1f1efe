from typing import Optional
from scraper.OCSpider import <PERSON><PERSON><PERSON>er
from scraper.utils.helper import body_normalization
import urllib.parse
import scrapy
import json
from urllib.parse import urlencode

class Kingfa(OCSpider):
    name = "Kingfa"

    country = "US"

    start_urls_names = {
        "https://www.kingfa.com/portal/list/index/id/5.html": "",
    }
    
    api_start_urls = {
        "https://www.kingfa.com/portal/list/index/id/5.html": {
            "url": "https://www.kingfa.com/portal/list/listajax.html",
            "payload": {
                       "cid":"5",
                      "start": "5",
                      "size":"6",
                      "list_tpl": "list_news",
            }
        }
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"
    
    @property
    def timezone(self):
        return "Asia/Shanghai"

    headers = {
    "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 10_3 like Mac OS X) AppleWebKit/602.1.50 (KHTML, like Gecko) Mobile/14E5239e MicroMessenger/7.0.1(0x17000121) NetType/WIFI Language/zh_CN",
    "Referer": "https://mp.weixin.qq.com/",
}
    
    
    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_data = self.api_start_urls.get(start_url)

        if not api_data:
            self.logger.warning(f"No API data found for {start_url}")
            return

        try:
            json_response = response.json()
        except json.JSONDecodeError:
            # Received HTML instead of JSON – likely the first page
            self.logger.info("Received HTML. Redirecting to API request.")

            payload = api_data["payload"].copy()
            api_url = api_data["url"]
            query_string = urlencode(payload)
            full_url = f"{api_url}?{query_string}"

            yield scrapy.Request(
                url=full_url,
                method="GET",
                headers=self.headers,
                callback=self.parse_intermediate,
                meta={
                    "start_url": start_url,
                    "api_url": api_url,
                    "payload": payload,
                    "current_start": int(payload.get("start", 0))
                }
            )
            return

        # At this point, the response is valid JSON
        html_data = json_response.get("data", "")
        if not html_data.strip():
            self.logger.warning("API returned empty 'data' field.")
            return

        selector = scrapy.Selector(text=html_data)
        article_links = selector.xpath('//li/a/@href').getall()

        for href in article_links:
            if href and "mp.weixin.qq.com" in href:
                yield scrapy.Request(
                    url=href,
                    method="GET",
                    headers=self.headers,
                    callback=self.parse,
                    meta={
                        "start_url": start_url,
                        "current_start": response.meta.get("current_start", 0),
                        "payload": response.meta.get("payload", {})
                    }
                )




    def get_articles(self, response) -> list:
        links = response.xpath('//div[@class="newslist"]//ul//li//a/@href').getall()
        filtered_links = [link for link in links if "mp.weixin.qq.com" in link]
        if not filtered_links:
            self.logger.warning("No article links found on page.")
        return filtered_links
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[@id="activity-name"]/text()').get(default="").strip()
        
    def get_body(self, response) -> str:
        paragraphs = response.xpath('//div[@id="js_content"]//text()').getall()
        return body_normalization(paragraphs) 
        
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return"%m/%d/%Y"
    
    def get_date(self, response) -> str:
        return response.xpath('//em[@id="publish_time"]/text()').get(default="").strip()
        
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response, current_page: Optional[int] = 1) -> Optional[int]:
        next_page = int(current_page) + 1
        return next_page
    
    def go_to_next_page(self, response, start_url, current_start: Optional[int] = 0):
        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            return

        api_url = api_data["url"]
        payload = response.meta.get("payload", {}).copy()

        json_response = response.json()
        size = int(json_response.get("size", 6))
        more_data = json_response.get("more_data", 0)

        if more_data:
            # Calculate next start
            next_start = current_start + size

            # Update payload
            payload["start"] = next_start
            payload["size"] = size  # Optional, depends if server requires it

            # Construct next page URL
            full_api_url = f"{api_url}?start={payload['start']}&size={payload['size']}"

            yield scrapy.Request(
                url=full_api_url,
                method="GET",
                headers=self.headers,
                callback=self.parse_intermediate,
                meta={
                    "start_url": start_url,
                    "api_url": api_url,
                    "payload": payload,
                    "current_start": next_start
                }
            )