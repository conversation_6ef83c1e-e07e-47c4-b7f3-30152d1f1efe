from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class InternetSocietyOfChina(OCSpider):
    name = "InternetSocietyOfChina"

    start_urls_names = {
        "https://www.isc.org.cn//category/7424.html" : "支部活动",
        "https://www.isc.org.cn//category/7329.html" : "协会动态",
        "https://www.isc.org.cn//category/7330.html" : "通知公告",
        "https://www.isc.org.cn//category/7347.html" : "省市协会动态",
        "https://www.isc.org.cn//category/7349.html" : "行业资讯"
    }
    
    include_rules = [r'^https://www\.isc\.org\.cn/.*']

    charset = "utf-8"
    
    @property
    def source_type(self) -> str:
        return "IndustryAssociation"
    
    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        articles = response.xpath('//div[@class="content"]//ul/li//a[h3]/@href').getall()
        return articles
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        title = response.xpath('//div[@class="content"]//h1[@class="new-tit"]//text()').get()
        return title
    
    def date_format(self) -> str:
        return '%Y年%m月%d日 %H:%M'
    
    def get_date(self, response) -> str:
        date = response.xpath('//div[@class="content"]//p[@class="new-tips"]//text()').re_first(r'^\d{4}年\d{2}月\d{2}日 \d{2}:\d{2}')
        return date
    
    def get_authors(self, response):
        return []
    
    def get_body(self,response) -> str:
        body = response.xpath('//div[@class="new-cont"]//p//text()').getall()
        return body_normalization(body)
    
    def get_images(self, response) -> list:
        return response.xpath('//div[@class="new-cont"]//p//img/@src').getall()
    
    def get_document_urls(self, response, entry=None) -> list:
        documents = response.xpath('//div[@class="new-cont"]//p//a/@href').getall()
        return documents
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        next_page = response.xpath('//div[@class="pages"]//a[contains(text(), "下一页")]/@href').get()
        if next_page:
            next_page_url = response.urljoin(next_page)
            return next_page_url
        return None