from typing import Union
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class Jon<PERSON><PERSON>iTech(OCSpider):
    name = '<PERSON><PERSON>HiTech'

    start_urls_names = {
        'http://www.jonjee.com/news/notice': '中炬高新',
        'http://www.jonjee.com/news/group': '中炬高新',
        'http://www.jonjee.com/investor/abstract': '中炬高新', # No Articles
        'http://www.jonjee.com/investor/notice': '中炬高新', # PDF
        'http://www.jonjee.com/investor/report': '中炬高新' # PDF
    }
    
    charset = "iso-8859-1"
    
    article_data_map = {} 

    @property
    def source_type(self) -> str:
        return "private_enterprise"

    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        articles =  response.xpath('//ul[@class= "center new-a-item-box"]//li/a/@href').getall()
        if articles:
            return articles
        else:
            articles = []
            for article in response.xpath('//div[@class="notice-wrapper-list clearFloat"]/a'):
                url = article.xpath('./@href').get()
                title = article.xpath('.//div[@class="notice-item-title justify-align"]/span/text()').get()
                year = article.xpath('.//div[@class="notice-date"]/div/text()').get()
                month_day = article.xpath('.//div[@class="notice-date"]/b/text()').get()
                if year and month_day:
                    date = f"{year}.{month_day.strip()}"  # e.g., "2025-05.20"
                else:
                    date = None  
                if url and title and date:
                    full_url = url
                    title = title.strip()
                    if '.pdf' in full_url.lower():
                        pdf = full_url
                    else:
                        pdf = "None"
                    clean_date = date.strip()
                    self.article_data_map[full_url] = {"title": title, "date": clean_date, "pdf": pdf}
                    articles.append(full_url) 
            return articles
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self,response) -> str:
        if ".pdf" in response.url.lower():
            return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
        return response.xpath('//div[@class= "news-des-content"]//h5//text()').get()
    
    def get_body(self, response) -> str:
        if ".pdf" in response.url.lower():
            return ""
        return body_normalization(response.xpath('//div[@class= "news-des-content"]//div[@class="news-media-txt"]//p//text()').getall())
    
    def date_format(self) -> str:
        return "%Y.%m.%d"
    
    def get_date(self, response) -> str:
        if ".pdf" in response.url.lower():
            return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        return response.xpath('//div[@class= "news-des-content"]//div[@class="news-media-time"]//text()').get()
    
    def get_images(self, response) -> list[str]:
        if ".pdf" in response.url.lower():
            return ""
        return response.xpath('//div[@class= "news-des-content"]//div[@class="news-media-txt"]//p/img/@src').getall()
    
    def get_authors(self, response):
        return []
    
    def get_document_urls(self, response, entry=None):
        pdf_url = self.article_data_map.get(response.request.meta.get('entry'), {}).get("pdf")
        return [pdf_url] if pdf_url and pdf_url != "None" else []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Union[None, str]:
        next_page = response.xpath('//ul[@class="pagination"]/li/a[normalize-space(text())="下一页"]/@href').get()
        return next_page