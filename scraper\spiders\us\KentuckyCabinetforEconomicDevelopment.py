from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime

class KentuckyCabinetForEconomicDevelopment(OCSpider):
    name = 'KentuckyCabinetForEconomicDevelopment'

    country = "US"

    start_urls_names = {
        "https://ced.ky.gov/NewsRoom/News_Releases/": "News Releases",
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "US/Eastern"

    @property
    def language(self):
        return "English"

    def get_articles(self, response) -> list:
        return response.xpath("//p//a//@href").getall()    
    
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//h1//span[@class='newstitle']//text()").get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='container']//div[@class='row']//p//text() | //div[@class='container']//div[@class='row']//ul//li//text()").getall())

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response) -> str:
        date = response.xpath("//div[@class='col-12 col-md-4']//span[@class='art_date']//text()").get().strip()
        for fmt in ["%Y-%m-%d %H:%M:%S", "%Y-%m-%d"]  :
            try:
                dt =datetime.strptime(date, fmt)
                return dt.strftime("%Y-%m-%d")
            except:
                return None

    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        next_page = response.xpath("//a[@class='btn'][contains(text(),'More')]//@href").get()
        if next_page:
            return next_page
        else:
            return None