import logging
from typing import Optional
import scrapy
from scraper.utils.helper import body_normalization
from scraper.OCSpider import OCSpider

class NewMexicoAttorneyGeneral(OCSpider):
    name = 'NewMexicoAttorneyGeneral'

    country = "US"

    start_urls_names = {
        "https://nmdoj.gov/press-releases/": "Press Releases"
    }
    
    HEADLESS_BROWSER_WAIT_TIME = 30000 
    
    custom_settings = {
		"DOWNLOADER_MIDDLEWARES" : {
			'scraper.middlewares.HeadlessBrowserProxy': 350,
		},
		"DOWNLOAD_DELAY" : 6,
	}
    
    charset = "utf-8"
    
    article_data_map ={}

    @property
    def source_type(self) -> str:
        return 'ministry'
    
    @property
    def timezone(self):
        return "US/Mountain"
    
    @property
    def language(self):
        return "English"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@class= "elementor-loop-container elementor-grid"]//div//section/a/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="elementor-widget-container"]//p//text()').getall())
    
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%B %d, %Y"
    
    def get_date(self, response) -> str:
        return response.xpath('//span[@class= "elementor-icon-list-text elementor-post-info__item elementor-post-info__item--type-date"]//time//text()').re_first(r"([A-Za-z]+ \d{1,2}, \d{4})")
    
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response, current_page):
        if response.status != 200:
            self.logger.error(f"Error {response.status}: Not incrementing page.")
            return None
        return str(int(current_page) + 1)
    
    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        next_page = self.get_next_page(response, current_page)
        url=f"https://nmdoj.gov/press-releases/{next_page}/"
        if next_page:
            yield scrapy.Request(
                url=url,
                method='GET',
                callback=self.parse, 
                dont_filter=True,
                 meta={
                        'current_page': next_page, 
                        'start_url': start_url,
                    }
            )
        else:
            logging.info("No more pages to fetch.")
            yield None