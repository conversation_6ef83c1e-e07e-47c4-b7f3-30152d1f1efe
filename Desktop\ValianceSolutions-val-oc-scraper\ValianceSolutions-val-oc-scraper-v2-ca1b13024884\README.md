## Local Environment Setup

- Setup virtualenvironment using pyenv or any other tool. Please note use Python >= 3.9.9

- Activate virtualenv and Install requirements.txt 
    `pip install -r requirements.txt` 

- Open scraper/settings.py. And comment out all pipelines and enable only dummy pipeline. 

activate virtualenv `venv/scripts/activate` and run `scrapy crawl <spider_name>` to test if it works. 

- To run any spider - 

    `scrapy crawl <spider_name>`

    E.g. 
    
    `scrapy crawl StateCouncil`


## How to add a spider

There are two example spiders in the codebase

1. CCTV - inherits from a base class OfficialLineSpider
2. StateCouncil - inherits from a base class OCSpider

