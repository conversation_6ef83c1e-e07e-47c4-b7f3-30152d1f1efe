import scrapy
from scrapy.http import Request
from ...items import *
from ...utils.helper import body_normalization

class MCCNews1(scrapy.Spider):
    name = 'mcc_news1'
    allowed_domains = ['mcc.com.cn']
    start_urls = [
        'http://www.mcc.com.cn/xwzx_7388/lddt/',
        'http://www.mcc.com.cn/tzzgx_7555/yjtj/'
    ]

    source_type = 'ChinaMinmetalsCorporationLTD'
    timezone = 'Asia/Shanghai'

    def parse(self, response):
        for article in self.get_articles(response):
            yield Request(
                url=self.get_href(article, response),
                callback=self.parse_article,
                meta={'page_url': response.url}
            )

        yield from self.get_next_page(response)

    def parse_article(self, response):
        item = NewsItem()
        item['title'] = self.get_title(response)
        item['body'] = self.get_body(response)
        item['date'] = self.get_date(response)
        item['images'] = self.get_images(response)
        item['source_url'] = response.url
        item['page_url'] = response.meta.get('page_url')
        item['authors'] = self.get_authors(response)
        item['document_urls'] = self.get_document_urls(response)
        return item

    def get_articles(self, response):
        return response.xpath('//div[contains(@class,"news_list")]//li')

    def get_href(self, article, response):
        return response.urljoin(article.xpath('.//a/@href').get())

    def get_title(self, response):
        return response.xpath('//div[@class="news_title"]/text()').get('').strip()

    def get_body(self, response):
        body = response.xpath('//div[contains(@class,"news_cont")]//text()').getall()
        return body_normalization(' '.join(body))

    def get_images(self, response):
        return [response.urljoin(img) for img in 
                response.xpath('//div[contains(@class,"news_cont")]//img/@src').getall()]

    def get_authors(self, response):
        return []

    def get_document_urls(self, response):
        return [response.urljoin(doc) for doc in
                response.xpath('//div[contains(@class,"news_cont")]//a[contains(@href,".pdf")]/@href').getall()]

    def get_date(self, response):
        return response.xpath('//div[@class="news_time"]/text()').re_first(r'\d{4}-\d{2}-\d{2}')

    date_format = '%Y-%m-%d'

    def get_next_page(self, response):
        next_page = response.xpath('//a[contains(text(),"Next") or contains(text(),"»")]/@href').get()
        if next_page:
            yield Request(response.urljoin(next_page), callback=self.parse)

    @property
    def get_page_flag(self) -> bool:
        return True