from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ChinaTrademarkAssociation(OCSpider):
    name = "ChinaTrademarkAssociation"

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350,
        },
        "DOWNLOAD_DELAY": 4,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    HEADLESS_BROWSER_WAIT_TIME = 30000

    # Only including the the URLs starting with "https://www.cta.org.cn/"
    include_rules = [r'.*https://www\.cta\.org\.cn/.*']

    start_urls_names = {
            "https://www.cta.org.cn/ywdt/": "要闻动态",
            "https://www.cta.org.cn/ppyj/gzdt/":"工作动态",
            "https://www.cta.org.cn/ppyj/llyj/": " 理论研究",
            "https://www.cta.org.cn/ldjh/": "商标资讯 "
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_articles(self, response) -> list:
        # Check URL and apply the corresponding XPath
        if response.url.startswith("https://www.cta.org.cn/ywdt/") or response.url.startswith("https://www.cta.org.cn/ppyj/llyj/"):
            xpath_expr = '//div[@class="NewListmainbox"]//ul/li[@class="NewListmainLeft01right01"]/a/@href'
        elif response.url.startswith("https://www.cta.org.cn/ppyj/gzdt/") or response.url.startswith("https://www.cta.org.cn/ldjh/"):
            xpath_expr = '//div[@class="list_box"]/ul/li[@class="indextextLeft02Rlist02"]/a/@href'
        else:
            return []  
        # Extract articles based on the determined XPath
        articles = response.xpath(xpath_expr).getall()
        return [response.urljoin(link) for link in articles]

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//div[@class="contentbox"]//div[@class="contentbox01"]//div[@class="content01"]/text()').get()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="content04"]//text()').getall())

    def get_images(self, response) -> list:
        return response.xpath('//div[@class="content04"]//img/@src').getall()

    def date_format(self) -> str:
        return "%Y年%m月%d日"
    
    def get_date(self, response) -> str:
        return response.xpath('//div[@class="content02"]/text()').re_first(r"\d{4}年\d{1,2}月\d{1,2}日")  

    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        try:
            next_page = response.xpath('//li[(@class = "NewListmainLeftfy02")]/a[contains(text(), "下一页")]/@href').get()
            if next_page:
                next_page_url = response.urljoin(next_page)
                return next_page_url
            else:
                return None
        except Exception as e:
            self.logger.error(f"Error extracting next page link from page {response.url}: {e}")
            return None