from typing import List
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class MetallurgicalCorporationofChinaLimited(OCSpider):
    name = "MetallurgicalCorporationofChinaLimited"

    start_urls_names = {
        'http://www.mcc.com.cn/xwzx_7388/lddt/',
        'http://www.mcc.com.cn/xxgk_7595/qygg/',
        'http://www.mcc.com.cn/tzzgx_7555/yjtj/',
        'http://www.mcc.com.cn/tzzgx_7555/yjbg/',
        'http://www.mcc.com.cn/tzzgx_7555/aggg/',
        'http://www.mcc.com.cn/tzzgx_7555/hggg/',
        'http://www.mcc.com.cn/tzzgx_7555/gddh/',
    }

    charset = "utf-8"

    current_page =2

    @property
    def source_type(self) -> str:
        return "Finance Department "
    
    @property
    def timezone(self) -> str:
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
     return response.xpath('//div[@class="sortlist"]//a//@href').getall()

    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response, entry=None) -> str:
        return response.xpath('//div[@class="xl_t"]//text()').get().strip()
    
    def get_body(self, response, entry=None) -> str:
        return body_normalization(response.xpath('//div[@class="main qs_clear xl"]//p//text()').getall())
    
    def get_date(self, response) -> str:
        date = response.xpath("//div[@class='xl_xx qs_clear]//span//text()").get()
        return date
    
    
    def get_authors(self, response, entry=None) -> List[str]:
        return []
    
    def get_document_urls(self, response, entry=None) -> list:
        return []
    
    def get_images(self, response, entry=None) -> List[str]:
        return response.xpath('//div[@class="fck_uploadpics"]//img//@src').getall()
    
    def get_page_flag(self) -> bool:
        return False
    
    # def get_next_page(self, response) -> List[str]:
    #     if self.current_page :
    #         next_page = f"https://news.ch.com/?pageIndex={self.current_page}"
    #         self.current_page += 1
    #         return next_page
    #     return None    
    def get_next_page(self, response) -> List[str]:
        next_page= response.xpath("//div[@class='page']//a[contains(text(),'下一页')]//@href").get()
        if next_page:
            return next_page
        return None
