from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
from datetime import datetime

class MassachusettsExecutiveOfficeOfHousingAndEconomicDevelopment(OCSpider):
    name = 'MassachusettsExecutiveOfficeOfHousingAndEconomicDevelopment'
    
    country = "US"

    start_urls_names = {
        'https://www.mass.gov/orgs/executive-office-of-economic-development/news': 'Press Release'
    }
    
    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350
        },
        "DOWNLOAD_DELAY": 2,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }
    
    HEADLESS_BROWSER_WAIT_TIME = 10000  # 10 seconds wait time
    
    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Central"
    
    def get_articles(self, response) -> list:
        return response.xpath('//h2[contains(@class, "ma__press-teaser__title")]//a/@href').getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return " ".join(response.xpath("//h1[@class='ma__page-header__title']//text()").getall()).strip()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[normalize-space(@class)='ma__rich-text']//p//text()").getall())

    def get_images(self, response) -> list[str]:
        return []
    
    def date_format(self) -> str:
        return "%m/%d/%Y"

    def get_date(self, response) -> str:
        raw_date = response.xpath('//div[@class="ma__press-status__date"]/text()').get().strip()
        parsed_date = datetime.strptime(raw_date, "%m/%d/%Y")
        return parsed_date.strftime(self.date_format())
  
    def get_authors(self, response):
        return ""
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        next_page = response.xpath("//a[contains(@class, 'ma__pagination__next')]/@href").get()
        return response.urljoin(next_page) if next_page else None