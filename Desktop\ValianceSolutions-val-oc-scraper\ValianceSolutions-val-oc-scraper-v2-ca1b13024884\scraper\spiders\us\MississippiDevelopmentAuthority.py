from datetime import datetime
from scraper.OCSpider import <PERSON>CSpid<PERSON>
from scraper.utils.helper import body_normalization
import scrapy
import json
from typing import Optional

class MississippiDevelopmentAuthority(OCSpider):

    name = "MississippiDevelopmentAuthority"

    country="US"

    start_urls_names = {
        "https://mississippi.org/news/":"News Releases",
    }

    api_start_urls = {
    'https://mississippi.org/news/': {
        "url": "https://mississippi.org/wp-json/mda/v1/news?per_page={per_page}&page={current_page}&offset={offset}&industries=&tags=",
        "payload": {
            "per_page": "12",
            "page": "1",
            "offset": "0"
            }
        }
    }

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        current_page = response.meta.get("current_page", 1)
        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            return
        per_page = api_data["payload"]["per_page"]
        offset = str(int(per_page) * (current_page - 1))
        api_url = api_data["url"].format(per_page=per_page, current_page=current_page, offset=offset)
        yield scrapy.Request(
            url=api_url,
            callback=self.parse,
            headers={"content-type": "application/json"},
            dont_filter=True,
            meta={
                "start_url": start_url,
                "current_page": current_page,
                "per_page": per_page,
                "offset": offset,
            },
        )

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return 'ministry'
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Central"
    
    def get_articles(self, response) -> list:
        data = json.loads(response.text)
        return [item.get("link") for item in data if "link" in item]

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[contains(@class, "c-headline")]/text()').get()
  
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//p//text()').getall())

    def date_format(self) -> str:
        return "%A,%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date = response.xpath('//time[@class="c-date"]/text()').get()
        date_obj = datetime.strptime(date, "%B %d, %Y")
        return date_obj.strftime("%A,%Y-%m-%d")

    def get_images(self, response, entry=None) :
        return []
    
    def get_authors(self, response, entry=None) -> list[str]:
        return []
    
    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response, current_page) -> Optional[str]:
        if response.status != 200:
            self.logger.error(f"Error {response.status}: Not incrementing page.")
            return None
        try:
            data = json.loads(response.text)
        except json.JSONDecodeError:
            return None
        if not data.get("items"):
            self.logger.info("No more articles available. Stopping pagination.")
            return None
        next_page = str(int(current_page) + 1)
        return next_page

    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            self.logger.info("API data not found in meta.")
            return None
        per_page = int(api_data["payload"]["per_page"])
        next_page = int(current_page) + 1
        offset = per_page * (next_page - 1)
        api_url = api_data["url"].format(per_page=per_page, current_page=next_page, offset=offset)
        yield scrapy.Request(
            url=api_url,
            method='GET',
            callback=self.parse,
            dont_filter=True,
            meta={
                "api_url": api_url,
                "current_page": next_page,
                "start_url": start_url,
                "per_page": per_page,
                "offset": offset,
            }
        )