import json
import re
from scraper.OCSpider import <PERSON>CSpider
from scraper.middlewares import HeadlessBrowserProxy
import scrapy
from typing import List
from dotenv import load_dotenv
from scraper.utils.helper import body_normalization
from typing import Optional
import logging
load_dotenv()

class ChinaSemiconductorIndustryAssociation(OCSpider):
    name = "ChinaSemiconductorIndustryAssociation"

    hbp = HeadlessBrowserProxy()
    
    start_urls_names = {
        "https://web.csia.net.cn/xhgg": "协会公告", 
        "https://web.csia.net.cn/dfxt": "地方协同", 
        "https://web.csia.net.cn/hydt": "会员动态" 
    }

    charset = "utf-8"

    custom_settings = {
        "DOWNLOAD_TIMEOUT": 500,
    }

    api_start_urls = {
        "https://web.csia.net.cn/xhgg": {
            "url": "https://web.csia.net.cn/Designer/Common/GetData",
            "payload": {
                "dataType": "news",
                "key": "",
                "pageIndex": "0",
                "pageSize": "10",
                "selectCategory": "343767",
                "selectId": "",
                "dateFormater": "yyyy-MM-dd",
                "orderByField": "createtime",
                "orderByType": "desc",
                "templateId": "0",
                "postData": "",
                "es": "False",
                "setTop": "True",
                "__RequestVerificationToken": "fOmq9TRuzKdz7VHuHs07vF4HuyQK7JEAIxzW-L8oKKpau6UpMw10MAVnLAnKs1gozmFpPoSu9vmYBs3VZyavgCtMyB9Fofc9vteOgsQV0N01"
            }
        },
        "https://web.csia.net.cn/dfxt": {
            "url": "https://web.csia.net.cn/Designer/Common/GetData",
            "payload": {
                "dataType": "news",
                "key": "",
                "pageIndex": "0",
                "pageSize": "10",
                "selectCategory": "497143",
                "selectId": "",
                "dateFormater": "yyyy-MM-dd",
                "orderByField": "createtime",
                "orderByType": "desc",
                "templateId": "0",
                "postData": "",
                "es": "False",
                "setTop": "True",
                "__RequestVerificationToken": "fOmq9TRuzKdz7VHuHs07vF4HuyQK7JEAIxzW-L8oKKpau6UpMw10MAVnLAnKs1gozmFpPoSu9vmYBs3VZyavgCtMyB9Fofc9vteOgsQV0N01"
            }
        },
        "https://web.csia.net.cn/hydt": {
            "url": "https://web.csia.net.cn/Designer/Common/GetData",
            "payload": {
                "dataType": "news",
                "key": "",
                "pageIndex": "0",
                "pageSize": "10",
                "selectCategory": "357026",
                "selectId": "",
                "dateFormater": "yyyy-MM-dd",
                "orderByField": "createtime",
                "orderByType": "desc",
                "templateId": "0",
                "postData": "",
                "es": "False",
                "setTop": "True",
                "__RequestVerificationToken": "fOmq9TRuzKdz7VHuHs07vF4HuyQK7JEAIxzW-L8oKKpau6UpMw10MAVnLAnKs1gozmFpPoSu9vmYBs3VZyavgCtMyB9Fofc9vteOgsQV0N01"
            }
        },
    }

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            self.logger.error(f"No API configuration found for start_url: {start_url}")
            return
        api_url = api_data["url"]
        payload = api_data["payload"]
        payload["pageIndex"] = payload.get("pageIndex")
        yield scrapy.FormRequest(
            url = api_url,
            method = "POST",
            headers={
                "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
            },
            dont_filter=True,
            formdata = payload,
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_data["url"],
                "payload": payload,
                "current_page": payload["pageIndex"],
            },
        )    

    @property
    def source_type(self) -> str:
        return 'IndustryAssociation'

    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        try:
            data = response.json()
            articles = data.get("Data", [])
            article_urls = [
                self.construct_article_url(article)
                for article in articles
                if article
            ]
            return article_urls
        except json.JSONDecodeError as e:
            self.logger.error(f"Error decoding JSON: {e}")
            return []
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//*[contains(@class,'w-title')]//text()").get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//*[contains(@class,'w-detail')]//text()").getall())

    def get_images(self, response, entry=None) -> List[str]:
        return [response.urljoin(img_url) for img_url in response.xpath("//*[contains(@class,'w-detail')]//img//@src").getall()]

    def get_authors(self, response, entry=None) -> list[str]:
        return []

    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date = response.xpath("//*[contains(@class,'w-createtime-date')]//text()").get()
        return re.search(r"\d{4}-\d{2}-\d{2}", date).group()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response, current_page):
        total_pages = int(response.json().get('TotalPages', 0))
        return str(int(current_page) + 1) if int(current_page) < total_pages else None

    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        api_url = response.meta.get("api_url")
        if not api_url:
            logging.error("API URL not found in meta data.")
            return
        next_page = self.get_next_page(response, current_page)
        if next_page:
            payload = response.meta.get('payload')
            payload['pageIndex'] = next_page
            yield scrapy.http.FormRequest(
                url=api_url,
                method='POST',
                formdata=payload,
                headers={
                    "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
                },
                callback=self.parse_intermediate,  # callback parse_intermediate
                dont_filter=True,
                meta={
                        "api_url": api_url,
                        'current_page': next_page, 
                        'start_url': start_url,
                        'payload': payload,
                    }
            )
        else:
            logging.info("No more pages to fetch.")
            yield None

    def construct_article_url(self, article):
        article_id = article.get('Id')
        if article_id:
            return self.hbp.get_proxy(f"https://web.csia.net.cn/newsinfo/{article_id}.html",timeout=30000)
        return None        