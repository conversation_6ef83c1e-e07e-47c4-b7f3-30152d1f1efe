from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime 
from urllib.parse import urlparse, parse_qs
from typing import Optional

class WisconsinAttorneyGeneral(OCSpider):
    name = "WisconsinAttorneyGeneral"
    
    country = "US"

    start_urls_names = {
        "https://www.doj.state.wi.us/news-releases": "Newsroom",
    }

    proxy_country = "us"

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES":
            {
                # for using geo targeted proxy, add this middleware
                'scraper.middlewares.GeoProxyMiddleware': 350,
            },
        "DOWNLOAD_DELAY": 6,
    }
    
    charset = "utf-8"
    
    @property
    def language(self): 
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "America/Chicago"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="views-field views-field-title"]/span[@class="field-content"]/a/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="page-title"]/text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="field-item even"]//p//text()').getall()) 
        
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return"%m-%d-%Y"
    
    def get_date(self, response) -> str:
        date_str = response.xpath('//div[@class="field-item even"]/span[@class="date-display-single"]/text()').get()
        date_obj = datetime.strptime(date_str.strip(), "%b %d %Y")
        return date_obj.strftime("%m-%d-%Y")
            
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return True
    
    def get_next_page(self, response) -> Optional[str]:
        next_page = response.xpath('//li[@class="pager-item"]/a/@href').get()
        last_page = response.xpath('//li[@class="pager-last last"]/a/@href').get()
        current_page = int(parse_qs(urlparse(response.url).query).get('page', [1])[0])
        last_page_num = int(parse_qs(urlparse(last_page).query).get('page', [None])[0]) if last_page else None
        if last_page_num is None or current_page < last_page_num:
           return response.urljoin(next_page)
        else:
            return None 