from scraper.utils.helper import body_normalization
from scraper.OCSpider import <PERSON><PERSON>pid<PERSON>
from datetime import datetime

class MissouriDepartmentOfEconomicDevelopment(OCSpider):
    name = 'MissouriDepartmentOfEconomicDevelopment'      
    
    country = "US" 
    
    start_urls_names = {
        'https://ded.mo.gov/press-room/archive':'News'
    }

    proxy_country = "us"

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES":
            {
                'scraper.middlewares.GeoProxyMiddleware': 350,
            },
        "DOWNLOAD_DELAY": 1,
    }

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "US/Central"
    
    @property
    def language(self):
        return "English"

    def get_articles(self, response) -> list:
        return response.xpath("//div[@class='views-row']//a/@href").getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//h1[@class='page-title']/text()").get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='node__content']//p//text()").getall())

    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response) -> str:
        date_text = response.xpath("//div[contains(@class, 'field--name-field-news-date')]//time/text()").get()
        if not date_text:
            return None
        try:
            date_obj = datetime.strptime(date_text.strip(), "%B %d, %Y")
            return date_obj.strftime(self.date_format())
        except ValueError:
            return None
        
    def get_authors(self, response):
        return ""
        
    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        next_page = response.xpath("//li[@class='pager__item pager__item--next']//a/@href").get()
        if next_page:
            return next_page
        else:
            return None