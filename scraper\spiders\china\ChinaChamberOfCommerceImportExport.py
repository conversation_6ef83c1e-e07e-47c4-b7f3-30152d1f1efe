from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from bs4 import BeautifulSoup
import logging
import re
from typing import Union
import scrapy

class ChinaChamberOfCommerceImportExport(OCSpider):
    name = 'ChinaChamberOfCommerceImportExport'
    
    custom_settings = {
        "DOWNLOAD_DELAY": 4,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }
    
    HEADLESS_BROWSER_WAIT_TIME = 30000
    
    charset = "gb2312"
    
    start_urls_names = {
        'http://www.cccla.org.cn/IssuanceMore.aspx?issuanceToID=3&newsTypeID=6' : '行业热点',
        'http://www.cccla.org.cn/IssuanceMore.aspx?m=Associator_NewsList&issuanceToID=2&newsTypeID=0' : '新闻动态',
        'http://www.cccla.org.cn/IssuanceMore.aspx?m=Associator_NewsList&issuanceToID=4' : '商会工作',
        'http://www.cccla.org.cn/IssuanceMore.aspx?m=Associator_NewsList&issuanceToID=18&newsTypeID=46' : '展会资讯',
        'http://www.cccla.org.cn/IssuanceMore.aspx?issuanceToID=23&newsTypeID=13':'企业风采'
    }

    api_start_url = {
        'http://www.cccla.org.cn/IssuanceMore.aspx?issuanceToID=3&newsTypeID=6': {
            'url': 'http://www.cccla.org.cn/IssuanceMore.aspx?issuanceToID=3&newsTypeID=6',
            'payload': {
                '__VIEWSTATE' : '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', 
                '__VIEWSTATEGENERATOR' : '59481E4C',
                '__SCROLLPOSITIONX' : '0',
                '__SCROLLPOSITIONY' : '396.79998779296875',
                '__EVENTTARGET': 'ctl00$cphContent$FrontPager1$Pager1',
                '__EVENTARGUMENT': '1',
                'ctl00$UCTop$txtID: ': '',
                'ctl00$UCTop$txtCode': '',
                'ctl00$UCBottom1$dwnMoreLink': '-1' 
            }
        },
        'http://www.cccla.org.cn/IssuanceMore.aspx?m=Associator_NewsList&issuanceToID=2&newsTypeID=0': {
            'url': 'http://www.cccla.org.cn/IssuanceMore.aspx?m=Associator_NewsList&issuanceToID=2&newsTypeID=0',
            'payload': {
                '__VIEWSTATE' : '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',
                '__VIEWSTATEGENERATOR' : '59481E4C',
                '__SCROLLPOSITIONX' : '0',
                '__SCROLLPOSITIONY' : '318.3999938964844',
                '__EVENTTARGET': 'ctl00$cphContent$FrontPager1$Pager1',
                '__EVENTARGUMENT': '1',
                'ctl00$UCTop$txtID: ': '',
                'ctl00$UCTop$txtCode': '',
                'ctl00$UCBottom1$dwnMoreLink': '-1' 
            }
        },
        'http://www.cccla.org.cn/IssuanceMore.aspx?m=Associator_NewsList&issuanceToID=4': {
            'url': 'http://www.cccla.org.cn/IssuanceMore.aspx?m=Associator_NewsList&issuanceToID=4',
            'payload': {
                '__VIEWSTATE' : '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',
                '__VIEWSTATEGENERATOR' : '59481E4C',
                '__SCROLLPOSITIONX' : '0',
                '__SCROLLPOSITIONY' : '412',
                '__EVENTTARGET': 'ctl00$cphContent$FrontPager1$Pager1',
                '__EVENTARGUMENT': '1',
                'ctl00$UCTop$txtID: ': '',
                'ctl00$UCTop$txtCode': '',
                'ctl00$UCBottom1$dwnMoreLink': '-1' 
            }
        },
        'http://www.cccla.org.cn/IssuanceMore.aspx?m=Associator_NewsList&issuanceToID=18&newsTypeID=46' : {
            'url' : 'http://www.cccla.org.cn/IssuanceMore.aspx?m=Associator_NewsList&issuanceToID=18&newsTypeID=46',
            'payload': {
                '__VIEWSTATE' : '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',
                '__VIEWSTATEGENERATOR' : '59481E4C',
                '__SCROLLPOSITIONX' : '0',
                '__SCROLLPOSITIONY' : '287.20001220703125',
                '__EVENTTARGET': 'ctl00$cphContent$FrontPager1$Pager1',
                '__EVENTARGUMENT': '1',
                'ctl00$UCTop$txtID: ': '',
                'ctl00$UCTop$txtCode': '',
                'ctl00$UCBottom1$dwnMoreLink': '-1'
            }
        },
        'http://www.cccla.org.cn/IssuanceMore.aspx?issuanceToID=23&newsTypeID=13' : {
            'url' : 'http://www.cccla.org.cn/IssuanceMore.aspx?issuanceToID=23&newsTypeID=13',
            'payload' : {
                '__VIEWSTATE' : '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',
                '__VIEWSTATEGENERATOR' : '59481E4C',
                '__SCROLLPOSITIONX' : '0',
                '__SCROLLPOSITIONY' : '303.20001220703125',
                '__EVENTTARGET': 'ctl00$cphContent$FrontPager1$Pager1',
                '__EVENTARGUMENT': '1',
                'ctl00$UCTop$txtID: ': '',
                'ctl00$UCTop$txtCode': '',
                'ctl00$UCBottom1$dwnMoreLink': '-1'
            }
        }
    }
    
    def parse_intermediate(self, response):
        start_url = response.meta.get('start_url')
        api_data = self.api_start_url[start_url]
        api_url = api_data["url"]
        if not api_url:
            self.logger.error(f"No API configuration found for start_url: {start_url}")
            return
        else:
            current_page = response.meta.get("current_page", 1)
            api_data["payload"]["__EVENTARGUMENT"] = str(current_page)
            payload = api_data["payload"]
            headers = {
                "Content-Type": "application/x-www-form-urlencoded"
            }
            yield scrapy.FormRequest(
                url=api_url,
                method="POST",
                headers=headers,
                dont_filter=True,
                formdata=payload,
                callback=self.parse,
                meta={
                    "start_url": start_url,
                    "api_url": api_data["url"],
                    "payload": payload,
                    "current_page": current_page
                },
            )
        
    @property
    def source_type(self) -> str:
        return "IndustryAssociation"

    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response):       
        soup = BeautifulSoup(response.text, 'html.parser')
        return [a['href'] for a in soup.find_all('a', class_='cpx14hei')] 
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self,response) -> str:
        return response.xpath("//span[@id = 'ctl00_cphContent_lblNewsTitle']/text()").get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//span[@id = 'ctl00_cphContent_lblContent']/p[@class='MsoNormal']//span/text()").getall()) 
    
    def get_images(self, response) -> list[str]:
        return response.xpath("//span[@id = 'ctl00_cphContent_lblContent']/p[@class='MsoNormal']//img/@src").extract()

    def get_authors(self, response):
        return []

    def date_format(self) -> str:
        return '%Y-%m-%d'
    
    def get_date(self, response) -> str:
        date_text = response.xpath('//span[@id="ctl00_cphContent_lblNewsCreateTime"]//text()').get()
        date_match = re.search(r'(\d{4})年(\d{2})月(\d{2})日', date_text)
        return f"{date_match.group(1)}-{date_match.group(2)}-{date_match.group(3)}" if date_match else ""
        
    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response, current_page) -> Union[None, str]:
        text = response.xpath("//div[@id='ctl00_cphContent_FrontPager1_Pager1']/div//text()").get()
        match = re.search(r"(\d+)\s*/\s*(\d+)", text)
        if match:
            total_pages = int(match.group(2))
        if current_page < total_pages:
            return current_page + 1
        else:
            return None 
    
    def go_to_next_page(self, response, start_url, current_page = 1):
        api_url = response.meta.get("api_url")
        if not api_url: 
            logging.info("API URL not found in meta data.")
            return
        next_page = self.get_next_page(response, current_page)
        if next_page:
            yield scrapy.Request(
                url=api_url,
                callback=self.parse_intermediate,
                meta={'current_page': next_page, 'start_url': start_url}
            )
        else:
            yield None 