from scraper.OCSpider import OCSpider
import scrapy
from typing import List, Union
import json
from urllib.parse import urlencode


class ZhongtaiSecuritiesCoLtd(OCSpider):
    name = 'ZhongtaiSecuritiesCoLtd'

    start_urls_names = {
        'https://open.sseinfo.com/ir2/indexkcb?language=zh_CN&stockCode=600918': '中泰证券',
    }

    api_start_url = {
        'https://open.sseinfo.com/ir2/indexkcb?language=zh_CN&stockCode=600918': {
            'url': 'https://open.sseinfo.com/ir2/noticeIn',
            'payload': {
                "code": "600918",
                "perpage": "5",
                "page": "1",
                "bulletintype": "2"
            },
        },
    }

    charset = 'iso-8859-1'
    
    article_data_map = {}

    @property
    def source_type(self) -> str:
        return "SOE"

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def parse_intermediate(self, response):
        start_url = response.meta.get('start_url')
        current_page = response.meta.get("current_page", 1)
        api_data = self.api_start_url[start_url]
        api_data["payload"]["page"] = str(current_page)
        payload = api_data["payload"]
        headers = {
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0",
        }
        query_string = urlencode(payload)
        url = f"{api_data['url']}?{query_string}"
        yield scrapy.Request(
            url=url,
            method="GET",
            headers=headers,
            callback=self.parse,
            meta={
                "start_url": start_url,
                "current_page": current_page,
                "payload": payload
            },
            dont_filter=True
        )

    def get_articles(self, response):
        try:
            articles = []
            data = response.json()
            items = data.get("pageContent", [])
            for item in items:
                article_url = item.get("url")
                title = item.get("title")
                date = item.get("ssedate")
                if article_url and title and date:
                    full_url = f'https://static.sse.com.cn{article_url}'
                    self.article_data_map[full_url] = {
                        "title": title.strip(),
                        "date": date.strip(),
                        "pdf": full_url,
                    }
                    articles.append(full_url)
            return articles
        except json.JSONDecodeError as e:
            return []

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")

    def get_body(self, response) -> str:
        return ""

    def get_images(self, response) -> List[str]:
        return []

    def date_format(self) -> str:
        return '%Y-%m-%d'

    def get_date(self, response):
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")

    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None) -> list:
        return [self.article_data_map.get(response.request.meta.get('entry'), {}).get("pdf", "")]

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response, current_page) -> Union[None, int]:
        try:
            data = response.json()
            total_pages = data.get("Pages", 1)
            if current_page < total_pages:
                return current_page + 1
            return None
        except Exception as e:
            return None

    def go_to_next_page(self, response, start_url, current_page=1):
        next_page = self.get_next_page(response, current_page)
        if not next_page:
            return
        api_data = self.api_start_url[start_url]
        payload = api_data["payload"]
        payload["page"] = str(next_page)
        headers = {
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0",
        }
        query_string = urlencode(payload)
        url = f"{api_data['url']}?{query_string}"
        yield scrapy.Request(
            url=url,
            method="GET",
            headers=headers,
            callback=self.parse,
            meta={
                "start_url": start_url,
                "current_page": next_page,
                "payload": payload
            },
            dont_filter=True
        )