from typing import Optional
from scraper.OCSpider import OCSpider
import re
from datetime import datetime

class WisconsinDepartmentOfRevenue(OCSpider):
    name = "WisconsinDepartmentOfRevenue"

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350
        },
        "DOWNLOAD_DELAY": 5,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    HEADLESS_BROWSER_WAIT_TIME = 10000   # 10 Seconds wait time
   
    start_urls_names = {
        "https://www.revenue.wi.gov/Pages/News/Home.aspx": "News"
    }

    charset = "iso-8859-1"
    
    visited_links = set()  # Keep track of visited URLs to avoid reprocessing
    page_queue = []  # Queue to store pages for further processing
    article_date_map = {}  # Mapping date with articles from start URL

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self): 
        return "English"
    
    @property
    def timezone(self):
        return "America/Chicago"

    def get_articles(self, response) -> list:
        # Extract articles from function
        self.extract_articles(response)
        article_urls = response.xpath("//table[@class='table table-bordered table-hover']//td[@data-title='Name']/a/@href").getall()
        return [response.urljoin(url.strip()) for url in article_urls if url]
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        article_data = self.article_date_map.get(response.url)
        return article_data["title"]

    def get_body(self, response) -> str:
        # Only PDF's are there to scrap
        return ""
    
    def get_images(self, response) -> list:
        # Only PDF's are there to scrap
        return []
    
    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response) -> Optional[str]:
        article_data = self.article_date_map.get(response.url)
        if not article_data:
            return None
        date_str = article_data["date"]
        extracted_year = article_data["year"]
        try:
            date_obj = datetime.strptime(date_str, "%B %d")
            full_date = datetime(extracted_year, date_obj.month, date_obj.day)
            return full_date.strftime("%Y-%m-%d")
        except ValueError:
            return None
    
    def get_authors(self, response):
        # Only PDF's are there to scrap
        return []
    
    def get_document_urls(self, response, entry=None) -> list:
        return response.xpath("//table[@class='table table-bordered table-hover']//td[@data-title='Name']/a/@href").getall()
    
    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> Optional[str]:
        next_pages = response.xpath('//a[contains(@href, "PubYear=")]/@href').getall()
        for next_page in next_pages:
            full_next_page = response.urljoin(next_page.strip())
            year_match = re.search(r'PubYear=(\d{4})', next_page)
            extracted_year = int(year_match.group(1)) if year_match else None
            if extracted_year and full_next_page not in self.visited_links:
                self.visited_links.add(full_next_page)
                return full_next_page
        return None
    
    def extract_articles(self, response):
        # Function to extract dates of respective articles from start URL
        year_header = response.xpath("//table[@class='table table-bordered table-hover']//th[@colspan='2']/text()").get()
        extracted_year = int(year_header.strip()) if year_header and year_header.strip().isdigit() else datetime.today().year
        for article in response.xpath("//table[@class='table table-bordered table-hover']//tr"):
            date = article.xpath("./td[@data-title='Date']/text()").get()
            title = article.xpath("./td[@data-title='Name']/a/text()").get()
            url = article.xpath("./td[@data-title='Name']/a/@href").get()
            if title and date and url:
                full_url = response.urljoin(url.strip())
                self.article_date_map[full_url] = {"title": title.strip(), "date": date.strip(), "year": extracted_year}