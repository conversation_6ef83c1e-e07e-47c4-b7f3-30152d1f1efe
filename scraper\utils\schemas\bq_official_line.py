from google.cloud import bigquery

default_schema = [
        bigquery.Schema<PERSON>ield("inserted", "TIMESTAMP", mode="REQUIRED"),
        bigquery.SchemaField("id", "STRING", mode="REQUIRED"),
        bigquery.Schema<PERSON>ield("article_number", "INTEGER", mode="REQUIRED"),
        bigquery.SchemaField("title", "STRING"),
        bigquery.SchemaField("subhead", "STRING"),
        bigquery.SchemaField("author", "STRING"),
        bigquery.SchemaField("source", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("print", "STRING"),
        bigquery.SchemaField("date", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("body", "STRING", mode="REQUIRED"),
        bigquery.<PERSON>hema<PERSON>ield("pic_list", "STRING"),
        bigquery.Schema<PERSON>ield("original_link", "STRING"),
        bigquery.Schema<PERSON>ield("body_html", "STRING")
]