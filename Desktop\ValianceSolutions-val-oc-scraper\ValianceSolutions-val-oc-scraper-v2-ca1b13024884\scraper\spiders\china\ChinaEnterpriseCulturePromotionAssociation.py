from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ChinaEnterpriseCulturePromotionAssociation(OCSpider):
    name = 'ChinaEnterpriseCulturePromotionAssociation'

    start_urls_names = {
        'https://www.cecia.cn/index.php?m=content&c=index&a=lists&catid=25': '通知公告',
        'https://www.cecia.cn/index.php?m=content&c=index&a=lists&catid=21': '资讯动态',
        'https://www.cecia.cn/index.php?m=content&c=index&a=lists&catid=22': '会员动态',
        'https://www.cecia.cn/index.php?m=content&c=index&a=lists&catid=38': '党建专栏'
    }
    
    charset = "utf-8"
    
    @property
    def source_type(self) -> str:
        return "IndustryAssociation"
    
    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        return response.xpath("//div/ul/li[@class='clearfix']//a/@href").getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self,response) -> str:
        return response.xpath('//div[@class="articleTitle"]//h1//text()').get()
    
    def date_format(self) -> str:
        return '%Y-%m-%d %H:%M:%S'
    
    def get_date(self, response) -> str:
        return response.xpath(".//div[@class='articleTitle']//text()").re_first(r"\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}")
    
    def get_authors(self, response):
        return []
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="articleCon"]//p//text()').getall())
    
    def get_images(self, response) -> list[str]:
        return response.xpath("//div[@class='articleCon']//img/@src").extract()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        try:
            next_page = response.xpath('//*[@id="pageGro"]//a[contains(text(), "下一页")]/@href').get()
            if next_page:
                next_page_url = response.urljoin(next_page)
                if next_page_url == response.url:   # Condition to check if the next page is the same as the current page
                    self.logger.info(f"Next page resolves to the current page URL: {response.url}. Stopping pagination.")
                    return None   # Stop crawling
                self.logger.info(f"Found next page: {next_page_url}")
                return next_page_url
            self.logger.info("No next page found.")
            return None
        except Exception as e:
            self.logger.error(f"Error extracting next page link from page {response.url}: {e}")
            return None