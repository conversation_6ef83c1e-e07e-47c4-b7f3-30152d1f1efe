from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ParliamentOfLabuhanbatuRegency(OCSpider):
    name = "ParliamentOfLabuhanbatuRegency"
    
    country = "ID"

    start_urls_names = {
        "https://dprdlabuhanbatu.com/category/berita-hari-ini/" : "Publications"
    }
    
    charset = "utf-8"

    @property
    def language(self): 
        return "Indonesian"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    def get_articles(self, response) -> list:
        return response.xpath("//div[@class='inner']//h4//a//@href").getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//div[@class='inner']//h4//text()").get().strip()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='content']//p//text()").getall()) 
        
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%b, %a, %Y"
    
    def get_date(self, response) -> str:
        return response.xpath('//div[@class="date"]/text()')[1].get().strip()
    
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
     
    def get_next_page(self, response): 
        return response.xpath("//div[@class='nav-links']//a[@class='next page-numbers']//@href").get()