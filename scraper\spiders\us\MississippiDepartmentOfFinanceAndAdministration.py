from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
from typing import List, Union
import re 

class MississippiDepartmentOfFinanceAndAdministration(OCSpider):
    name = 'MississippiDepartmentOfFinanceAndAdministration'

    country = "US"

    start_urls_names = {
        'https://treasury.ms.gov/newsroom/':'Newsroom',
    }
    
    charset="utf-8"

    @property
    def source_type(self) -> str:
        return 'ministry'
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Central"

    def get_articles(self, response) -> list:
        return response.xpath("//div[@class='entry-content excerpt']//a/@href").getall()
    
    def get_href(self, entry) -> str:
        return entry

    def get_title(self,response) -> str:
        return response.xpath("//div[@class='cs-content']//h2/text()[normalize-space()]").get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="e150-6 x-col"]//p//text()[normalize-space()]').getall()) 
    
    def get_images(self, response) -> List[str]:
        return []
        
    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response) -> str:
        match = re.search(r"/(\d{4})/(\d{2})/(\d{2})/", response.url)
        if match:
            year, month, day = match.groups()
            return f"{year}-{month}-{day}"
        return None
    
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Union[None, str]:
        next_page = response.xpath("//div[@class ='x-pagination']//a[contains(text(), '→')]/@href").get()
        if next_page:
            return response.urljoin(next_page)
        else:
            return None