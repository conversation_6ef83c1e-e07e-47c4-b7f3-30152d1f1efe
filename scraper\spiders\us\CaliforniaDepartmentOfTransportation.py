from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime

class CaliforniaDepartmentOfTransportation(OCSpider):
    name = "CaliforniaDepartmentOfTransportation"

    country = "US"

    start_urls_names = {
        "https://dot.ca.gov/news-releases": "News Releases",
    }
        
    charset = "utf-8"

    @property
    def language(self): 
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "America/Los_Angeles"
    
    def get_articles(self, response) -> list:
        return response.xpath('//section[@class="news-list"]//article[@class="news-item"]//a/@href').getall()
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[@itemprop="headline"]/text()').get()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@itemprop="articleBody"]//text()').getall()) 
        
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return"%m-%d-%Y"
    
    def get_date(self, response) -> str:
        date_str = (response.xpath('//div[@class="published"]/time[@class="date"]//text()').get()).strip()
        return datetime.strptime(date_str, "%b %d, %Y").strftime("%m-%d-%Y")
            
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        next_page = response.xpath('//a[@aria-label="Next"]/@href').get()
        if not next_page:
           return None
        else:
            return next_page