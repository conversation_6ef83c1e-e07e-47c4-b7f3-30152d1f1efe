import scrapy
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class CenterForStrategicAndInternationalStudies(OCSpider):
    name = "CenterForStrategicAndInternationalStudies"

    country="US"

    start_urls_names = {
        "https://www.csis.org/analysis" : "Press Releases",
    }

    charset = "utf-8"

    @property
    def language(self):
        return "English"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "US/Eastern"
    
    def get_articles(self, response) -> list:
       return response.xpath("//h3//a//@href").getall()
       
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//h1//text()").get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='wysiwyg-wrapper text-high-contrast']//text()").getall())

    def get_images(self, response, entry=None) :
        return response.xpath("//picture//img//@src").getall()
    
    def get_authors(self, response, entry=None) -> list[str]:
        return []

    def date_format(self) -> str:
        return "%B %d, %Y"
    
    def get_date(self, response) -> str:
        date = response.xpath("//p[@class='mt-xs']//text() | //div[@class='mt-0 utility-xs text-md-contrast']//text()").get()
        date = date.split("•")[0].replace("Published ","").strip()
        return date 
    
    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        next_page=response.xpath("//a[@title='Go to next page']//@href").get()
        if next_page:
            return next_page
        return None