from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization

class CenterForGlobalDevelopment(OCSpider):
    name = 'CenterForGlobalDevelopment'
    
    country = "US"

    start_urls_names = {
        'https://www.cgdev.org/section/publications': 'News'
    }
    
    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350
        },
        "DOWNLOAD_DELAY": 5,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    HEADLESS_BROWSER_WAIT_TIME = 30000  # 30 seconds wait time
    
    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Central"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@class = "search-result-left"]/a/@href').getall() 
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self,response) -> str:
        return response.xpath('//h1[@class= "title-default"]/text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class= "field-body-blog-post w3-bar-item field__item"]/p/text()').getall())    
            
    def get_images(self, response) -> list[str]:
        return []

    def date_format(self) -> str:
        return "%B %d, %Y"

    def get_date(self, response) -> str:
        return response.xpath('//div[@class= "published-time"]/text()').re_first(r"([A-Za-z]+ \d{1,2}, \d{4})")
    
    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        return response.xpath('//li[@class ="w3-button pager__item pager__item--next li-0"]/a/@href').get()