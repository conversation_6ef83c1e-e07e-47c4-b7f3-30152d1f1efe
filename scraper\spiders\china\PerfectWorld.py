from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import scrapy

class PerfectWorld(OCSpider):
    name = "PerfectWorld"

    country="US"

    start_urls_names = {
        "https://www.pwrd.com/cn/report.html" :"Newsroom",
        "https://www.pwrd.com/cn/capital.html" :"Newsroom",
        "https://www.pwrd.com/cn/notice.html" :"Newsroom",
        "https://www.pwrd.com/cn/news/press/p2025/index.html" :"Newsroom", 
    }

    charset="iso-8859-1"

    def parse_intermediate(self, response):
            articles = response.xpath("//div[@class='report_box']//a//@href | //div[@class='news_list']//h2//a//@href").getall()
            total_articles = len(articles)
            start_url = list(self.start_urls_names.keys()) 
            for url in start_url:
                for start_idx in range(0,total_articles, 100):  # Indexing for virtual pagination to extract more than 100 articles from single page
                    yield scrapy.Request(
                        url=url,  
                        callback=self.parse,
                        meta={'start_idx': start_idx, 'start_url': url},  
                        dont_filter=True
                    )
                
    @property
    def language(self) :
        return "English"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "US/Eastern"
    
    article_data_map = {}  

    def get_articles(self, response) -> list:
        try:
            articles = response.xpath("//div[@class='report_box']")
            article_urls=[]
            for article in articles:
                url = article.xpath(".//a//@href").get()
                title = article.xpath(".//div[@class='report_left']/p[1]//text()").get()
                date = article.xpath(".//div[@class='report_left']//h2//span//following-sibling::*[1]//text() | //div[@class='report_box']//div[@class='report_left']//h2//em//text()").get()
                body = article.xpath("//div[@class='report_left']/p[2]//text()").get()
                if 'https' in url:
                    continue
                else:
                    url = f"https://www.pwrd.com/cn/{url}"
                if url and title and date:
                    full_url = url
                    clean_date = date.strip().replace("\xa0", " ") 
                    body = body_normalization(body)
                    self.article_data_map[full_url] = {"title": title.strip(), "date": clean_date, "pdf": [full_url], "body": body}
                    article_urls.append(full_url)
            all_articles = article_urls
            start_idx = response.meta.get('start_idx', 0) # Indexing should be called from parse_intermediate only
            end_idx = start_idx + 100
            # print("aaa",all_articles[start_idx:end_idx], len(all_articles[start_idx:end_idx]))
            return all_articles[start_idx:end_idx]  # Article url's are extracted and returned
        except Exception as e:
            return []

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map[response.request.meta.get('entry')].get("title")
        
    def get_body(self, response) -> str:
        body = self.article_data_map[response.request.meta.get('entry')].get("body")
        body = body_normalization(body)
        if body:
            return body
        return body_normalization(response.xpath("//div[@class='news_cont']//p//text()").getall())

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        return self.article_data_map[response.request.meta.get('entry')].get("date")

    def get_authors(self, response, entry=None) -> list[str]:
        return []
    
    def get_document_urls(self, response, entry=None):
        return self.article_data_map[response.request.meta.get('entry')].get("pdf")
    
    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        return None