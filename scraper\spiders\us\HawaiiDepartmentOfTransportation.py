from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime
import re

class HawaiiDepartmentOfTransportation(OCSpider):
    name = "HawaiiDepartmentOfTransportation"

    country = "US"

    start_urls_names = {
        "https://hidot.hawaii.gov/blog/category/news/": "News",
    }
        
    charset = "utf-8"

    @property
    def language(self): 
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Pacific/Honolulu"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="post"]//h3/a/@href').getall()
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="pagetitle"]/h2/text()').get()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="primary-content"]//p/text()').getall()) 
        
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%m-%d-%Y"
    
    def get_date(self, response) -> str:
        date_str = (response.xpath('//div[@class="pagetitle"]/span/text()').get()).strip()
        match = re.search(r"([A-Za-z]+ \d{1,2}, \d{4})", date_str)
        clean_date = match.group(1) 
        return datetime.strptime(clean_date, "%b %d, %Y").strftime("%m-%d-%Y")
            
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        next_page = response.xpath('//a[@class="next page-numbers"]/@href').get()
        if not next_page:
           return None
        else:
            return next_page