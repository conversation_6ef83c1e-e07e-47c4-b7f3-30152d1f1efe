from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from urllib.parse import urljoin
import re
from datetime import datetime
import pytz

class WingtechSseMinimal(OCSpider):
    name = "WingtechSseMinimal"
    country = "CN"

    start_urls_names = {
        "http://www.wingtech.com/cn/toWTNEWS/21/1": "Wingtech News",
        "https://www.sse.com.cn/assortment/stock/list/info/announcement/index.shtml?productId=600745": "SSE Announcements",
    }
    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "Corporate"

    @property
    def language(self) -> str:
        return "Chinese"

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_articles(self, response) -> list:
        # For Wingtech website
        if "wingtech.com" in response.url:
            # Try to find news links
            links = response.xpath('//a[contains(@href, "/cn/")]/@href').getall()
            return [response.urljoin(link) for link in links if link and not link.startswith('#')]
        # For SSE website
        else:
            # Try to find announcement links
            links = response.xpath('//a[contains(@href, ".pdf")]/@href').getall()
            return [response.urljoin(link) for link in links if link]

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response, entry=None) -> str:
        # Extract title
        title = response.xpath('//title/text()').get()
        if not title:
            title = response.xpath('//h1/text()').get()
        return title.strip() if title else "No Title"

    def get_body(self, response, entry=None) -> str:
        # Extract body content
        body_parts = response.xpath('//div[contains(@class, "content")]//text()').getall()
        if not body_parts:
            body_parts = response.xpath('//div[@id="content"]//text()').getall()
        return body_normalization(body_parts or ["No content available"])

    def get_images(self, response, entry=None) -> list:
        # Extract images
        img_urls = response.xpath('//div[contains(@class, "content")]//img/@src').getall()
        return [urljoin(response.url, img) for img in (img_urls or [])]

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response, entry=None) -> str:
        # Try to extract date
        date_str = response.xpath('//div[contains(@class, "date")]/text() | //span[contains(@class, "date")]/text()').get()
        if date_str:
            date_match = re.search(r'(\d{4}-\d{2}-\d{2})', date_str)
            if date_match:
                return date_match.group(1)
        # Return current date if no date found
        return datetime.now(pytz.timezone(self.timezone)).strftime(self.date_format())

    def get_authors(self, response, entry=None) -> list:
        # Try to extract author
        author = response.xpath('//div[contains(@class, "author")]/text()').get()
        if author and author.strip():
            return [author.strip()]
        return ["Wingtech" if "wingtech.com" in response.url else "SSE"]

    def get_document_urls(self, response, entry=None) -> list:
        # Extract document URLs
        doc_urls = response.xpath('//a[contains(@href, ".pdf") or contains(@href, ".doc") or contains(@href, ".docx")]/@href').getall()
        return [urljoin(response.url, doc) for doc in (doc_urls or [])]

    def get_next_page(self, response) -> list:
        # Try to find next page link
        next_page = response.xpath('//a[contains(text(), "下一页") or contains(text(), "Next")]/@href').get()
        if next_page:
            return [response.urljoin(next_page)]
        return []

    def get_page_flag(self) -> bool:
        return True

    def get_meta(self, response, entry=None) -> list:
        return []

    def get_pdf(self, response, entry=None):
        return None
