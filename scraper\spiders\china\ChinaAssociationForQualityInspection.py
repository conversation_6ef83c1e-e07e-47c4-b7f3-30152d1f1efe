from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import scrapy
import re

class ChinaAssociationForQualityInspection(OCSpider):
    name = 'ChinaAssociationForQualityInspection'
    
    custom_settings = {
        "DOWNLOADER_MIDDLEWARES" : {
            'scraper.middlewares.HeadlessBrowserProxy': 350,
        },
        "DOWNLOAD_DELAY" : 2,
    }

    HEADLESS_BROWSER_WAIT_TIME = 5000 

    include_rules = [r'.*https://www\.chinatt315\.org\.cn/.*']
    
    start_urls_names = {
        "https://www.chinatt315.org.cn/enterprise/list.html" : "会员资讯", 
        "https://www.chinatt315.org.cn/xhwj/index.html" : "中国质量检验协会文件" 
    }
    
    charset = "utf-8"
    
    def parse_intermediate(self, response):
        articles = response.xpath('//div[@class="grayborder wenjian"]//li/a[contains(text(),"  ")]/@href').getall() #for this starturl - https://www.chinatt315.org.cn/enterprise/list.html
        if not articles:
            articles = response.xpath('//div[@class="ky_center rightmore"]//ul//li//a/@href').getall() #for this starturl - https://www.chinatt315.org.cn/xhwj/index.html
        total_articles = len(articles)
        start_url = response.meta.get("start_url")
        for start_idx in range(0, total_articles, 100):
            yield scrapy.Request(
                    url=start_url,
                    callback=self.parse,
                    meta={'start_idx': start_idx, 'start_url': start_url},
                    dont_filter=True
            )
    
    @property
    def source_type(self) -> str:
        return 'IndustryAssociation'
    
    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        articles = response.xpath('//div[@class="grayborder wenjian"]//li/a[contains(text(),"  ")]/@href').getall() # For first starturl - https://www.chinatt315.org.cn/enterprise/list.html
        if not articles:
            articles = response.xpath('//div[@class="ky_center rightmore"]//li//a/@href').getall() # For second starturl - https://www.chinatt315.org.cn/xhwj/index.html
        unique_articles = list(set(articles))  # Removes duplicate article URLs to ensure uniqueness
        # Handles pagination for the second URL where more than 100 articles exist
        # Fetches only a batch of 100 articles at a time
        start_idx = response.meta.get('start_idx', 0)
        end_idx = start_idx + 100
        return unique_articles[start_idx:end_idx]   
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1//text()').get()
    
    def get_body(self, response) -> str:
        body = response.xpath('//div[@id="newscontent"]/h2//p//text()').getall() # https://www.chinatt315.org.cn/enterprise/2025-2/8/39287.html
        if not body:
            body = response.xpath('//div[@class="wj"]/p//text()').getall()  # https://www.chinatt315.org.cn/xhfw/2024-10/220119/245672.html
        return body_normalization(body)
    
    def get_images(self, response) -> list:
        images = response.xpath('//div[@id="newscontent"]/h2//p/img/@src').getall() # https://www.chinatt315.org.cn/enterprise/2025-2/8/39287.html
        if not images:
            images = response.xpath('//div[@class="wj"]//img/@src').getall() # https://www.chinatt315.org.cn/xhfw/2023-8/28/237567.html
        return images
    
    def date_format(self) -> str:
        return '%Y-%m-%d'
    
    def get_date(self, response) -> str:
        match = re.search(r"/(\d{4})-(\d{1,2})/(\d{1,2})", response.url)
        if match:
            year, month, day = match.groups()
            return f"{year}-{month}-{day}"
        return None
    
    def get_authors(self, response):
        return []
            
    def get_page_flag(self) -> bool:
        return False
     
    def get_next_page(self, response) -> str:
        next_page = response.xpath('//div[@id="paging"]/div//a[contains(text(), "下一页")]/@href').get()
        if next_page:
            return next_page
        else:
            return None