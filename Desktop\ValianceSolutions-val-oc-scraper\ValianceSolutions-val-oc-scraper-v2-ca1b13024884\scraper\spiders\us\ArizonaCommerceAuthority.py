from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime
import re 

class ArizonaCommerceAuthority(OCSpider):
    name = "ArizonaCommerceAuthority"

    country = "US"

    start_urls_names = {
        "https://www.azcommerce.com/news-events/news/?categories=": "News Feed",
    }
        
    charset = "utf-8"

    @property
    def language(self): 
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "America/Phoenix"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[contains(@class, "news__wrapper")]//a[@class="black"]/@href').getall()
            
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="contain text-left blue moveTitle"]//h1/span/text()').get()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="mainContent col-xs-12 center"]//p//text()').getall())
        
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%m-%d-%Y"
    
    def get_date(self, response) -> str:
        text = " ".join(response.xpath('//div[@class="mainContent col-xs-12 center"]//p//text()').getall())
        date_str = re.search(r'\((\w+\s+\d{1,2},\s+\d{4})\)', text).group(1)
        return datetime.strptime(date_str, "%B %d, %Y").strftime("%m-%d-%Y")
        
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        return None