from scraper.OCSpider import OCSpider
from datetime import datetime
import scrapy

class HawaiiAttorneyGeneral(OCSpider):
    name = "HawaiiAttorneyGeneral"

    country = "US"

    start_urls_names = {
        "https://ag.hawaii.gov/news-releases/" : "News Releases"
    }
    
    charset="iso-8859-1"

    @property
    def language(self) -> str:
        return "English"
    
    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self) -> str:
        return "US/Eastern"
    
    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        current_year = response.meta.get("current_year",datetime.now().year)
        url = f"{start_url}news-releases-{current_year}/"
        yield scrapy.Request(
            url = url,
            callback = self.parse,
            dont_filter = True, 
            meta = {
                "start_url":start_url,
                "current_year":current_year
            }
        )

    article_data_map = {}  # Mapping article with title, date and pdf from start URL
    
    def get_articles(self, response) :
        self.extract_articles_with_dates(response)
        return response.xpath('//div[@class="primary-content"]//p//a/@href').getall()
    
    def get_href(self, entry: str) -> str:
        return entry

    def get_title(self, response) :
        return self.article_data_map[response.request.meta.get('entry')].get("title")
    
    def get_body(self, response) -> str:
        return ""

    def get_images(self, response) :
        return []

    def date_format(self) -> str:
        return "%m/%d/%Y"

    def get_date(self, response) -> str:
        return self.article_data_map[response.request.meta.get('entry')].get("date")
    
    def get_authors(self, response) :
        return []
    
    def get_document_urls(self, response, entry=None):
        return self.article_data_map[response.request.meta.get('entry')].get("pdf")

    def get_next_page(self, response) :
        current_year=int(response.meta.get("current_year"))-1
        if response.status!=200:
            return None
        else:
            return str(current_year)

    def get_page_flag(self) -> bool:
        return True
    
    def go_to_next_page(self, response, start_url, current_page=None):
        start_url = response.meta.get("start_url")
        previous_year = self.get_next_page(response)
        url=f"{start_url}news-releases-{previous_year}/"
        if previous_year:
            yield scrapy.Request(
                url = url,
                callback=self.parse_intermediate,
                meta = {
                    "start_url":start_url,
                    "current_year":previous_year
                }
            )
        else:
            self.logger.info("No more pages to scrape")
            return None

    def extract_articles_with_dates(self, response):
        mapping = {}
        for article in response.xpath('//div[@class="primary-content"]//p'):
                url = article.xpath(".//a/@href").get()
                title = article.xpath(".//a/text()").get()
                date = article.xpath("./text()").get().strip().rstrip(':')
                if url and date and title :
                    mapping[url] = {
                        "title": title,
                        "date": date,
                        "pdf": url
                    }
        self.article_data_map.update(mapping)
        return self.article_data_map