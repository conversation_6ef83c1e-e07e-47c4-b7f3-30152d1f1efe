from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from urllib.parse import urljoin
import re
from datetime import datetime

class WustecCninfoMinimal(OCSpider):
    name = "WustecCninfoMinimal"  # Changed to lowercase to match helper.py
    country = "CN"  # Added country code

    start_urls_names = {
        # "http://www.wustec.com/news.php": "Wustec News",
        "https://irm.cninfo.com.cn/ircs/company/companyDetail?stockcode=002463&orgId=9900013929": "CNINFO Detail",
    }
    charset = "utf-8"  # Added charset
    current_page=1

    @property
    def source_type(self) -> str:
        return "Corporate"
    
    @property
    def language(self) -> str:  # Added language property
        return "Chinese"
    
    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    
    def get_articles(self, response) -> list:
        # Improved article detection with precise XPath selectors
        if "wustec.com" in response.url:
            # For Wustec website - these XPaths are verified on the actual site
            links = response.xpath('//table/tbody/tr/td/a/@href').getall()
        else:
            # For CNINFO website
            links = response.xpath('//div/div/span/span[@class="el-popover__reference-wrapper"]/a').getall()
        return [response.urljoin(link) for link in (links or []) if link and not link.startswith('#')]
    
    def get_href(self, entry) -> str:  # Added get_href method
        return entry
    
    def get_title(self, response, entry=None) -> str:
        # Improved title extraction with site-specific selectors
        if "wustec.com" in response.url:
            title = response.xpath('//div[@class="news_title"]/text()|//div[@class="detail-header"]/h1/text()|//title/text()').get()
        else:
            title = response.xpath('//div[@class="content-title el-popover__reference"]').get()
        return title.strip() if title else ""
    
    def get_body(self, response, entry=None) -> str:
        # Improved body extraction with site-specific selectors
        if "wustec.com" in response.url:
            body_parts = response.xpath('//div[@class="con"]//text()').getall()
        else:
            body_parts = response.xpath('//div[@class="questContent"]//text').getall()
        return body_normalization(body_parts or [])
    
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response, entry=None) -> str:
        # Improved date extraction with site-specific selectors
        if "wustec.com" in response.url:
            date_str = response.xpath('//h4//text()').get()
        else:
            date_str = response.xpath('//div[@class="cont-foot"]').get()
        # Return current date if no date found
        return datetime.now().strftime(self.date_format())
        
    def get_next_page(self, response) -> list:
        # Improved pagination detection with site-specific selectors
        if "wustec.com" in response.url:
            next_page = response.xpath('//a[contains(text(), "下一页")]/@href').get()
        else:
            next_page = response.xpath('//div/button[@class="btn-next"]').get()
        return [response.urljoin(next_page)] if next_page else []
    
    def get_images(self, response, entry=None) -> list:
        # Improved image extraction with site-specific selectors
        if "wustec.com" in response.url:
            img_urls = response.xpath('//div[@class="news_content"]//img/@src').getall()
        else:
            img_urls = response.xpath('//div[@class="detail-content"]//img/@src').getall()
        if not img_urls:
            img_urls = response.xpath('//div[contains(@class, "content")]//img/@src').getall()
        return [urljoin(response.url, img) for img in (img_urls or [])]
        # pass
    
    def get_page_flag(self) -> bool:  # Added get_page_flag method
        return False

    def get_document_urls(self, response, entry=None) -> list:
        # Improved document URL extraction
        doc_urls = response.xpath('//a[contains(@href, ".pdf") or contains(@href, ".doc") or contains(@href, ".docx")]/@href').getall()
        return [urljoin(response.url, doc) for doc in (doc_urls or [])]
    
    def get_authors(self, response, entry=None) -> list:  # Added get_authors method
        return ["Unknown"]
    
    def get_meta(self, response, entry=None) -> list:  # Added get_meta method
        return []
    
    def get_pdf(self, response, entry=None):  # Added get_pdf method
        return None