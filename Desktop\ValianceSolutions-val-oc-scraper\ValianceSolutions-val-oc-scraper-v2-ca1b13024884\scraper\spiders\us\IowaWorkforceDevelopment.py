from typing import Optional
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
from datetime import datetime

class IowaWorkforceDevelopment(OCSpider):
    name = "IowaWorkforceDevelopment"

    country = "US"

    start_urls_names = {
        "https://workforce.iowa.gov/news/newsroom": "Newsroom",
        }
        
    charset = "utf-8"
    
    @property
    def language(self): 
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "America/Chicago"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="views-row"]//a[@class="sdc-component link-default link__link-collection"]/@href').getall()
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="page-title"]/span/text()').get(default='').strip()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="content-body clearfix text-formatted field field--name-field-news__body field--type-text-long field--label-hidden field__item"]/p//text()').getall()) 
        
    def get_images(self, response) -> list:
        return [] 

    def date_format(self) -> str:
        return "%m-%d-%Y"

    def get_date(self, response) -> str:
        date_text = response.xpath('//div[contains(@class, "field--name-field-news__display-date")]//time/text()').get()
        if not date_text:
            date_text = response.xpath('//div[contains(@class, "content-body")]//span[contains(text(), "Date:")]/text()').get()
            if date_text:
                date_text = date_text.replace("Date:", "").strip()
        if date_text:
            for fmt in ["%A, %B %d, %Y", "%B %d, %Y"]:  
                 return datetime.strptime(date_text, fmt).strftime("%m-%d-%Y")
            return date_text  
        
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        next_page = response.xpath('//li[@class="pager__item pager__item--next"]/a[@rel="next"]/@href').get()
        if not next_page:
            return None
        return response.urljoin(next_page)