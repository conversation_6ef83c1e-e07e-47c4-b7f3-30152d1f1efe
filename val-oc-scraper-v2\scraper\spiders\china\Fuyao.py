from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from typing import List
import scrapy

class <PERSON><PERSON><PERSON>(OCSpider):
    name = "<PERSON><PERSON><PERSON>"

    start_urls_names = {
        "https://www.fuyaogroup.com/new_list.html":"福耀玻璃",
        "https://www.fuyaogroup.com/communicate_director.html":"福耀玻璃",
        "https://www.fuyaogroup.com/investor_list_1.html":"福耀玻璃"  
    }

    charset = "iso-8859-1"
    
    def parse_intermediate(self, response):
        articles = response.xpath('//div[@class="layout-btm"]/a[@class="btn"]/@href').getall()
        total_articles = len(articles)
        start_url = response.meta.get("start_url")
        for start_idx in range(0, total_articles, 100):
            yield scrapy.Request(
                    url=start_url,
                    callback=self.parse,
                    meta={
                        'start_idx': start_idx, 
                        'articles': articles, 
                        'start_url': start_url
                    },
                    dont_filter=True
            )

    article_data_map = {}

    @property
    def source_type(self) -> str:
        return "private_enterprise"

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_articles(self, response):
        url =response.url
        if url =='https://www.fuyaogroup.com/communicate_director.html':
            all_articles = response.meta.get('articles', [])
            start_idx = response.meta.get('start_idx', 0)
            end_idx = start_idx + 100
            articles=[]
            for i in all_articles:
                if '3013' not in i:
                    articles.append(i)
            return articles[start_idx:end_idx]
        else:
            article_urls = response.xpath("//div[@class='Tbox pr']/@onclick").re(r"window.location.href='(.*?)'")
            if not article_urls:
                article_urls = response.xpath('//div[@class="layout-btm"]/a[@class="btn"]/@href').getall()
            if not article_urls:
                self.extract_articles_with_dates(response)
                return list(self.article_data_map.keys())
            return article_urls
    
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        entry = response.request.meta.get('entry')
        if "investor_list" in response.url:
            if entry and entry in self.article_data_map and self.article_data_map[entry].get("title"):
                return self.article_data_map[entry]["title"]
            return ""
        try:
            title = response.xpath('//h5/text()').get()
            if title:
                return title.strip()
        except Exception:
            pass
        if entry and entry in self.article_data_map and self.article_data_map[entry].get("title"):
            return self.article_data_map[entry]["title"]
        return ""

    def get_body(self, response) -> str:
        if "investor" in response.url :
            return ""  
        try:
            body_parts = response.xpath('//p//text()').getall()
            return body_normalization(body_parts)
        except Exception:
            return ""

    def date_format(self) -> str:
        return '%Y-%m-%d'

    def get_date(self, response) -> str:
        if response.request.meta.get("start_url", "").startswith("https://www.fuyaogroup.com/investor_list_"):
            entry = response.request.meta.get('entry')
            if entry and entry in self.article_data_map and self.article_data_map[entry].get("date"):
                return self.article_data_map[entry]["date"]
            return ""
        try:
            date_text = response.xpath("//span[contains(text(), '20')]/text()").get()
            return date_text.strip() if date_text else ""
        except Exception:
            return ""
                
    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None) -> List[str]:
        return []
    
    def get_images(self, response) -> list:
        return []
    
    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        next_page = response.xpath('//div[@class="page_set"]/a[contains(text(), "下一页")]/@href').get()
        if next_page and next_page != "javascript:void(0)":
            return response.urljoin(next_page)
        else:
            return None
        
    def extract_articles_with_dates(self, response):
        articles = response.xpath("//div[@class='baogaoList pr']")
        for article in articles:
            full_url = None
            pdf_path = article.xpath("./@data-ahref").get()
            if pdf_path:
                full_url = response.urljoin(pdf_path.strip())
            date_text = article.xpath("./span[1]/text()").get()
            title = article.xpath("./span[2]//text()").getall()
            title = ''.join(title).strip() if title else None
            if full_url and title and date_text:
                clean_date = date_text.strip()
                self.article_data_map[full_url] = {
                    "title": title,
                    "date": clean_date,
                    "pdf": [full_url] if full_url.lower().endswith(".pdf") else []
                }