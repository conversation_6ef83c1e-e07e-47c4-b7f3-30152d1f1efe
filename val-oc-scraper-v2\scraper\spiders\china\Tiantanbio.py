from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
import urllib.parse
import scrapy
from typing import Optional, List
import json
import re
import logging
import traceback
from datetime import datetime


class Tiantanbio(OCSpider):
    name = "Tiantanbio"

    start_urls_names = {
        'https://www.tiantanbio.com/category/company-news.html': "",
        'https://www.sse.com.cn/assortment/stock/list/info/announcement/index.shtml?productId=600161': ""
    }

    api_start_urls = {
        "https://www.sse.com.cn/assortment/stock/list/info/announcement/index.shtml?productId=600161": {
            "url": "https://query.sse.com.cn/security/stock/queryCompanyBulletin.do",
            "payload": {
                "jsonCallBack": "jsonpCallback83903",
                "isPagination": "true",
                "productId": "600161",
                "securityType": "0101,120100,020100,020200,120200",
                "reportType": "ALL",
                "pageHelp.pageSize": 25,
                "pageHelp.pageCount": 50,
                "pageHelp.pageNo": 1,
                "pageHelp.beginPage": 1,
                "pageHelp.cacheSize": 1,
                "pageHelp.endPage": 11,
                "_": "1745989205005"
            }
        },
        # Add Tiantanbio website handling (direct processing without API)
        "https://www.tiantanbio.com/category/company-news.html": None
    }

    charset = "utf-8"

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36"
    }

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"

    @property
    def timezone(self) -> str:
        return "Asia/Shanghai"
    
    @property
    def country(self) -> str:
        return "China"
    
    @property
    def language(self) -> str:
        return "Chinese"

    def parse_intermediate(self, response):
        logging.debug(f"Crawled ({response.status}) <GET {response.url}> (referer: {response.request.headers.get('Referer', b'None').decode('utf-8')})")

        if not hasattr(self, "article_date_map"):
            self.article_date_map = {}

        start_url = response.meta.get("start_url")

        # SSE API response handling
        if "queryCompanyBulletin.do" in response.url:
            try:
                jsonp_body = response.text
                match = re.search(r'\((.*?)\)', jsonp_body, re.DOTALL)
                if not match:
                    logging.error("JSONP response could not be parsed.")
                    return

                json_str = match.group(1)
                data = json.loads(json_str)

                for item in data.get("result", []):
                    pdf_path = item.get("URL")
                    title = (item.get("TITLE") or item.get("title") or "").strip()
                    date = (item.get("SSEDATE") or "").strip()

                    if not pdf_path:
                        logging.warning(f"Missing PDF URL in item: {item}")
                        continue

                    full_url = f"https://www.sse.com.cn{pdf_path}"
                    if self.is_already_crawled(start_url, full_url):
                      continue  # don't count or yield duplicates
                    self.article_date_map[full_url] = {
                        "title": title,
                        "date": date,
                    }
                    
                    # Generate the mock output for debugging
                    self._log_mock_output(full_url, title, date, start_url)
                    self.crawler.stats.inc_value("articles_crawled")
                    yield scrapy.Request(
                        url=full_url,
                        callback=self.parse_article,
                        headers=self.headers,
                        meta={
                            "title": title,
                            "date": date,
                            "start_url": start_url,
                        }
                    )
                
                # Handle pagination if needed
                if data.get("pageHelp", {}).get("pageNo", 1) < data.get("pageHelp", {}).get("pageCount", 1):
                    next_page = data.get("pageHelp", {}).get("pageNo", 1) + 1
                    payload = response.meta.get("payload", {}).copy()
                    payload["pageHelp.pageNo"] = next_page
                    api_url = self.api_start_urls.get(start_url, {}).get("url")
                    if api_url:
                        full_api_url = f"{api_url}?{urllib.parse.urlencode(payload)}"
                        yield scrapy.Request(
                            url=full_api_url,
                            method="GET",
                            headers=self.headers,
                            callback=self.parse_intermediate,
                            meta={
                                "start_url": start_url,
                                "payload": payload,
                                "current_page": next_page,
                            }
                        )
                
            except Exception as e:
                logging.error(f"Failed to parse JSONP: {e}")
            return
        
        # Direct handling for Tiantanbio HTML page
        if "tiantanbio.com" in response.url:
            try:
                # Extract news article links directly
                article_links = response.xpath(
                    '//div[contains(@class, "swiper-slide")]//a[starts-with(@href, "/news/details_")]/@href | '
                    '//div[contains(@class, "list")]//a[starts-with(@href, "/news/details_")]/@href'
                ).getall()
                
                for link in article_links:
                    full_url = response.urljoin(link)
                    yield scrapy.Request(
                        url=full_url,
                        callback=self.parse,
                        headers=self.headers,
                        meta={
                            "start_url": start_url,
                        }
                    )
                
                # Handle pagination if it exists
                next_page_link = response.xpath('//a[contains(@class, "page-next")]/@href').get()
                if next_page_link:
                    full_next_url = response.urljoin(next_page_link)
                    yield scrapy.Request(
                        url=full_next_url,
                        callback=self.parse_intermediate,
                        headers=self.headers,
                        meta={
                            "start_url": start_url,
                        }
                    )
                
                return
            except Exception as e:
                logging.error(f"Failed to parse Tiantanbio HTML: {e}")
            return

        # Fallback logic for other HTML pages
        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            logging.warning(f"No API data for start URL: {start_url}")
            return
            
        # Skip if None (direct processing without API)
        if api_data is None:
            return

        payload = response.meta.get("payload", api_data["payload"].copy())
        api_url = api_data["url"]
        full_api_url = f"{api_url}?{urllib.parse.urlencode(payload)}"

        yield scrapy.Request(
            url=full_api_url,
            method="GET",
            headers=self.headers,
            callback=self.parse_intermediate,
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "payload": payload,
                "current_page": payload.get("pageHelp.pageNo", 1),
            }
        )

    def _log_mock_output(self, url, title, date, start_url):
        import time
        import datetime
        
        # Create a mock timestamp for the output
        timestamp = int(time.time())
        
        # Build the mock article data
        article_data = {
            'id': url,
            'source': '',
            'title': title,
            'body': '',  # Empty body as requested
            'images': [],  # Can be populated if needed
            'authors': [],
            'date': timestamp,
            'start_url': start_url,
            'inserted': timestamp,
            'meta': None,
            'article_url': url,
            'newspaper': 'Tiantanbio',
            'country': 'China',
            'news_line': 'IndustryAssociation',
            'language': 'Chinese',
            'subhead': None,
            'pdf_urls': [url] if url.endswith('.pdf') else None
        }
        
        # Format the log output to match the desired format
        formatted_date = datetime.datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d")
        logging.debug(f"Article output:\n{article_data}")
        logging.info(f"Article Date ----- {formatted_date}")
        logging.debug(f"Scraped from <{200} {url}>\n{article_data}")

    def get_articles(self, response) -> list:
        articles = []

        # Skip processing if not HTML or content is empty
        content_type = response.headers.get('Content-Type', b'').decode('utf-8', errors='ignore')
        if "application/pdf" in content_type or response.url.lower().endswith(".pdf"):
            # Make sure to set title and date properly from metadata or article_date_map
            title = response.meta.get("title", "")
            date = response.meta.get("date", "")
            
            if not title and response.url in getattr(self, "article_date_map", {}):
                meta = self.article_date_map.get(response.url, {})
                title = meta.get("title", "")
                date = meta.get("date", "")
            
            articles.append({
                "url": response.url,
                "title": title,
                "date": date
            })
            return articles

        if not content_type.startswith("text/html"):
            return []

        # If this is a detail page, add it as the only article
        if "/news/details_" in response.url:
            title = self.get_title(response)
            date = self.get_date(response)
            
            if title:
                articles.append({
                    "url": response.url,
                    "title": title,
                    "date": date
                })
            return articles

        # Extract internal article links from the HTML page
        html_links = response.xpath(
            '//div[contains(@class, "swiper-slide")]//a[starts-with(@href, "/news/details_")]//@href | '
            '//div[contains(@class, "list")]//a[starts-with(@href, "/news/details_")]//@href'
        ).getall()

        for url in html_links:
            full_url = response.urljoin(url)
            articles.append({"url": full_url})

        # Add entries from article_date_map (used in SSE API)
        article_date_map = getattr(self, "article_date_map", {})
        if response.url in article_date_map:
            meta = article_date_map[response.url]
            articles.append({
                "url": response.url,
                "title": meta.get("title", ""),
                "date": meta.get("date", "")
            })

        return articles

    def get_href(self, entry) -> str:
        return entry["url"]

    def get_title(self, response, entry=None) -> str:
        if response.url.lower().endswith(".pdf"):
            title = (
                response.meta.get("title") or
                self.article_date_map.get(response.url, {}).get("title", "")
            )
            return title
        title = response.xpath('//h1[@class="tit"]/text()').get(default="").strip()
        return title

    def get_body(self, response, entry=None) -> str:
        # Return empty string as requested
        return ""

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response, entry=None) -> str:
        if response.url.lower().endswith(".pdf"):
            date = (
                response.meta.get("date") or
                self.article_date_map.get(response.url, {}).get("date", "")
            )
            return date
        date = response.xpath('//div[@class="subtit"]/p/i/text()').get(default="").strip()
        if not date:
            # Fallback date selectors
            date = response.xpath('//div[contains(@class, "subtit")]//time/text() | //span[contains(@class, "time")]/text()').get(default="").strip()
        # If still no date, use current date
        if not date:
            date = datetime.now().strftime("%Y-%m-%d")
        return date

    def get_images(self, response, entry=None) -> list:
        relative_urls = response.xpath('//div[contains(@class, "des")]//img/@src').getall()
        return [response.urljoin(url) for url in relative_urls]

    def get_authors(self, response, entry=None) -> List[str]:
        return []

    def get_next_page(self, response, current_page: Optional[int] = 1) -> Optional[str]:
        # Check if there's a next page link
        next_page_link = response.xpath('//a[contains(@class, "page-next") and not(contains(@class, "disabled"))]/@href').get()
        return next_page_link

    def get_page_flag(self) -> bool:
        return True  # Enable pagination

    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        # For Tiantanbio website
        if "tiantanbio.com" in start_url:
            next_page_link = response.xpath('//a[contains(@class, "page-next")]/@href').get()
            if next_page_link:
                full_next_url = response.urljoin(next_page_link)
                yield scrapy.Request(
                    url=full_next_url,
                    callback=self.parse_intermediate,
                    headers=self.headers,
                    meta={
                        "start_url": start_url,
                    }
                )
            return
        
        # For SSE API
        api_data = self.api_start_urls.get(start_url)
        if not api_data or api_data is None:
            return
            
        api_url = api_data["url"]
        next_page = int(current_page) + 1
        payload = response.meta.get("payload", api_data["payload"].copy())
        payload["pageHelp.pageNo"] = next_page
        full_api_url = f"{api_url}?{urllib.parse.urlencode(payload)}"

        yield scrapy.Request(
            url=full_api_url,
            method="GET",
            headers=self.headers,
            callback=self.parse_intermediate,
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "payload": payload,
                "current_page": next_page,
            }
        )
    
    # Implementing required abstract methods that were missing
    
    def get_meta(self, response, entry=None) -> dict:
        """Extract meta information from the article"""
        return None
    
    def get_pdf(self, response, entry=None):
        """Extract PDF content if available"""
        if response.url.lower().endswith(".pdf"):
            return response.body
        return None
    
    def get_document_urls(self, response, entry=None) -> list:
        """Return a list of PDF URLs"""
        if response.url.lower().endswith(".pdf"):
            return [response.url]
        
        # Look for PDF links in the content
        pdf_links = response.xpath('//a[contains(@href, ".pdf")]/@href').getall()
        return [response.urljoin(url) for url in pdf_links]
    
    def extract_articles_with_dates(self, response):
        article_date_map = {}
        rows = response.xpath('//table[@class="table search_"]//tr[contains(@class, "modal_pdf_list")]')

        for row in rows:
            url = row.xpath('.//a[@class="pdf-first"]/@href').get()
            title = row.xpath('.//a[@class="pdf-first"]/@title').get()
            date = row.xpath('./td[last()]/text()').get()

            if url and title and date:
                full_url = response.urljoin(url)
                article_date_map[full_url] = {
                    "title": title.strip(),
                    "date": date.strip()
                }

        return article_date_map
    
    def scrap_article(self, article, response):
        entry = response.request.meta.get("entry", None)

        article["title"] = response.meta.get("title", "")
        article["body"] = ""
        article["date"] = response.meta.get("date", self.run_date.strftime("%Y-%m-%d"))
        article["authors"] = []
        article["images"] = []
        article["meta"] = None
        article["subhead"] = None
        article["pdf_urls"] = [response.url] if response.url.endswith(".pdf") else []
        article["start_url"] = response.meta.get("start_url", "")
        article["country"] = self.country
        article["language"] = self.language

        try:
            dt = datetime.strptime(article["date"], self.date_format())
            article["date"] = pytz.timezone(self.timezone).localize(dt).timestamp()
        except Exception as e:
            article["date"] = self.run_date.timestamp()

        article["inserted"] = datetime.now().timestamp()
        article["article_url"] = response.url
        article["newspaper"] = self.name
        article["news_line"] = self.source_type

        return {
            "success": True,
            "value": article
        }
    
    def parse_article(self, response):
        _id = response.url
        start_url = response.meta.get("start_url", "")

        article = {}
        article["id"] = _id
        article["source"] = ""
        article["title"] = response.meta.get("title", "")
        article["body"] = ""
        article["images"] = []
        article["authors"] = []
        article["meta"] = None
        article["subhead"] = None
        article["pdf_urls"] = [response.url]
        article["start_url"] = start_url
        article["country"] = self.country
        article["language"] = self.language
        article["article_url"] = response.url
        article["newspaper"] = self.name
        article["news_line"] = self.source_type

        # Parse and format date
        from datetime import datetime
        import pytz
        date_str = response.meta.get("date", self.run_date.strftime(self.date_format()))
        try:
            date_obj = datetime.strptime(date_str, self.date_format())
            tz = pytz.timezone(self.timezone)
            article["date"] = tz.localize(date_obj).timestamp()
        except Exception as e:
            article["date"] = self.run_date.timestamp()

        article["inserted"] = datetime.now().timestamp()

        self.crawler.stats.inc_value("articles_successfully_scraped")

        yield article
