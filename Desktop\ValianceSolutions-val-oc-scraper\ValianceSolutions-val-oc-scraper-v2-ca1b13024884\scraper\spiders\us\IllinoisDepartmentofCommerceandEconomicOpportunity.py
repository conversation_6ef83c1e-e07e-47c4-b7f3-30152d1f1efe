from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import scrapy
import json

class IllinoisDepartmentofCommerceandEconomicOpportunity(OCSpider):
    name = 'IllinoisDepartmentofCommerceandEconomicOpportunity'
    
    country  = "US"

    start_urls_names = {
        'https://www.illinois.gov/search-results.html?q=&contentType=news':'News',
    }
    
    api_start_url = {
        'https://www.illinois.gov/search-results.html?q=&contentType=news': {
            'url': 'https://doit-web-content-management.ent.us-east4.gcp.elastic-cloud.com/api/as/v1/engines/soi-allproperties/search',
            'payload':{
                "query":"",
                "page":{"size":10,"current":1},
                "filters":{"contenttype": ["news"]},
            },
        }
    }

    def parse_intermediate(self, response):
        start_url = response.meta.get('start_url')
        api_data = self.api_start_url.get(start_url)
        api_url = api_data["url"]
        payload = api_data["payload"]
        current_page = payload["page"]["current"]
        payload["page"]["current"] = current_page
        yield scrapy.Request(
            url=api_url,
            method="POST",
            body=json.dumps(payload),
            headers={
                "Authorization": "Bearer search-6vsm2asko372xhcp4ttjjm1e",
                "Content-type": "application/json"
                },
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "payload": payload,
                "current_page": current_page
            },
            dont_filter=True,
        )
   
    @property
    def source_type(self) -> str:
        return "IndustryAssociation"
    
    @property
    def timezone(self) -> str:
        return "America/Chicago"
    
    def get_articles(self, response) -> list:
        try:
            data = json.loads(response.text) 
            articles = data.get("results", [])  
            article_urls = [article.get("path", {}).get("raw", "") for article in articles if article.get("path")]
            return article_urls
        except json.JSONDecodeError:
            return []
    
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//h1//text()").get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='cmp-text ']//p//text() | //div[@class='cmp-text ']//div//text() | //div[@class='cmp-text ']//ul//li//text()").getall())
    
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return '%A, %B %d, %Y'

    def get_date(self, response) -> str:
        date = response.xpath("//span[@class='template__sub-title lable-font-style']//text()").get()
        date = date.split("- ", 1)[-1]
        return date
    
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        current_page = int(response.meta.get("current_page"))
        if current_page:
            next_page = current_page + 1
            return next_page
        return None
        
    def go_to_next_page(self, response, start_url=None, current_page=None):
        api_url = response.meta.get("api_url")
        payload = response.meta.get("payload")
        next_page = self.get_next_page(response)
        if next_page:
            payload["page"]["current"] = next_page
            yield scrapy.FormRequest(
                url=api_url,
                method='POST',
                body=json.dumps(payload),
                headers={
                    "Authorization": "Bearer search-6vsm2asko372xhcp4ttjjm1e",
                    "Content-type": "application/json"
                        },
                callback=self.parse_intermediate,
                meta={
                    "start_url": start_url,
                    "api_url": api_url,
                    "payload": payload,
                    "current_page": next_page
                }
            )
        else:
            return