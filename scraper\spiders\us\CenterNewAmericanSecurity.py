from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class CenterNewAmericanSecurity(OCSpider):
    name = "CenterNewAmericanSecurity"
    
    country = "US"

    start_urls_names = {
        "https://www.cnas.org/reports" : "Reports"
    }
    
    charset = "utf-8"

    @property
    def language(self): 
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "US/Eastern"
    
    def get_articles(self, response) -> list:
        return response.xpath('//li[@class="-with-image"]/a/@href').getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="page-title"]//text()').get()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@id="mainbar"]//p/text()').getall()) 
        
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%B %d, %Y"
    
    def get_date(self, response) -> str:
        return response.xpath('//div[@class="attribution-block"]//p/text()').get()
        
    def get_authors(self, response):
        return response.xpath('//a[@class="contributor"]//text()').getall()
    
    def get_page_flag(self) -> bool:
        return False
     
    def get_next_page(self, response): 
        return response.xpath('//a[span[contains(@class, "screenreader-only") and text()="Next Page"]]/@href').get()