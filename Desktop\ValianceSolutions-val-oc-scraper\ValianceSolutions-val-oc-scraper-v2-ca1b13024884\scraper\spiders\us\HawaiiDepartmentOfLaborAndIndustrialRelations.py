from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime

class HawaiiDepartmentOfLaborAndIndustrialRelationsn(OCSpider):
    name = "HawaiiDepartmentOfLaborAndIndustrialRelations"

    country = "US"

    start_urls_names = {
        "https://labor.hawaii.gov/blog/category/news/": "News",
        }
        
    charset = "utf-8"

    @property
    def language(self): 
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Pacific/Honolulu"
    
    def get_articles(self, response) -> list:
        return response.xpath('//article//h2/a/@href').getall()
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="container"]/h1/text()').get()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@id="container_main"]//text()').getall()) 
        
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return "%m-%d-%Y"
    
    def get_date(self, response) -> str:
        date_str = (response.xpath('//div[@id="container_main"]//p[strong[text()="FOR IMMEDIATE RELEASE"]]/following-sibling::p[1]/text() |//div[@class="sub-section published-date text-uppercase"]/text()[1]').get()).strip()
        return datetime.strptime(date_str, "%B %d, %Y").strftime("%m-%d-%Y")
            
    def get_authors(self, response):
        return response.xpath('//div[@id="container_main"]//p[preceding-sibling::p[strong[text()="STATE OF "]] and following-sibling::p[strong[text()="FOR IMMEDIATE RELEASE"]]]/strong/text()').getall()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        next_page = response.xpath('//a[@class="next page-numbers"]/@href').get() # Pagination not working right now, from page 2 it is showing" There has been a critical error on this website"
        return next_page