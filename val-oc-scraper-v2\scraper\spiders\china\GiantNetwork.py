import json
from urllib.parse import urlencode
from scraper.OCSpider import <PERSON>CSpider
from datetime import datetime
import scrapy
from scraper.utils.helper import body_normalization
from typing import Optional

class GiantNetwork(OCSpider):
    name="GiantNetwork"

    start_urls_names={
        "https://www.ztgame.com/news" : "学会动态", 
        "https://www.ga-me.com/news?year={current_year}" : "媒体信息",
        "http://www.cninfo.com.cn/new/disclosure/stock?orgId=9900018280&stockCode=002558#latestAnnouncement" : "最新公告", 
    }
    
    charset = "utf-8"

    api_start_urls = {
        "https://www.ztgame.com/news": {
            "url": "https://ucmsv2api.ztgame.com/api/news/list?site=ztgamecom&type=news&page=1&per_page=7",
            "payload": {
                "site:" : "ztgamecom",
                "type" : "news",
                "page" : "1",
                "per_page" : "7",
            }
        },
        "https://www.ga-me.com/news": {
        },
        "http://www.cninfo.com.cn/new/disclosure/stock?orgId=9900018280&stockCode=002558#latestAnnouncement": {
            "url": "http://www.cninfo.com.cn/new/hisAnnouncement/query",
            "payload": {
               "stock": "002558,9900018280",
                "tabName": "fulltext",
                "pageSize": "30",
                "pageNum": "1",
                "column": "szse",
                "category": "",
                "plate": "sz",
                "seDate": "",
                "searchkey": "",
                "secid": "",
                "sortName": "",
                "sortType": "",
                "isHLtitle": "True"
            },
        },
    }

    @property
    def source_type(self) -> str:
        return 'IndustryAssociation'

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_data = self.api_start_urls.get(start_url)
        if api_data is None:
            current_year = response.meta.get("current_year", datetime.now().year)
            url = start_url.format(current_year=current_year)
            yield scrapy.Request(
            url = url,
            method = "GET",
            dont_filter=True,
            callback=self.parse,
            meta={
                "start_url": start_url,
                "url": url,
                "current_year": current_year,
            },
        )
            return 
        payload = api_data["payload"]
        if "page" in payload and payload["page"]:
            payload["page"] = payload.get("page")
            yield scrapy.Request(
                url=f"https://ucmsv2api.ztgame.com/api/news/list?site=ztgamecom&type=news&page={payload['page']}&per_page=7",
                method = "GET",
                    headers={
                        'Content-Type': 'application/json',
                    },
                    body = json.dumps(payload),
                    callback = self.parse,   
                    meta={
                        "start_url": start_url,
                        "api_url": api_data["url"],
                        "payload": payload,
                        "current_page": payload["page"]
                    }
            )
        else:
            payload["pageNum"] = payload.get("pageNum")
            api_url = api_data["url"]
            yield scrapy.FormRequest(
            url= api_url,
            method = "POST",
            headers={
                "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
                "User-Agent": "Mozilla/5.0", 
            },
            dont_filter=True,
            formdata = payload,
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_data["url"],
                "payload": payload,
                "page": payload["pageNum"],
            },
        )    
    
    article_to_pdf_urls_mapping={}

    def get_articles(self, response) -> list:
        try:
            data = json.loads(response.text)
            # Case: JSON with 'data' -> 'announcements' for 3rd start_url
            if "announcements" in data:
                urls = self.article_pdf_mapping(data.get("announcements", []))
                return urls
            # Case: JSON with 'data' -> 'list' for 1st start_url
            elif "data" in data:
                articles = data.get("data", {}).get("list", [])
                return [self.construct_article_url(article) for article in articles if article]
        except Exception as e:
            pass
        # Case : For 2nd start_url directly extracting articles form the starting page
        try:
            articles = response.xpath('//li[@class="txtItem inlineblock"]//a[1]/@href').getall()
            if articles :
                return articles
        except Exception as  e:
            pass
        
    def get_href(self, entry) -> str:
        return entry   

    def get_title(self, response) -> str:
        title = (
            response.xpath('//div[@class="news-title"]/strong/text()').get() or
            response.xpath('//div[@class="txtBox ct1"]/h3/text()').get()
        )
        if not title:
            mapping_entry = self.article_to_pdf_urls_mapping.get(response.url)
            if mapping_entry:
                title = mapping_entry.get("title")
        return title
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="news-content"]//p/text()').getall() or response.xpath('//div[@class="pageContent"]//p//text()').getall())    

    def get_images(self, response) -> list:
        return response.xpath('//div[@class="news-content"]//img/@src').getall() or response.xpath('//div[@class="pageContent"]//img/@src').getall()
    
    def get_document_urls(self, response, entry=None)->list :
        try:
            # Child articles do not have direct pdf hrefs for 3rd url
            mapping_entry = self.article_to_pdf_urls_mapping.get(response.url)
            if mapping_entry:
                pdf_urls = mapping_entry.get("pdf_urls")
            if pdf_urls:
                return [pdf_urls]
            else:
                return None
        except Exception as e:
            pass
    
    def get_authors(self, response, entry=None) -> list[str]:
        return []

    def date_format(self) -> str:
        return "%Y-%m-%d %H:%M:%S"

    def get_date(self, response) -> str:
        try :
            date = response.xpath('//span[@class="time"]//text()').get()
            if date:
                return date
        except Exception as e:
            pass
        try :
            date = response.xpath('//div[@class="nowTime"]//text()').get().strip()
            if date:
                date_obj = datetime.strptime(date, "%Y.%m.%d")
                formatted_date = date_obj.strftime("%Y-%m-%d %H:%M:%S")
                return formatted_date
        except Exception as e:
            pass
        try :
            mapping_entry = self.article_to_pdf_urls_mapping.get(response.url)
            if mapping_entry:
                date = mapping_entry.get("date")
                if date:
                    date_obj = datetime.strptime(date, "%Y-%m-%d %H:%M")
                    formatted_date = date_obj.strftime("%Y-%m-%d %H:%M:%S")
                    return formatted_date
            return date
        except Exception as e:
            pass
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        start_url =response.meta.get("start_url")
        try :
            if start_url =="https://www.ztgame.com/news":
                current_page=response.meta.get('current_page')
                total_pages = int(response.json().get('data', {}).get("last_page", 0))
                return str(int(current_page) + 1) if int(current_page) < total_pages else None
            elif start_url =="http://www.cninfo.com.cn/new/disclosure/stock?orgId=9900018280&stockCode=002558#latestAnnouncement":
                page=response.meta.get('page')
                if response.status != 200:
                    return None
                return str(int(page) + 1)
        except Exception as e:
            pass
        try :
            current_year = response.meta.get("current_year")
            if response.status != 200:
                return None
            return str(int(current_year) - 1)
        except Exception as e:
            pass
        

    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        start_url = response.meta.get("start_url")
        api_url = response.meta.get("api_url")
        next_page = self.get_next_page(response)
        # Case 1: If no api_url (for 2nd URL)
        if not api_url:
            url = start_url.format(current_year=next_page)  # Format the URL with the next page
            yield scrapy.Request(
                url=url,
                method='GET',
                callback=self.parse_intermediate, 
                dont_filter=True,
                meta={
                    'current_year': next_page, 
                    'start_url': start_url,
                }
            )
        # Case 2: If it's the first URL ("https://www.ztgame.com/news")
        elif start_url == "https://www.ztgame.com/news":
            payload = response.meta.get('payload')
            payload['page'] = next_page  # Update the page number in the payload
            yield scrapy.Request(
                url=f"https://ucmsv2api.ztgame.com/api/news/list?site=ztgamecom&type=news&page={payload['page']}&per_page=7",
                method='GET',
                body=json.dumps(payload),
                headers={
                    "Content-Type": "application/x-www-form-urlencoded;",
                },
                callback=self.parse_intermediate,
                dont_filter=True,
                meta={
                    "api_url": api_url,
                    'current_page': next_page, 
                    'start_url': start_url,
                    'payload': payload,
                }
            )
        # Case 3: If it's the third URL (other URLs with 'pageNum')
        else:
            payload = response.meta.get('payload')
            payload['pageNum'] = next_page  # Update the pageNum to the next page number
            yield scrapy.FormRequest(
                url=api_url,
                method="POST",
                headers={
                    "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
                    "User-Agent": "Mozilla/5.0",
                },
                dont_filter=True,
                formdata=payload,
                callback=self.parse_intermediate,
                meta={
                    "start_url": start_url,
                    "api_url": api_url,
                    "payload": payload,
                    "page": next_page,
                },
            )

        # start_url = response.meta.get("start_url")
        # api_url = response.meta.get("api_url")
        # next_page = self.get_next_page(response)
        # if not api_url: #for 2nd url 
        #     url = start_url.format(current_year=next_page)
        #     yield scrapy.Request(
        #         url= url,
        #         method='GET',
        #         # headers={
        #         #     "Content-Type": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;",
        #         # },
        #         callback=self.parse_intermediate, 
        #         dont_filter=True,
        #         meta={
        #                 'current_year': next_page, 
        #                 'start_url': start_url,
        #             }
        #     )
        # # next_page = self.get_next_page(response, current_page)
        # else:
        #     if start_url =="https://www.ztgame.com/news": #for 1st url 
        #         payload = response.meta.get('payload')
        #         payload['page'] = next_page
        #         yield scrapy.Request(
        #             url=api_url,
        #             method='GET',
        #             body = json.dumps(payload),
        #             headers={
        #                 "Content-Type": "application/x-www-form-urlencoded;",
        #             },
        #             callback=self.parse_intermediate,
        #             dont_filter=True,
        #             meta={
        #                     "api_url": api_url,
        #                     'current_page': next_page, 
        #                     'start_url': start_url,
        #                     'payload': payload,
        #                 }
        #         )
        #     else:
        #         payload = response.meta.get('payload') # for 3rd url
        #         payload["pageNum"] = payload.get("pageNum")
        #         print(payload)
        #         payload['pageNum'] = next_page
        #         print(payload['pageNum'])
        #         # api_url = api_data["url"]
        #         yield scrapy.FormRequest(
        #         url= api_url,
        #         method = "POST",
        #         headers={
        #             "Content-Type": "application/x-www-form-urlencoded;charset=utf-8",
        #             "User-Agent": "Mozilla/5.0", 
        #         },
        #         dont_filter=True,
        #         formdata = payload,
        #         callback=self.parse_intermediate,
        #         meta={
        #             "start_url": start_url,
        #             "api_url": api_url,
        #             "payload": payload,
        #             "page": next_page,
        #         },
        #     )    
                    
        # else:
        #     yield None
                    
    def construct_article_url(self, article):
        article_id = article.get('defaulturl')
        if article_id:
            return f"https://www.ztgame.com/news/{article_id}"   
        else:
            return None
        
    def article_pdf_mapping(self, announcements: list) -> list:
        urls = []
        mapping = {}

        for item in announcements:
            plate = item.get("pageColumn", "szse")
            org_id = item.get("orgId")
            stock_code = item.get("secCode")
            ann_id = item.get("announcementId")
            ann_time_raw = item.get("announcementTime")

            if ann_time_raw:
                ann_time_str = datetime.fromtimestamp(ann_time_raw / 1000).strftime("%Y-%m-%d %H:%M")
            else:
                ann_time_str = ""

            query_params = {
                "plate": plate,
                "orgId": org_id,
                "stockCode": stock_code,
                "announcementId": ann_id,
                "announcementTime": ann_time_str
            }

            url = f"http://www.cninfo.com.cn/new/disclosure/detail?{urlencode(query_params)}"
            urls.append(url)

            mapping[url] = {
                "title": item.get("announcementTitle"),
                "date": ann_time_str,
                "pdf_urls": url
            }

        # Update the spider-wide mapping only here
        if mapping:
            self.article_to_pdf_urls_mapping.update(mapping)

        return urls

        