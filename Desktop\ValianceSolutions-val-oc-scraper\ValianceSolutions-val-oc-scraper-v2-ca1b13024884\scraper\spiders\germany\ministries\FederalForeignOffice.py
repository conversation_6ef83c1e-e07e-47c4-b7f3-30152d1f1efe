import re
from typing import List, Union
from bs4 import BeautifulSoup
from urllib.parse import urlparse, parse_qs
import scrapy
import json
import urllib.parse
from urllib.parse import urlparse, parse_qs, urljoin
from scraper.middlewares import HeadlessBrowserProxy
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization


class FederalForeignOffice(OCSpider):
    name = "FederalForeignOffice"
    country = "German"

    start_urls_names = {
        "https://www.auswaertiges-amt.de/ajax/json-filterlist/de/newsroom/presse/web-archiv/aktuell/-/216676?limit=10&offset=0": "",
        "https://www.auswaertiges-amt.de/ajax/json-filterlist/de/newsroom/presse/newsroom-archiv/-/609192?limit=20&offset=0": ""
    }

    def parse_intermediate(self, response):
        # Pattern for websites where the landing page alone has JS rendering
        hbp = HeadlessBrowserProxy()
        request = scrapy.Request(hbp.get_proxy(response.url, timeout=10000), callback=self.parse
        )
        request.meta['start_url'] = response.request.meta['start_url']
        yield request

    @property
    def source_type(self) -> str:
        return "ministry"

    def get_page_flag(self) -> bool:
        return False

    @property
    def timezone(self):
        return "Europe/Berlin"

    def get_articles(self, response) -> list:
        soup = BeautifulSoup(response.text, 'html.parser')

        # Step 2: Extract the JSON string from the <pre> tag
        json_data_str = soup.find('pre').text

        # Step 3: Convert the JSON string into a Python dictionary
        json_data = json.loads(json_data_str)
        articles = [i['link'] for i in json_data['items']]

        parsed_url = urlparse(response.url)
        # Extract the 'url' parameter from the query string
        query_params = parse_qs(parsed_url.query)
        extracted_url = query_params.get('url', [None])[0]
        articles = [urljoin(extracted_url, i) for i in articles]
        print(articles)
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response, entry=None) -> str:
        return response.xpath('//h1/span/text()').get().strip()

    def get_body(self, response, entry=None) -> str:
        # This website only contains pdfs and titles
        return body_normalization(response.xpath('//div[@class="c-quote--default"]//p/text()').getall())

    def date_format(self) -> str:
        return "%d-%m-%Y"

    def get_document_urls(self, response, entry=None):
        return [i for i in response.xpath('//a/@href').extract() if '.pdf' in i]

    def get_date(self, response, entry=None) -> str:
        return response.xpath('//span[@class="heading__meta"]/text()').get().strip().split(' ')[0].replace('.', '-')


    def get_images(self, response, entry=None) -> List[str]:
        return []

    def get_authors(self, response, entry=None):
        text = " ".join(response.xpath('//div[@class="sc-1779wej-2 ZMxLI"]//text()').getall())
        author_pattern = r"Urheber:\s*([A-Za-zäöüß\s]+)"

        # Search for the author in the text
        match = re.search(author_pattern, text)

        if match:
            # Extract the author
            author = match.group(1).strip()
        else:
            author = ""
        return author

    def get_next_page(self, response) -> Union[None, str]:
        parsed_url = urllib.parse.urlparse(response.url)
        query_params = urllib.parse.parse_qs(parsed_url.query)
        encoded_url = query_params['url'][0]

        # Decode the URL
        decoded_url = urllib.parse.unquote(encoded_url)

        current_offset = int(decoded_url.split('&offset=')[-1])
        new_offset = current_offset + 20
        new_page_url = decoded_url.replace(f"&offset={current_offset}", f"&offset={new_offset}")
        return new_page_url

    def go_to_next_page(self, response, start_url, current_page=None):
        next_page = self.get_next_page(response)
        if next_page is not None:
            next_page = response.urljoin(next_page)
            request = response.request.replace(url=next_page, callback=self.parse_intermediate)
            request.meta['start_url'] = start_url
            request.meta['current_page'] = int(response.request.meta.get('current_page', 1)) + 1
            yield request
        else:
            yield None
