from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import re

class ShanxiXinghuacunFenjiuDistillery(OCSpider):
    name = "ShanxiXinghuacunFenjiuDistillery"

    start_urls_names = {
        "https://www.fenjiu.com.cn/gf/corporateNews/index.html" : "新闻资讯",
        "https://www.fenjiu.com.cn/gf/media/index.html" : "新闻资讯",
        "https://www.fenjiu.com.cn/gf/disclosure/index.html" : "信息披露" 
    }

    charset = "iso-8859-1"

    article_data_map ={}

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"

    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response):
            articles = []
            for article in response.xpath("//div[@class='inner']//ul[@class='news_list clearfix position_r']/li"):
                url = article.xpath(".//a//@data-href | .//a/@href").get()
                title = article.xpath(".//div[@class='news_title']//text()").get()
                if url and title :
                    full_url = url
                    title = title.strip()
                    if '.pdf' in full_url.lower():
                        pdf = full_url
                    else:
                        pdf = "None"
                    self.article_data_map[full_url] = {"title": title, "pdf": pdf}
                    articles.append(full_url) 
            return articles
    
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response, entry=None) -> str:
        if ".pdf" in response.url.lower():
            return ""
        return body_normalization(response.xpath('//div[@class="news_detail_content_text"]//text()').getall())
      
    def get_images(self, response) -> list:
        if ".pdf" in response.url.lower():
            return ""
        return response.xpath('//div[@class="news_detail_content_text"]//img/@src').getall()
        
    def date_format(self) -> str:
        return"%Y-%m-%d"
    
    def get_date(self, response) -> str:
        match = re.search(r'/(\d{4}-\d{2}-\d{2})/', response.url)
        if match:
            date = match.group(1)
            return date
    
    def get_authors(self, response):
        return ""
    
    def get_document_urls(self, response, entry=None) -> list:
        pdf= self.article_data_map.get(response.request.meta.get('entry'), {}).get("pdf", "").replace("..","")
        if pdf!='None':
            return  f'https://www.fenjiu.com.cn/gf{pdf}'
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        if response.status == 404:
            return None
        current_url = response.url
        if "index.html" in current_url:
            next_page_url = current_url.replace("index.html", "index_2.html")
        else:
            import re
            match = re.search(r"index_(\d+)\.html", current_url)
            if match:
                current_page_num = int(match.group(1))
                next_page_num = current_page_num + 1
                next_page_url = current_url.replace(
                    f"index_{current_page_num}.html",
                    f"index_{next_page_num}.html"
                )
            else:
                return None
        return next_page_url