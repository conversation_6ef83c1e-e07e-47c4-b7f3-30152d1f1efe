from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import scrapy
from typing import List, Union
import json
import logging

class ChinaAssociationDevelopmentZones(OCSpider):
    name = 'ChinaAssociationDevelopmentZones'
    
    custom_settings = {
        "DOWNLOAD_DELAY": 2,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }
    
    HEADLESS_BROWSER_WAIT_TIME = 20000
    
    start_urls_names = {
        'https://www.cadz.org.cn/articleList/20':'国家级开发区动态',
        'https://www.cadz.org.cn/articleList/21':'省级开发区动态',
        'https://www.cadz.org.cn/articleList/26':'政策法规'
    }
    
    api_start_url = {
        'https://www.cadz.org.cn/articleList/20': {
            'url': 'https://www.cadz.org.cn/api/tpWenz/indexlist',
            'payload': {
                "pageSize": "23",
                "pageNum": "1",
                "id": "20"
            },
        },
        'https://www.cadz.org.cn/articleList/21': {
            'url': 'https://www.cadz.org.cn/api/tpWenz/indexlist',
            'payload': {
                "pageSize": "23",
                "pageNum": "1",
                "id": "21"
            },
        },
        'https://www.cadz.org.cn/articleList/26': {
            'url': 'https://www.cadz.org.cn/api/tpWenz/indexlist',
            'payload': {
                "pageSize": "23",
                "pageNum": "1",
                "id": "26"
            }
        }
    } 

    def parse_intermediate(self, response):
        start_url = response.meta.get('start_url')
        api_data = self.api_start_url[start_url]
        api_url = api_data["url"]
        if not api_url:
            self.logger.error(f"No API configuration found for start_url: {start_url}")
        else:
            self.logger.info(f"Fetching API URL: {api_url}")
        current_page = response.meta.get("current_page", 1)
        api_data["payload"]["pageNum"] = str(current_page)
        payload = api_data["payload"]
        headers = {
            "Content-Type": "application/x-www-form-urlencoded"
        }
        yield scrapy.FormRequest(
            url=api_url,
            method="POST",
            headers=headers,
            dont_filter=True,
            formdata=payload,
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_data["url"],
                "payload": payload,
                "current_page": current_page
            },
        )
        
    @property
    def source_type(self) -> str:
        return "IndustryAssociation"
    
    @property
    def timezone(self): 
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        try:
            data = response.json()
            article_list = data["data"]["records"]
            ids = [article["id"] for article in article_list]
            articles = [f"https://www.cadz.org.cn/article/{article_id}" for article_id in ids]
            return articles
        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to decode JSON: {e}")
            return []

    def get_href(self, entry) -> str:
        return entry

    def get_title(self,response) -> str:
        return response.xpath("//div[@class='article_title']//text()").get() 
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='article_content']//p//text()").getall())
    
    def get_images(self, response) -> List[str]:
        return response.xpath("//p//img/@src").extract()
    
    def date_format(self) -> str:
        return '%Y-%m-%d'

    def get_date(self, response) -> str:
        return response.xpath("//div[@class='article_actionBar']//span//text()").re_first(r"\d{4}-\d{2}-\d{2}")
        
    def get_authors(self, response):
        return []
    
    def get_document_urls(self, response, entry=None) -> list:
        return response.xpath("//p//a/@href").extract() 
    
    def get_next_page(self, response, current_page) -> Union[None, str]:
        data = response.json() 
        total_pages = data["data"]["pages"]
        if current_page < total_pages:
            return current_page + 1
        else:
            return None
        
    def get_page_flag(self) -> bool:
        return False

    def go_to_next_page(self, response, start_url, current_page = 1):
        api_url = response.meta.get("api_url")
        if not api_url: 
            logging.info("API URL not found in meta data.")
            return
        next_page = self.get_next_page(response, current_page)
        if next_page:
            yield scrapy.Request(
                url=api_url,
                callback=self.parse_intermediate,
                meta={'current_page': next_page, 'start_url': start_url}
            )
        else:
            logging.info("No more pages to fetch.")
            yield None