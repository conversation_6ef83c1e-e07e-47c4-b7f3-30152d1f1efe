from datetime import datetime
from typing import List, Union
import scrapy
import urllib.parse
from urllib.parse import urlparse, parse_qs, urljoin
from scraper.middlewares import HeadlessBrowserProxy
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

month_translation = {
    'Januar': 'January', 'Februar': 'February', 'März': 'March', 'April': 'April',
    'Mai': 'May', 'Juni': 'June', 'Juli': 'July', 'August': 'August', 'September': 'September',
    'Oktober': 'October', 'November': 'November', 'Dezember': 'December'
}

# Use this type of pattern if you want to use headless browser only for the start url
# copy the parse_intermediate method and use it in your scraper

class GermanyGovtSpeeches(OCSpider):
    name = "GermanyGovtSpeeches"
    country = "German"

    start_urls_names = {
        "https://www.bundesregierung.de/breg-de/service/newsletter-und-abos/bulletin?page=0": ""
    }

    def parse_intermediate(self, response):
        # Pattern for websites where the landing page alone has JS rendering
        hbp = HeadlessBrowserProxy()
        request = scrapy.Request(hbp.get_proxy(response.url, timeout=10000), callback=self.parse
        )
        request.meta['start_url'] = response.request.meta['start_url']
        yield request

    @property
    def source_type(self) -> str:
        return "ministry"

    def get_page_flag(self) -> bool:
        return False

    @property
    def timezone(self):
        return "Europe/Berlin"

    def get_articles(self, response) -> list:
        articles = response.xpath(
            '//ol[@class="bpa-search-result-list"]/li//div[@class="bpa-teaser-text-wrapper"]/a/@href').getall()
        parsed_url = urlparse(response.url)
        # Extract the 'url' parameter from the query string
        query_params = parse_qs(parsed_url.query)
        extracted_url = query_params.get('url', [None])[0]
        articles = [urljoin(extracted_url, i) for i in articles]
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_document_urls(self, response, entry=None):
        return [i for i in response.xpath('//a/@href').extract() if '.pdf' in i]

    def get_title(self, response, entry=None) -> str:
        return body_normalization(response.xpath('//h2/span[@class="bpa-title"]//text()').getall())

    def get_body(self, response, entry=None) -> str:
        return body_normalization(response.xpath('//div[@class="bpa-main bpa-first-section"]/div[@class="bpa-container"]//div[@class="bpa-richtext"]//text()').getall())

    def date_format(self) -> str:
        return "%Y-%m-%dT%H:%M:%SZ"

    def get_date(self, response, entry=None) -> str:
        date = response.xpath('//span[@class="bpa-time"]/time/@datetime').get()
        if not date:
            date = response.xpath('//ul[@class="bpa-collection-list"]/li/text()')[-1].get().split(',')[-1].strip().replace('.','')
            for de_month, en_month in month_translation.items():
                if de_month in date:
                    date = date.replace(de_month, en_month)

            date_obj = datetime.strptime(date, "%d %B %Y")
            formatted_date = date_obj.strftime("%Y-%m-%dT%H:%M:%SZ")
            date = formatted_date
        return date

    def get_images(self, response, entry=None) -> List[str]:
        images = []
        for img_src in response.xpath('//div[@class="bpa-main bpa-first-section"]//img/@src').getall():
            if img_src.endswith('.jpg') or img_src.endswith('.png'):
                images.append(response.urljoin(img_src))
        return images

    def get_authors(self, response, entry=None):
        return []

    def get_next_page(self, response) -> Union[None, str]:
        parsed_url = urllib.parse.urlparse(response.url)
        query_params = urllib.parse.parse_qs(parsed_url.query)
        encoded_url = query_params['url'][0]

        # Decode the URL
        decoded_url = urllib.parse.unquote(encoded_url)

        current_page = int(decoded_url.split('=')[-1])
        next_page = current_page + 1
        next_page_url = decoded_url.split('page')[0] + 'page=' + str(next_page)
        return next_page_url

    def go_to_next_page(self, response, start_url, current_page=None):
        next_page = self.get_next_page(response)
        if next_page is not None:
            next_page = response.urljoin(next_page)
            request = response.request.replace(url=next_page, callback=self.parse_intermediate)
            request.meta['start_url'] = start_url
            request.meta['current_page'] = int(response.request.meta.get('current_page', 1)) + 1
            yield request
        else:
            yield None
