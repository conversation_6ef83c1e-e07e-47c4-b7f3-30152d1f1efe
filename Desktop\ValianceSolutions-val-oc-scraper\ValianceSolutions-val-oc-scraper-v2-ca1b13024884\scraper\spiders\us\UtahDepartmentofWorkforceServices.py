from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime 

class UtahDepartmentofWorkforceServices(OCSpider):
    name = "UtahDepartmentofWorkforceServices"

    country = "US"

    start_urls_names = {
        "https://jobs.utah.gov/department/press.html": "Press Release",
        }

    charset = "utf-8"

    proxy_country = "us"

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES":
            {
                # for using geo targeted proxy, add this middleware
                'scraper.middlewares.GeoProxyMiddleware': 350,
            },
        "DOWNLOAD_DELAY": 4,
    }

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "America/Denver"
    
    def get_articles(self, response) -> list:
        return response.xpath('//ul/li/a[starts-with(@href, "/department/press")]/@href').getall()[0:2]

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h2/text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//p[@dir="ltr"]//text()').getall())

    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return"%m/%d/%Y"
    
    def get_date(self, response) -> str:
        return datetime.strptime(
            response.xpath('//h1/br/following-sibling::text()').get().strip(), "%B %d, %Y").strftime("%m/%d/%Y")
    
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]: 
        return None