from datetime import datetime
from scraper.OCSpider import <PERSON>CSpid<PERSON>
from scraper.utils.helper import body_normalization

class RhodeIslandOfficeoftheGeneralTreasurer(OCSpider):
    name = "RhodeIslandOfficeoftheGeneralTreasurer"

    country="US"

    start_urls_names = {
        "https://treasury.ri.gov/press-releases":"Press Releases",
    }

    charset = "utf-8"
    
    @property
    def language(self):
        return "English"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "US/Eastern"
    
    def get_articles(self, response) -> list:
       return response.xpath('//h3[@class="qh__teaser-article__title"]//a//@href').getall()
   
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//article[@class="node node--type-press-release node--view-mode-full"]/h1//text()').get()
       
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//article[@class="node node--type-press-release node--view-mode-full"]//span/text()').getall())
    
    def get_images(self, response, entry=None) :
        return []

    def date_format(self) -> str:
        return "%A,%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date = response.xpath('//p[@class="publish-date"]//text()').get()
        # print(date)  #For all the articles output -> Published on Friday, November 15, 2024
        date_cleaned = date.replace("Published on ", "")
        date_obj = datetime.strptime(date_cleaned, "%A, %B %d, %Y")
        formatted_date = date_obj.strftime("%A,%Y-%m-%d")
        return formatted_date
    
    def get_authors(self, response, entry=None) -> list[str]:
        return []
    
    def get_document_urls(self, response, entry=None):
        return None
    
    def get_page_flag(self) -> bool:
        return True

    def get_next_page(self, response) -> str:
        return response.xpath('//a[@title="Go to next page"]//@href').get()