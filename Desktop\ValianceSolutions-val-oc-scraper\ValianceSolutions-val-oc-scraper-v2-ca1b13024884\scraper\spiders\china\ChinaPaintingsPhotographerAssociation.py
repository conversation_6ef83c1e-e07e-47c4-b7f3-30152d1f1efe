from bs4 import BeautifulSoup
import scrapy
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ChinaPaintingsPhotographerAssociation(OCSpider):
    name = "ChinaPaintingsPhotographerAssociation"

    start_urls_names = {
        "https://www.zgjtsh.org.cn/page.do?sid=87" : "协会资讯列表",
        "https://www.zgjtsh.org.cn/page.do?sid=93" : "行业动态列表",
        "https://www.zgjtsh.org.cn/page.do?sid=94" : "业界要闻列表"
    }

    api_start_urls = {
        'https://www.zgjtsh.org.cn/page.do?sid=87': {
            "url": "https://www.zgjtsh.org.cn/page.do?sid=87",
            "payload": {
                "sid": "87",
                "pages": "5",
                "current": "1",
                "type": "list"
            },
        },
        'https://www.zgjtsh.org.cn/page.do?sid=93': {
            "url": "https://www.zgjtsh.org.cn/page.do?sid=93",
            "payload": {
                "sid": "93",
                "pages": "5",
                "current": "1",
                "type": "list"
            },
        },
        'https://www.zgjtsh.org.cn/page.do?sid=94': {
            "url": "https://www.zgjtsh.org.cn/page.do?sid=94",
            "payload": {
                "sid": "94",
                "pages": "5",
                "current": "1",
                "type": "list"
            },
        }
    }

    charset = "utf-8"

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        current_page = response.meta.get("current_page", 1)
        api_details = self.api_start_urls.get(start_url)
        if isinstance(api_details, dict):
            api_url = api_details["url"]
            payload = api_details["payload"]
            payload["current"] = str(current_page)
            yield scrapy.FormRequest(
                url=api_url,
                method="POST",
                formdata=payload,
                headers={"Content-Type": "application/x-www-form-urlencoded"},
                callback=self.parse,
                meta={
                    "current_page": current_page,
                    "start_url": start_url,
                    "api_url": api_url,
                    "payload": payload,
                },
            )
        else:
            self.logger.error(f"API details not found for {start_url}")

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"

    @property
    def timezone(self):
        return "Asia/Shanghai"        

    def get_articles(self, response) -> list:
        try:
            response_text = response.text
            soup = BeautifulSoup(response_text, "html.parser")
            list_div = soup.find("div", class_="list")
            if not list_div:
                return []
            articles = [a['href'] for a in list_div.find_all('a', href=True)]
            base_url = "https://www.zgjtsh.org.cn"
            articles = [base_url + link if link.startswith("/") else link for link in articles]
            return articles
        except Exception as e:
            self.logger.error(f"Error extracting articles: {e}")
            return []
    
    def get_href(self, entry) -> str:
        href = entry if isinstance(entry, str) else str(entry)
        return href
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="newsTitle"]/h5/text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="newsInfo"]//p//text()').getall())
    
    def get_images(self, response) -> list:
        return response.xpath('//div[@class="newsInfo"]//p//img/@src').getall()
    
    def date_format(self) -> str:
        return "%Y.%m.%d"
    
    def get_date(self, response) -> str:
        return response.xpath('//div[@class="newsTitle"]/h6//text()').re_first(r"\d{4}.\d{2}.\d{2}")
    
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response):
        current_page = int(response.meta.get("current_page", 1))
        payload = response.meta.get("payload")
        max_pages = int(payload.get("pages"))
        if current_page < max_pages:
            return str(current_page + 1)
        return None

    def go_to_next_page(self, response, start_url=None, current_page=None):
        api_url = response.meta.get("api_url")
        payload = response.meta.get("payload").copy()
        next_page = self.get_next_page(response)
        if next_page:
            payload["current"] = next_page
            yield scrapy.FormRequest(
                url=api_url,
                method="POST",
                formdata=payload,
                headers={"Content-Type": "application/x-www-form-urlencoded"},
                callback=self.parse_intermediate,
                meta={
                    "start_url": start_url,
                    "api_url": api_url,
                    "payload": payload,
                    "current_page": next_page,
                },
                dont_filter=True
            )
        else:
            self.logger.info(f"No more pages to scrape for {start_url}")