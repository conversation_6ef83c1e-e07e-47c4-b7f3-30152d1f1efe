from scraper.OCSpider import OCSpider
import scrapy
from scraper.utils.helper import body_normalization

class LouisianaconomicDevelopment(OCSpider):
    name = 'LouisianaconomicDevelopment'

    country = "US"

    start_urls_names = {
        "https://www.opportunitylouisiana.gov/news": "News",
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "US/Central"

    @property
    def language(self):
        return "English"

    def get_articles(self, response) -> list:
        return response.xpath("//a[contains(@href,'/news/')]//@href").getall()   
    
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//h1//text()").get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='singlePost__content']//p//text() | //div[@class='singlePost__content']//ul//li//text()").getall())

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%B %d, %Y"

    def get_date(self, response) -> str:
        return response.xpath("//time//text()").get()
    
    def get_authors(self, response):
        return []
    
    def get_document_urls(self, response, entry=None):
        return ""

    def get_page_flag(self) -> bool:
        return True

    def get_next_page(self, response) -> str:
        next_page = response.xpath("//a[@class='btn'][contains(text(),'More')]//@href").get()
        if next_page:
            return next_page
        return None