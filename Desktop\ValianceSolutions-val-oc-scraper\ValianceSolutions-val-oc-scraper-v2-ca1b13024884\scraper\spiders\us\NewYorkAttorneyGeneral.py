from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class NewYorkAttorneyGeneral(OCSpider):
    name = "NewYorkAttorneyGeneral"

    country="US"

    start_urls_names = {
        "https://ag.ny.gov/press-releases" : "Press Releases",
    }

    charset = "utf-8"

    @property
    def language(self) :
        return "English"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "US/Eastern"
    
    def get_articles(self, response) -> list:
        return response.xpath('//span[@class="field-content"]//@href').getall()
       
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="tw-text-xl tw-font-black tw-italic tw-text-blue tw-mt-0 md:tw-text-2xl lg:tw-text-3xl "]//text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="tw-max-w-[855px]"]//p//text()').getall())

    def get_images(self, response, entry=None) :
        return []

    def date_format(self) -> str:
        return "%B %d, %Y"
    
    def get_date(self, response) -> str:
        return response.xpath('//time//text()').get()
   
    def get_authors(self, response, entry=None) -> list[str]:
        return []
    
    def get_document_urls(self, response, entry=None):
        return response.xpath('//div[@class="tw-max-w-[855px]"]//a/@href').getall()
    
    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        return response.xpath('//a[@title="Go to next page"]/@href').get()