from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime

class CenterOnBudgetAndPolicyPriorities(OCSpider):
    name = 'CenterOnBudgetAndPolicyPriorities'

    country = "US"
    
    custom_settings = {
		"DOWNLOADER_MIDDLEWARES" : {
			'scraper.middlewares.HeadlessBrowserProxy': 350,
		},
		"DOWNLOAD_DELAY" : 5,
	}
    
    HEADLESS_BROWSER_WAIT_TIME = 30000   # 30 Seconds wait time
    
    start_urls_names = {
        'https://www.cbpp.org/comprehensive': "News"
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Central"

    def get_articles(self, response) -> list:
        return response.xpath("//h2//a//@href").getall()
    
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//h1//text() | //li[@class='breadcrumb-item active']//text()").get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='content']//p//text() | //div[@class='content']//ul//li//text()").getall())
    
    def get_images(self, response) -> list:
        return response.xpath("//div[@class='content']//p//img//@src").getall()
    
    def date_format(self) -> str:
        return "%m-%d-%Y"

    def get_date(self, response) -> str:
        date = response.xpath("//div[@class='content']//time//text() | //div[@class='field field--name-field-statement-note field--type-text field--label-hidden field__item']//text()").get(default="").strip()
        date= date.replace("CBPP Statement:","").replace("- For Immediate Release","")
        if date:
            for fmt in ["%B %d, %Y"," %B %d, %Y", " %B %d, %Y ", "%B %d, %Y, %I:%M %p"]:
                try:
                    return datetime.strptime(date, fmt).strftime("%m-%d-%Y")
                except ValueError:
                    continue  
        return date 
    
    def get_authors(self, response):
        return ""

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        next_page = response.xpath("//ul//li[@class='page-item']//a[@title='Go to next page']//@href").get()
        if next_page:
            return next_page
        else:
            return None