# from typing import Optional, List
# import logging
# import scrapy
# from scraper.OCSpider import <PERSON><PERSON><PERSON><PERSON>
# from scraper.utils.helper import body_normalization

# class SseThreesixty(OCSpider):
#     name = "SseThreesixty"
#     # country = "cn"

#     start_urls_names = {
#         "https://www.360.cn/news.html": "360 News",
#         "https://www.sse.com.cn/assortment/stock/list/info/announcement/index.shtml?productId=601360": "SSE Announcements"
#     }

#     api_start_urls = {
#         "https://www.360.cn/news.html": {
#             "url": "https://www.360.cn/n/ajaxlist?page={current_page}&type=6",
#             "payload": {"page": "1", "type": "6"}
#         }
#     }

#     @property
#     def source_type(self) -> str:
#         return "company"

#     @property
#     def language(self):
#         return "Chinese"

#     @property
#     def timezone(self):
#         return "Asia/Shanghai"

#     def parse_intermediate(self, response):
#         start_url = response.meta.get("start_url")
#         current_page = response.meta.get("current_page", 1)
#         api_data = self.api_start_urls.get(start_url)
#         api_url = api_data["url"].format(current_page=current_page)

#         yield scrapy.Request(
#             url=api_url,
#             callback=self.parse,
#             headers={"content-type": "application/json"},
#             dont_filter=True,
#             meta={
#                 "start_url": start_url,
#                 "api_url": api_data["url"],
#                 "current_page": current_page,
#             },
#         )

#     def get_articles(self, response) -> list:
#         return response.xpath('//div[@class="txt"]//a/@href').getall()

#     def get_href(self, entry) -> str:
#         return entry

#     def get_title(self, response) -> str:
#         return response.xpath('//h1//text()').get()

#     def get_body(self, response) -> str:
#         return body_normalization(response.xpath('//div[@class="content-text"]//p/text()').getall())

#     def date_format(self) -> str:
#         return "%Y-%m-%d"

#     def get_date(self, response) -> str:
#         date_text = response.xpath('//div[@class="article-info"]//ul//li//text()').get()
#         # Ideally add parsing here
#         return date_text.strip() if date_text else ""

#     def get_images(self, response, entry=None) -> List[str]:
#         return response.xpath('//div[@class="content-text"]//img/@src').getall()

#     def get_authors(self, response, entry=None) -> List[str]:
#         return []

#     def get_page_flag(self) -> bool:
#         return False

#     def get_next_page(self, response, current_page) -> Optional[str]:
#         if response.status != 200:
#             self.logger.error(f"Error {response.status}: Not incrementing page.")
#             return None
#         return str(int(current_page) + 1)

#     def go_to_next_page(self, response, start_url, current_page: Optional[str] = "1"):
#         api_url_template = response.meta.get("api_url")
#         next_page = self.get_next_page(response, current_page)
#         if next_page:
#             next_url = api_url_template.format(current_page=next_page)
#             yield scrapy.Request(
#                 url=next_url,
#                 method="GET",
#                 callback=self.parse_intermediate,
#                 dont_filter=True,
#                 meta={
#                     "api_url": api_url_template,
#                     "current_page": next_page,
#                     "start_url": start_url,ss
#                 },
#             )
#         else:
#             logging.info("No more pages to fetch.")

from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import scrapy
import json
from typing import Optional, List

class SseThreesixty(OCSpider):
    name = "SseThreesixty"
    country = "cn"

    start_urls_names = {
        "https://www.360.cn/news.html": "360 News",
        "https://www.sse.com.cn/assortment/stock/list/info/announcement/index.shtml?productId=601360": "SSE Announcements"#no artical
    }

    api_start_urls = {
        "https://www.360.cn/news.html": {
            "url": "https://www.360.cn/n/ajaxlist?page={current_page}&type=6",
            "payload": {
                "page": "1",
                "type": "6"
                         }
        }
    }

    @property
    def source_type(self) -> str:
        return "company"

    @property
    def language(self):
        return "Chinese"

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        current_page = response.meta.get("current_page", 1)
        api_data = self.api_start_urls.get(start_url)
        api_url = api_data["url"].format(current_page=current_page)

        yield scrapy.Request(
            url=api_url,
            callback=self.parse,
            headers={"content-type": "application/json"},
            dont_filter=True,
            meta={
                "start_url": start_url,
                "api_url": api_data["url"],
                "current_page": current_page,
            },
        )

    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="txt"]//a/@href').getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//h1//text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="content-text"]//p/text()').getall())

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response) -> str:
        date_text = response.xpath('//div[@class="article-info"]//ul//li//text()').get()
        return date_text.strip() if date_text else ""

    def get_images(self, response, entry=None) -> List[str]:
        return response.xpath('//div[@class="content-text"]//img/@src').getall()

    def get_authors(self, response, entry=None) -> List[str]:
        return []

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response, current_page) -> Optional[str]:
        if response.status != 200:
            self.logger.error(f"Error {response.status}: Not incrementing page.")
            return None

        try:
            data = json.loads(response.text)
            if not data or not data.get("data"):
                self.logger.info("No more data found in API response.")
                return None
        except Exception as e:
            self.logger.error(f"Failed to parse JSON response: {e}")
            return None

        return str(int(current_page) + 1)

    def go_to_next_page(self, response, start_url, current_page: Optional[str] = "1"):
        api_url_template = response.meta.get("api_url")
        next_page = self.get_next_page(response, current_page)

        if next_page:
            next_url = api_url_template.format(current_page=next_page)
            yield scrapy.Request(
                url=next_url,
                method="POST",
                callback=self.parse_intermediate,
                dont_filter=True,
                meta={
                    "api_url": api_url_template,
                    "current_page": next_page,
                    "start_url": start_url,
                },
            )
        else:
            self.logger.info("No more pages to fetch from API.")
