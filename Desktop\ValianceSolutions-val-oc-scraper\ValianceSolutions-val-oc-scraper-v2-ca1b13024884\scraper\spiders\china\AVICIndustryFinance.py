from scraper.OCSpider import OCSpider
from datetime import datetime
import re

class AVICIndustryFinance(OCSpider):
    name = "AVICIndustryFinance"

    country = "CN"

    charset = "utf-8"

    start_urls_names = {
        "https://www.avicindustry-finance.com/sycd/xwzx/zhcrxw/": "AVIC Industry Finance News",
    }

    @property
    def source_type(self):
        return "Corporate"

    @property
    def language(self): 
        return "Chinese"
    
    @property
    def timezone(self): 
        return "Asia/Shanghai"
    

    def get_articles(self, response) -> list:
        # return response.xpath(
            # '//div[@class="right-content"]/ul/li/a/@href | '
            # '//ul[@class="news-list"]/li/a/@href | '
            # '//a[contains(@href, "/c/")]/@href'
        # ).getall()
        
     return response.xpath('//li[@class="listTableLi"]/a/@href').getall()

    
def get_href(self, entry): return entry

def get_title(self, response, entry=None):
        title = response.xpath(
            '//div[@class="article-title"]/text() | '
            '//h1/text() | '
            '//title/text()'
        ).get()
        return title.strip() if title else "No title found"

def date_format(self): return "%Y/%m/%d"

def get_date(self, response, entry=None):
        date_str = response.xpath(
            '//div[@class="article-info"]/span[@class="date"]/text() | '
            '//div[@class="article-info"]/span[contains(text(), "发布时间")]/text()'
        ).get()
        if date_str and "发布时间：" in date_str:
            date_str = date_str.replace("发布时间：", "").strip()
        if not date_str:
            m = re.search(r'/(\d{4}-\d{2}-\d{2})/', response.url)
            if m: date_str = m.group(1).replace('-', '/')
        try:
            return int(datetime.strptime(date_str, self.date_format()).timestamp())
        except: return int(datetime.now().timestamp())
    
def get_page_flag(self) -> bool:
        return True
    
def get_next_page(self, response):
        m = re.search(r'/page/(\d+)/', response.url)
        current = int(m.group(1)) if m else 1
        info = response.xpath('//div[@class="page-info"]/text()').get()
        if info:
            m = re.search(r'共(\d+)页', info)
            if m and current < int(m.group(1)):
                base = re.sub(r'/page/\d+/?', '/', response.url)
                return [f"{base.rstrip('/')}/page/{current+1}/"]
        return []