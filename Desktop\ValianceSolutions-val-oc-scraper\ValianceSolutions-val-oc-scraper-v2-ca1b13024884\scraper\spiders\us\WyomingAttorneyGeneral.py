from typing import Optional
from scraper.OCSpider import OCSpider
import re
from datetime import datetime

class WyomingAttorneyGeneral(OCSpider):
    name = "WyomingAttorneyGeneral"

    country = "US"
   
    start_urls_names = {
        "https://sites.google.com/a/wyo.gov/wy-ag/press-releases": "Press Releases",                  
    }
    
    charset = "iso-8859-1"

    article_date_map = {} # Mapping articles with dates and Pdf URL from start URL
    
    @property
    def language(self): 
        return "English"
    
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "America/Denver"

    def get_articles(self, response) -> list:
        document_entries = response.xpath('//div[@class="tyJCtd mGzaTb Depvyb baZpAe"]//a[contains(@href, "drive.google.com/file/d/")]')
        self.article_date_map = {} 
        for entry in document_entries:
            link = entry.xpath('./@href').get()
            date = entry.xpath('./text()').get()
            self.article_date_map[link] = date.strip() 
        return list(self.article_date_map.keys()) 

    def get_href(self, entry) -> str:
        return entry
        
    def get_title(self, response) -> str:
        return response.xpath('//h1/text() | //meta[@property="og:title"]/@content').get()
        
    def get_body(self, response) -> str:
        # Only PDF's are there to scrape
        return ""
    
    def get_images(self, response) -> list:
        # Only PDF's are there to scrape
        return []
    
    def date_format(self) -> str:
        return "%m/%d/%Y"

    def get_date(self, response) -> Optional[str]:
        return self.article_date_map.get(response.url)
        
    def get_authors(self, response):
        # Only PDF's are there to scrape
        return []
    
    def get_document_urls(self, response, entry=None) -> list:
        return response.xpath('//a[contains(@href, "drive.google.com/file/d/")]/@href').get()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        # No next page to scrape
        return None