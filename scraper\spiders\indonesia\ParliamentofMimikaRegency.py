from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from urllib.parse import urljoin
import re
from datetime import datetime
import pytz

class ParliamentofmimikaRegencys(OCSpider):
    name = "ParliamentofmimikaRegencys"
    country = "ID"
    start_urls_names = {
        "https://dprd.mimikakab.go.id/contents/berita": "DPRD Mimika News",
        "https://dprd.mimikakab.go.id/jdih/": "DPRD Mimika JDIH",
    }
    charset = "utf-8"
    handle_httpstatus_list = [200, 301, 302, 404, 500]
    custom_settings = {
        "DOWNLOAD_DELAY": 1.0, 
        "CONCURRENT_REQUESTS_PER_DOMAIN": 2,
        "DOWNLOAD_FAIL_ON_DATALOSS": False,
        "DOWNLOAD_TIMEOUT": 60,
        "RETRY_TIMES": 3,
    }

    @property
    def source_type(self) -> str: 
        return "Government"

    @property
    def language(self) -> str: 
        return "Indonesian"

    @property
    def timezone(self): 
        return "Asia/Jakarta"

    def parse_intermediate(self, response):
        return self.parse(response)

    def get_articles(self, response) -> list:
        articles = []
        
        # For main news page
        if "contents/berita" in response.url:
            # Extract article links from news listing
            article_links = response.xpath('//div[contains(@class, "news-item")]//a/@href | //a[contains(@href, "detail?")]/@href').getall()
            
            # Also try alternative selectors for popular news
            if not article_links:
                article_links = response.xpath('//a[contains(@href, "contents/detail")]/@href').getall()
            
            for link in article_links:
                if link and "detail?" in link:
                    full_url = urljoin(response.url, link)
                    articles.append(full_url)
        
        # For JDIH page
        elif "jdih" in response.url:
            # Extract news article links from JDIH
            jdih_links = response.xpath('//a[contains(@href, "detailnews?")]/@href').getall()
            
            for link in jdih_links:
                if link and "detailnews?" in link:
                    full_url = urljoin(response.url, link)
                    articles.append(full_url)
        
        # Remove duplicates
        return list(set(articles))

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response, entry=None) -> str:
        # Try multiple XPath patterns for title
        title_xpaths = [
            '//h1/text()',
            '//h2/text()',
            '//div[contains(@class, "title")]/text()',
            '//title/text()',
            '//h3/text()'
        ]
        
        for xpath in title_xpaths:
            title = response.xpath(xpath).get()
            if title and title.strip():
                # Clean up title
                title = title.strip()
                if " | " in title:
                    title = title.split(" | ")[0]
                return title
        
        return "No title available"

    def get_body(self, response, entry=None) -> str:
        # Try multiple XPath patterns for body content
        body_xpaths = [
            '//div[contains(@class, "content")]//p//text()',
            '//div[contains(@class, "article")]//text()',
            '//div[contains(@class, "news")]//text()',
            '//div[@class="content"]//text()',
            '//p//text()'
        ]
        
        for xpath in body_xpaths:
            body_parts = response.xpath(xpath).getall()
            if body_parts:
                # Filter out navigation and unwanted text
                filtered_parts = []
                for part in body_parts:
                    part = part.strip()
                    if part and len(part) > 10 and not any(skip in part.lower() for skip in 
                        ['copyright', 'share', 'tags', 'kategori', 'berita terkait', 'polling']):
                        filtered_parts.append(part)
                
                if filtered_parts:
                    return body_normalization(filtered_parts)
        
        return "No content available"

    def get_images(self, response, entry=None) -> list:
        # Try multiple XPath patterns for images
        img_xpaths = [
            '//div[contains(@class, "content")]//img/@src',
            '//div[contains(@class, "article")]//img/@src',
            '//img[contains(@src, "lampiran")]/@src',
            '//img/@src'
        ]
        
        images = []
        for xpath in img_xpaths:
            img_urls = response.xpath(xpath).getall()
            for img in img_urls:
                if img and not img.startswith('data:') and 'lampiran' in img:
                    full_url = urljoin(response.url, img)
                    images.append(full_url)
        
        return list(set(images))

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response, entry=None) -> str:
        # Try multiple XPath patterns for date
        date_xpaths = [
            '//div[contains(@class, "date")]/text()',
            '//span[contains(@class, "date")]/text()',
            '//li[contains(text(), "20")]//text()',
            '//time/@datetime',
            '//div[contains(text(), "20")]//text()'
        ]
        
        for xpath in date_xpaths:
            date_str = response.xpath(xpath).get()
            if date_str and date_str.strip():
                # Try to extract date from Indonesian format
                date_patterns = [
                    r'(\d{1,2})-(\d{1,2})-(\d{4})',  # DD-MM-YYYY
                    r'(\d{4})-(\d{1,2})-(\d{1,2})',  # YYYY-MM-DD
                    r'(\d{1,2})/(\d{1,2})/(\d{4})',  # DD/MM/YYYY
                ]
                
                for pattern in date_patterns:
                    match = re.search(pattern, date_str)
                    if match:
                        if len(match.group(1)) == 4:  # YYYY-MM-DD format
                            year, month, day = match.groups()
                        else:  # DD-MM-YYYY or DD/MM/YYYY format
                            day, month, year = match.groups()
                        return f"{year}-{int(month):02d}-{int(day):02d}"
        
        # If no date found, use current date
        return datetime.now(pytz.timezone(self.timezone)).strftime(self.date_format())

    def get_authors(self, response, entry=None) -> list:
        # Try multiple XPath patterns for authors
        author_xpaths = [
            '//div[contains(@class, "author")]/text()',
            '//span[contains(@class, "author")]/text()',
            '//div[contains(text(), "by")]//text()',
            '//span[contains(text(), "by")]//text()',
            '//div[contains(@class, "byline")]//text()'
        ]
        
        for xpath in author_xpaths:
            author = response.xpath(xpath).get()
            if author and author.strip():
                # Clean up the author string
                author = re.sub(r'by\s*|oleh\s*', '', author.strip(), flags=re.IGNORECASE)
                if author:
                    return [author]
        
        # Default author if none found
        return ["DPRD Kabupaten Mimika"]

    def get_document_urls(self, response, entry=None) -> list:
        # Look for document links in the page
        doc_patterns = [
            '//a[contains(@href, ".pdf")]/@href',
            '//a[contains(@href, ".doc")]/@href',
            '//a[contains(@href, ".docx")]/@href',
            '//a[contains(@href, "download")]/@href',
            '//a[contains(text(), "Download")]/@href',
            '//a[contains(text(), "Unduh")]/@href'
        ]
        
        doc_urls = []
        for pattern in doc_patterns:
            urls = response.xpath(pattern).getall()
            if urls:
                doc_urls.extend(urls)
        
        # Remove duplicates and convert to absolute URLs
        return [urljoin(response.url, url) for url in set(doc_urls)]

    def get_next_page(self, response) -> str:
        # Check for pagination links
        next_page_xpaths = [
            '//a[contains(text(), "Next")]/@href',
            '//a[contains(text(), "Selanjutnya")]/@href',
            '//a[@class="next"]/@href',
            '//a[contains(@class, "next")]/@href',
            '//li[contains(@class, "next")]/a/@href'
        ]
        
        for xpath in next_page_xpaths:
            next_page = response.xpath(xpath).get()
            if next_page:
                return urljoin(response.url, next_page)
        
        return None

    def get_page_flag(self, response=None) -> bool:
        if response is None:
            return True
        # Only parse article pages, not the main listing pages
        if any(url in response.url for url in ["contents/berita", "jdih/"]) and "detail" not in response.url:
            return True
        if any(detail in response.url for detail in ["detail?", "detailnews?"]):
            return False
        return True
