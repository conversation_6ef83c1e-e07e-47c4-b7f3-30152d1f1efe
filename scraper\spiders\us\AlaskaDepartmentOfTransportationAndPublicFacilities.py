from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime 

class AlaskaDepartmentOfTransportationAndPublicFacilities(OCSpider):
    name = "AlaskaDepartmentOfTransportationAndPublicFacilities"
    
    country = "US"
    
    start_urls_names = {
        "https://dot.alaska.gov/comm/pressbox/": "DOT&PF Newsroom",
    }

    include_rules = [r'^https://dot\.alaska\.gov/comm/pressbox/.*']

    charset = "utf-8"

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350
            },
        "DOWNLOAD_DELAY": 6,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }
    HEADLESS_BROWSER_WAIT_TIME = 50000

    visited_links = set()

    @property
    def language(self): 
        return "English"
     
    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "America/Anchorage"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="col-sm-8 order-md-12 order-1"]//ul//a/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        title_list = response.xpath('//div[@class="text-center py-4"]//h4/strong/text() | //div[@class="text-center py-4"]//h4/span/em/strong/text()').getall()
        return " ".join(title_list).strip()
      
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="col pt-4"]//text()').getall())

    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return"%m-%d-%Y"
    
    def get_date(self, response) -> Optional[str]:
        date_str = response.xpath('//div[@class="col-8"]/text()').get() 
        date_obj = datetime.strptime(date_str, "%B %d, %Y")
        return date_obj.strftime("%m-%d-%Y")
       
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]: 
        next_pages = response.xpath('//div[@id="archive_list"]//a/@href').getall()# Extract all archive links
        for next_page in next_pages:
            if next_page not in self.visited_links:
                self.visited_links.add(next_page)
                return next_page 