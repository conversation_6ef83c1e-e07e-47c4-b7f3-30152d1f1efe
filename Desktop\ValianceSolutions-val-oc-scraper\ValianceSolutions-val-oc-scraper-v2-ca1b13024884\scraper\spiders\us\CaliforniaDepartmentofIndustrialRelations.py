from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
import datetime
import scrapy
import re

class CaliforniaDepartmentOfIndustrialRelations(OCSpider):
    name = 'CaliforniaDepartmentOfIndustrialRelations'

    country = "US"

    start_urls_names = {
        'https://www.dir.ca.gov/dirnews/':"News"
    }
    
    charset="iso-8859-1"

    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self) -> str:
        return "US/Eastern"  
    
    @property
    def language(self):
        return "English"
    
    def parse_intermediate(self, response):
        current_year = datetime.datetime.now().year
        for year in range(current_year, 2016, -1):
            news_url = f"https://www.dir.ca.gov/dirnews/NR{year}.html"
            yield scrapy.Request(
                url=news_url,
                callback=self.parse,
                meta={
                    'start_url': response.url
                    },
                dont_filter=True
            )

    def get_articles(self, response) -> list:
        return response.xpath("//td/a/@href").getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1/text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//p//text()').getall())

    def get_images(self, response) -> list[str]:
        return []

    def date_format(self) -> str:
        return '%B %d, %Y'

    def get_date(self, response) -> str:
        texts = response.xpath("//div[.//text()]//text()").getall()
        full_text = ' '.join(text.strip() for text in texts if text.strip())
        match = re.search(r'([A-Z][a-z]+ \d{1,2}, \d{4})', full_text)
        if match:
            date_text = match.group(1)
            try:
                dt = datetime.datetime.strptime(date_text, self.date_format())
                return dt.strftime(self.date_format())
            except ValueError:
                return date_text

    def get_authors(self, response, entry=None) -> list[str]:
        return []
    
    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        next_page = response.xpath('//div[@class="pagination"]//a[i[contains(@class, "fa-angle-right")]]/@href').get()
        return response.urljoin(next_page) if next_page else None