
from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from urllib.parse import urljoin
import datetime

class ParliamentOfNaganRayaRegency(OCSpider):
    name = "ParliamentOfNaganRayaRegency"

    country = "ID"

    start_urls_names = {
        "https://dprk.naganrayakab.go.id/berita": "News",
    }

    custom_settings = {
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350,
    },
    }
    
    HEADLESS_BROWSER_WAIT_TIME = 10000

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "Indonesian"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    def get_articles(self, response) -> list:   
        return response.xpath("//h5//a//@href").getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//h1//text()").get().strip()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='card-body isi-berita']//p//text()").getall())
    
    def get_images(self, response) -> list:
        return response.xpath("//div[@class='card-header']//div[@class='gambar-caption']//img//@src").getall()
       
    def date_format(self) -> str:
        return "%d %m %Y"
    
    def get_date(self, response) -> str:
        date = response.xpath("//div[@class='meta']//span[@class='tanggal-meta tanggal-berita']//text()").get()
        date_part = date.split(',')[1].strip()
        return date_part
    
    def get_authors(self, response):
        return response.xpath("//div[@class='meta']//span[@class='author-meta author-berita']//text()").getall()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]: 
        return response.xpath("//div[@class='p_page']//a[@class='page_a page_next ']//@href").get()
        