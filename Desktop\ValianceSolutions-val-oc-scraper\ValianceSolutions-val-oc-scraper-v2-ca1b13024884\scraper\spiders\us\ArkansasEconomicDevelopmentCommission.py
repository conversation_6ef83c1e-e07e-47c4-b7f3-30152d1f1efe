from typing import Optional
from scraper.OCSpider import <PERSON><PERSON>pider
from scraper.utils.helper import body_normalization
from datetime import datetime
import urllib.parse
import scrapy
import math
import json
import re

class ArkansasEconomicDevelopmentCommission(OCSpider):
    name = "ArkansasEconomicDevelopmentCommission"

    country = "US"

    start_urls_names = {
        "https://www.arkansasedc.com/news-events/newsroom": "Newsroom",
        }
    
    api_start_urls = {
        "https://www.arkansasedc.com/news-events/newsroom": {
            "url": "https://www.arkansasedc.com/api/news",
            "payload": {
                "industryId": "null",
                "categoryId": "null",
                "orderBy": "publicationDate",
                "isAscendingSort": "false",
                "page": 1,
                "take": 9
}
        }
    }

    custom_settings = {
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36"
    }

    charset = "utf-8"

    @property
    def language(self): 
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "America/Chicago"
    
    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            return
        payload = response.meta.get("payload", api_data["payload"].copy())
        api_url = api_data["url"]
        full_api_url = f"{api_url}?{urllib.parse.urlencode(payload)}"
        
        yield scrapy.Request(
            url=full_api_url,
            method="GET",
            headers=self.headers,
            callback=self.parse,
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "payload": payload,
                "current_page": payload["page"]
            }
        )

    def get_articles(self, response) -> list:
            data = json.loads(response.text)
            articles = data.get("newsResult", [])
            article_urls = []
            for article in articles:
                url = article.get("detailPageURL")
                if url:
                    if url.startswith("/"):
                        url = f"https://www.arkansasedc.com{url}"
                    article_urls.append(url)
            return article_urls

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="sf-mb-xl text-info font-regular text-initial sf-mb-xxl"]/text()').get()
        

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="news-content text-default text-small"]//text()').getall()) 
        
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return"%m-%d-%Y"
    
    def get_date(self, response) -> str:
        date_str = response.xpath('//strong[@class="vertical-align-middle"]/text()').get()
        return datetime.strptime(date_str, "%B %d, %Y").strftime("%m-%d-%Y") 
        
    def get_authors(self, response):
        return []
    
    def get_next_page(self, response, current_page: Optional[int] = 1) -> Optional[int]:
            data = json.loads(response.text)
            pages_count = data.get("pagesCount", 0)
            if current_page < pages_count:
                return current_page + 1
    
    def get_page_flag(self) -> bool:
            return False
    
    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        api_data = self.api_start_urls.get(start_url)
        api_url = api_data["url"]
        payload = response.meta.get("payload", {}).copy()
        next_page = self.get_next_page(response, current_page)
        payload["page"] = next_page  
        full_api_url = f"{api_url}?page={payload['page']}"
        yield scrapy.Request(
            url=full_api_url,
            method="GET",
            headers=self.headers,
            callback=self.parse_intermediate,
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "payload": payload,
                "current_page": next_page
            },
            
        )


        