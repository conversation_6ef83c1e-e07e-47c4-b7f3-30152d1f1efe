from datetime import datetime
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class NewMexicoStateTreasurer(OCSpider):
    name = "NewMexicoStateTreasurer"

    country="US"

    start_urls_names = {
        "https://nmsto.gov/category/news-and-announcements/":"News and Announcements",
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return 'ministry'
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Mountain"
    
    def get_articles(self, response) -> list:
       return response.xpath('//h2[@class="entry-title"]//a/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="entry-title"]//text()').get()
       
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="entry-content"]//p//text()').getall())
    
    def get_images(self, response, entry=None) :
        return response.xpath('//div[@class="entry-content"]//img/@src').getall()
    
    def get_authors(self, response, entry=None) -> list[str]:
        return []

    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date = response.xpath('//time[@class="entry-date published"]//text()').get()
        if date is None:
            date =response.xpath('//time[@class="entry-date published updated"]/text()').get()
        if date:
            date_obj = datetime.strptime(date, "%B %d, %Y")
            formatted_date = date_obj.strftime("%Y-%m-%d")
            return formatted_date
    
    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        # No next page is there to scarp
        return None
