from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from typing import List

class GuangzhouPharHoldings(OCSpider):
    name = 'GuangzhouPharHoldings'

    start_urls_names = {
        "https://www.gpc.com.cn/news.html":"白云山", 
        "https://www.gybys.com.cn/information/tmpNotice.html":"白云山", #pdf
        "https://www.gybys.com.cn/information/regularReport.html":"白云山", #pdf 
        "https://www.gybys.com.cn/article/9.html":"白云山" #No articles
    }
    
    article_data_map ={}

    charset = "iso-8859-1"
    
    @property
    def source_type(self) -> str:
        return "SOE"
    
    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        articles = []
        for article in response.xpath("//div[@class='txtbox-inner']//div[@class='txt-main']"):
            url = article.xpath(".//h2//a/@href").get()
            title = article.xpath(".//h2//a//text()").get()
            date = article.xpath(".//div[@class='news_info2']//span//text()").get()
            if url and title and date:
                full_url = url
                title = title.strip()
                if '.pdf' in full_url.lower():
                    pdf = full_url
                else:
                    pdf = "None"
                clean_date = date.strip()
                self.article_data_map[full_url] = {"title": title, "date": clean_date, "pdf": pdf}
                articles.append(full_url) 
        return articles
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self,response) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response, entry=None) -> str:
        if ".pdf" in response.url.lower():
            return ""
        return body_normalization(response.xpath('//div[@class="artical"]//p//text()').getall())
        
    def date_format(self) -> str:
        return '%Y-%m-%d %H:%M:%S'
    
    def get_date(self, response, entry=None) -> int:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        
    def get_authors(self, response, entry=None) -> List[str]:
        return []
    
    def get_document_urls(self, response, entry=None) -> list:
        pdf_url = self.article_data_map.get(response.request.meta.get('entry'), {}).get("pdf")
        return [pdf_url] if pdf_url and pdf_url != "None" else []
    
    def get_images(self, response, entry=None) -> List[str]:
        if ".pdf" in response.url.lower():
            return ""
        return response.xpath("//div[@class='artical']//img//@src").getall()
    
    def get_page_flag(self) -> bool:
        return True
    
    def get_next_page(self, response) -> List[str]:
        if response.status == 404:
            return None
        current_url = response.url
        if current_url.endswith('.html'):
            next_page_url = current_url.replace(".html", ".html?p=2")
        else:
            import re
            match = re.search(r"\.html\?p=(\d+)", current_url)
            if match:
                current_page_num = int(match.group(1))
                next_page_num = current_page_num + 1
                next_page_url = current_url.replace(f".html?p={current_page_num}",f".html?p={next_page_num}")
            else:
                return None
        return next_page_url