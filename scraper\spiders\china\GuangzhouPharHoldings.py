from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization

class GuangzhouPharHoldings(OCSpider):
    name = 'GuangzhouPharHoldings'

    start_urls_names = {
        'https://www.gpc.com.cn/news/media.html#': '',
    }
    
    charset = "utf-8"
    
    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350
            },
        "DOWNLOAD_DELAY": 6,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }
    HEADLESS_BROWSER_WAIT_TIME = 30000

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"
    
    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="news_li"]//h2/a/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self,response) -> str:
        return response.xpath('//div[@class="title"]//h3/text()').get()
    
    def date_format(self) -> str:
        return '%Y-%m-%d %H:%M:%S'
    
    def get_date(self, response) -> str:
        return response.xpath("//div[@class='title']//p/span[1]/text()").re_first(r"\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}")
    
    def get_authors(self, response):
        return []
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="artical"]//p[normalize-space(text())]/text()').getall())
    
    def get_images(self, response) -> list[str]:
        return response.xpath("//div[@class='artical']//img/@src").getall()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str: 
        next_page = response.xpath('//div[@id="pageControl"]//a[contains(@class, "next")]/@href').get()
        if not next_page:
            return None
        else:
            return next_page