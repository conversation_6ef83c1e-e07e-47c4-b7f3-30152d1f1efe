from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime

class PublisherAssociationOfChina(OCSpider):
    name = "PublisherAssociationOfChina"

    start_urls_names = {
         "https://www.pac.org.cn/hangyedongtai": "中国出版协会",
         "https://www.pac.org.cn/xiehuidongtai": "中国出版协会"
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"
    
    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="text_list"]//a/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="text_title"]//h1/text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="text_area"]//p//text()').getall())
    
    def get_images(self, response) -> list:
        return response.xpath('//div[@class="text_area"]//p//img/@src').getall()
    
    def get_document_urls(self, response, entry=None) -> list:
        documents = response.xpath('//div[@class="text_area"]//p//a/@href').getall()
        if documents:
            absolute_documents = [response.urljoin(doc) for doc in documents]
            return absolute_documents
        return None
    
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date_str =  response.xpath('//div[@class="text_title"]//p//text()').re_first(r"\d{4}\.\d{2}\.\d{2}")
        date_obj = datetime.strptime(date_str, "%Y.%m.%d")
        return date_obj.strftime("%Y-%m-%d")
    
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        try:
            next_page = response.xpath('//div[@class="list_page"]//a[contains(text(), "»")]/@href').get()
            if next_page:
                next_page_url = response.urljoin(next_page)
                return next_page_url
            return None
        except Exception as e:
            self.logger.error(f"Error extracting next page link from page {response.url}: {e}")
            return None