from datetime import datetime
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
from typing import Optional
import scrapy
import logging

class SouthCarolinaStateTreasurer(OCSpider):
    name = "SouthCarolinaStateTreasurer"

    country="US"
    
    start_urls_names = {
        "https://treasurer.sc.gov/about-us/newsroom/" : "Newsroom",
    }

    HEADLESS_BROWSER_WAIT_TIME = 50000  # 50 seconds

    custom_settings = { 
		"DOWNLOADER_MIDDLEWARES" : {
			'scraper.middlewares.HeadlessBrowserProxy': 350,
		},
		"DOWNLOAD_DELAY" : 2,            
	}

    charset = "utf-8"
    
    @property
    def language(self):
        return "English"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "US/Mountain"
    
    def get_articles(self, response) -> list:
       return response.xpath('//div[@class="col-lg-10 col-md-8  align-self-center"]//a/@href').getall()
   
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="article-header"]/h1/text()').get()
       
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="main-content news-article"]/p/text()').getall())
    
    def get_images(self, response, entry=None) :
        return response.xpath('//div[@class="main-content news-article"]//img/@src').getall()

    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date = response.xpath('//div[@class="article-header"]/h3/text()').get()
        if date:
            date_obj = datetime.strptime(date, "%B %d, %Y")
            formatted_date = date_obj.strftime("%Y-%m-%d")
            return formatted_date

    def get_authors(self, response, entry=None) -> list[str]:
        return []
    
    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response, current_page):
        if response.status != 200:
            self.logger.error(f"Error {response.status}: Not incrementing page.")
            return None
        return str(int(current_page) + 1)
     
    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        next_page = self.get_next_page(response, current_page)
        url=f"https://treasurer.sc.gov/about-us/newsroom/?page={next_page}"
        if next_page:
            yield scrapy.Request(
                url=url,
                method='GET',
                callback=self.parse, 
                dont_filter=True,
                 meta={
                        'current_page': next_page, 
                        'start_url': start_url,
                    }
            )
        else:
            logging.info("No more pages to fetch.")
            yield None