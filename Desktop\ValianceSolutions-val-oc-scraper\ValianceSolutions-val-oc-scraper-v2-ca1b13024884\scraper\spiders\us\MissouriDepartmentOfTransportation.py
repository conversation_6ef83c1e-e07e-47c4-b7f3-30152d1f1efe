from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class MissouriDepartmentofTransportation(OCSpider):
    name = 'MissouriDepartmentofTransportation'

    country = "US"
    
    start_urls_names = {
        'https://www.modot.org/search/news': "News"
    }
            
    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Central"

    def get_articles(self, response) -> list:
        return response.xpath("//h2//a//@href").getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//h1//text()").get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='news-main-content col-12 col-md-8']//text()").getall())
    
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return '%a, %m/%d/%Y - %H:%M'

    def get_date(self, response) -> str:
        return response.xpath("//div[@class='field--item']//time//text()").get()
    
    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False
 
    def get_next_page(self, response) -> str:
        next_page = response.xpath("//li[@class='pager__item pager__item--next']//a//@href").get()
        if next_page:
            return next_page
        else:
            return None