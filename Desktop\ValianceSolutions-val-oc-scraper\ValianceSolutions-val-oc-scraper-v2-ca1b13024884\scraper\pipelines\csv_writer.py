import csv
import logging
import os
from datetime import datetime

class Pipeline:
    def open_spider(self, spider):
        # Create a filename with the spider name and timestamp
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{spider.name}_{timestamp}.csv"

        # Create a directory for CSV files if it doesn't exist
        os.makedirs('output', exist_ok=True)

        # Open the CSV file when the spider starts
        self.file = open(f"output/{filename}", "w", newline="", encoding="utf-8")
        self.csv_writer = None

        # Log the output file location
        logging.info(f"CSV output will be saved to: output/{filename}")

    def process_item(self, item, spider):
        # Initialize the CSV writer on the first item to set headers
        try:
            if self.csv_writer is None:
                # Define fieldnames - ensure we have all the required fields
                required_fields = ['url', 'title', 'body', 'images', 'date', 'document_urls', 'authors']

                # Get all fields from the item
                item_fields = list(item.keys())

                # Combine required fields with any additional fields in the item
                fieldnames = list(set(required_fields + item_fields))

                # Log the fields that will be written to CSV
                logging.info(f"CSV fields: {fieldnames}")

                # Create the CSV writer
                self.csv_writer = csv.DictWriter(self.file, fieldnames=fieldnames)
                self.csv_writer.writeheader()

            # Write the item to the CSV
            self.csv_writer.writerow(item)
            return item
        except Exception as e:
            logging.error(f"Exception in csv_writer.py: {e}")
            return item

    def close_spider(self, spider):
        # Close the CSV file when the spider ends
        if hasattr(self, 'file') and self.file:
            self.file.close()
            logging.info(f"CSV file closed for spider: {spider.name}")
