from scraper.utils.helper import body_normalization
from scraper.OCSpider import OCSpider
from datetime import datetime

class KansasAttorneyGeneral(OCSpider):
    name = 'KansasAttorneyGeneral'

    country = "US"

    start_urls_names = {
        "https://www.ag.ks.gov/media-center/news-releases": "News Release",
        "https://www.ag.ks.gov/media-center/news-releases/-arch-1":"News Release",
    }
    
    HEADLESS_BROWSER_WAIT_TIME = 30000 
    
    custom_settings = {
		"DOWNLOADER_MIDDLEWARES" : {
			'scraper.middlewares.HeadlessBrowserProxy': 350,
		},
		"DOWNLOAD_DELAY" : 2,
	}
    
    charset = "utf-8"
    
    article_data_map ={}  # Mapping date with articles from start URL

    @property
    def source_type(self) -> str:
        return 'ministry'
    
    @property
    def timezone(self):
        return "US/Central"
    
    @property
    def language(self):
        return "English"
    
    def get_articles(self, response) -> list:
        try:
            articles = response.xpath("//ul//li")
            all_articles = set()
            for article in articles:
                full_url = article.xpath(".//h2/a/@href").get()
                img = article.xpath(".//div[@class='item-img']//img//@src").get()
                if full_url:
                    self.article_data_map[full_url] = {
                        "img": img if img else "",
                        "full_url": [full_url],
                    }
                    all_articles.add(full_url)
            return list(all_articles)
        except Exception as e:
            return []
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        title =  response.xpath("//div[@class='detail-content']//h1//text()").get()  # Example: https://www.ag.ks.gov/Home/Components/News/News/73/1292?arch=1
        if not title:
            title = response.xpath("//h2[@class='detail-title']//text()").get()  # Example: https://www.ag.ks.gov/Home/Components/News/News/154/1292
        return title
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='detail-content']//p//text()").getall())
    
    def get_images(self, response) -> list:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("img"," ").strip()
    
    def date_format(self) -> str:
        return '%m/%d/%Y %I:%M %p'
    
    def get_date(self, response) -> str:
        date_str=response.xpath("//div[@class='detail-list']//span[@class='detail-list-value']//text()").get()
        for fmt in ("%m/%d/%Y %I:%M %p", "%m/%d/%Y"):
            try:
                date_obj = datetime.strptime(date_str, fmt)
                return date_obj.strftime("%m/%d/%Y %I:%M %p")
            except ValueError:
                continue
        return ""
    
    def get_authors(self, response):
        return []
    
    def get_document_urls(self, response, entry=None):
        return response.xpath("//div[@class='detail-content']//p//a/@href").get()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        next_page = response.xpath("//div[@class='list-pager']/a[contains(text(),'Next')]/@href").get()
        if next_page:
            return next_page
        else:
            return None