import logging
from typing import Optional
import scrapy
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization

class WoodrowWilsonInternationalCenterForScholars(OCSpider):
    name = 'WoodrowWilsonInternationalCenterForScholars'
    
    country = "US"

    start_urls_names = {
        'https://www.wilsoncenter.org/insight-analysis?_page=1&keywords=&_tab=insight-analysis&_limit=10&program=1031': 'News'
    }
    
    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Central"
    
    def get_articles(self, response) -> list:
        return response.xpath('//h2[@class= "title h4 -blue-600"]/a/@href').getall()  
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self,response) -> str:
        return response.xpath('//div[@class= "hero-article-content-inner"]/h1/text() | //div[@class= "hero-event-detail-main"]/h1/text() | //div[@class="hero-publication-main-content"]//h1//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class= "inner"]//p/text()').getall())    
            
    def get_images(self, response) -> list[str]:
        return []

    def date_format(self) -> str:
        return "%B %d, %Y"

    def get_date(self, response) -> str:
        return response.xpath('//div[@class= "article-meta"]/div[@class= "published-info"]/span//time/text() | //span//time/text()').re_first(r"([A-Za-z]+ \d{1,2}, \d{4})")
    
    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response, current_page):
        if response.status != 200:
            return None
        else:
            return str(int(current_page) + 1)
     
    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        next_page = self.get_next_page(response, current_page)
        url=f"https://www.wilsoncenter.org/insight-analysis?_page={next_page}"
        if next_page:
            yield scrapy.Request(
                url=url,
                method='GET',
                callback=self.parse, 
                dont_filter=True,
                 meta={
                        'current_page': next_page, 
                        'start_url': start_url,
                    }
            )
        else:
            yield None