import re
from scraper.OCSpider import <PERSON><PERSON>pider
from typing import List
from dotenv import load_dotenv
from scraper.utils.helper import body_normalization
load_dotenv()

class ChinaCivilAirportsAssociation1(OCSpider):
    name = "ChinaCivilAirportsAssociation1"

    start_urls_names = {
        "https://www.chinaairports.org.cn/dl/IsvPnFiMDp.html":"协会要闻", 
        "https://www.chinaairports.org.cn/dl/tG9M3CrEyj.html":"行业新闻", 
        "https://www.chinaairports.org.cn/dl/EQEH73XJvH.html":"国际动态", 
        "https://www.chinaairports.org.cn/dl/HyskAUNBBw.html":"通知公告", 
    }

    include_rules=[r'https://www\.chinaairports\.org\.cn/.*']
        
    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return 'IndustryAssociation'

    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        return response.xpath("//div[@class='tulist3' or @class='list1']//a[not(ancestor::div[@class='fy'])]/@href").getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//div[@class='wz_div_title wz_div_title_h2']//h2//text()").get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[contains(@class, 'wz_div_show')]//p//text()").getall())
    
    def get_images(self, response, entry=None) -> List[str]:
        return [response.urljoin(i) for i in response.xpath("//div[contains(@class, 'wz_div_show')]//img/@src").getall()]
    
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date = response.xpath("//div[@class='wz_div_time']//p//text()").get()
        return re.search(r"\d{4}-\d{2}-\d{2}", date).group()
    
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        try:
            next_page=response.xpath("//div[@class='fy']//a[contains(text(), '下一页')]/@href").get()
            if next_page:
                next_page_url = response.urljoin(next_page)
                if next_page_url == response.url:
                    return None
                return next_page_url
            else:
                return None
        except Exception as e:
            return None