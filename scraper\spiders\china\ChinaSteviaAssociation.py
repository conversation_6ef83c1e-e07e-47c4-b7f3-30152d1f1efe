from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ChinaSteviaAssociation(OCSpider):
    name = "ChinaSteviaAssociation"

    start_urls_names = {
       "http://www.csa1988.cn/index.php?m=content&c=index&a=lists&catid=44" : "资讯",
       "http://www.csa1988.net/html/zhuanti/XIEHUILIUDA/" : "协会六大",  # one child article have no data in it http://www.csa1988.cn/index.php?m=member&c=index&a=login&forward=http%3A%2F%2Fwww.csa1988.cn%2Findex.php%3Fm%3Dcontent%26c%3Dindex%26a%3Dshow%26catid%3D26%26id%3D1649
       "http://www.csa1988.net/html/zhuanti/XIEHUIDASJ/" : "协会大事记一",
       "http://www.csa1988.net/html/zhuanti/XIEHUIASF" : "协会大事记二",
       "http://www.csa1988.net/html/zhuanti/ANSDD/" : "甜菊安全性评估",
       "http://www.csa1988.net/html/zhuanti/DFGDGG/" : "协会成立二十周年",
       "http://www.csa1988.net/html/zhuanti/DFKLGDG/" : "甜菊风波",
       "http://www.csa1988.cn/index.php?m=content&c=index&a=lists&catid=58" : "七大",
       "http://www.csa1988.cn/index.php?m=content&c=index&a=lists&catid=59" : "八大"
    }

    charset= "utf-8"

    @property
    def source_type(self) -> str:
        return 'IndustryAssociation'

    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self,response)-> list:
       self.article_date_mapping(response)
       return response.xpath('//td[@align="left"]/a[@class="c2"]/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//td[@class="c6"]//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//td[@align="left" and @valign="top" and @height="360"]//text()').getall())
    
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    article_to_date_mapping = {}  # Variable to store the article URL to date mapping

    def get_date(self, response) -> str:
        """Dates are not directly present in the articles."""
        article_url = response.url
        article_date = self.article_to_date_mapping.get(article_url, None)
        if article_date:
            return article_date
        else:
            self.logger.error(f"No date found for URL: {article_url}")
            return None
        
    def article_date_mapping(self, response):
        """
        Creates and returns a hashmap of article URLs and their associated dates.
        """
        mapping = {}
        entries = response.xpath('//table[@class="next_list"]//tr')
        for entry in entries: 
            url = entry.xpath('.//td[@align="left"]/a[@class="c2"]/@href').get()
            # Eg dates: 2025-01-10
            date = entry.xpath('.//td[@align="right"]//text()').re_first(r'(\d{4}-\d{2}-\d{2})')
            if not date:
                self.logger.debug(f"Missing date for URL: {url}, entry content: {entry.get()}")
            full_url = response.urljoin(url)
            mapping[full_url] = date
        self.article_to_date_mapping.update(mapping)
    
    def get_images(self, response) -> list:
       return response.xpath('//div/span/img/@src').getall()
    
    def get_authors(self, response):
        return []
    
    def get_document_urls(self, response, entry=None) -> list:
        return response.xpath('//div/span/a/@href').getall()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        try:
            next_page=response.xpath("//td//a[contains(text(), '下一页')]/@href").get()
            if next_page:
                next_page_url = response.urljoin(next_page)
                if next_page_url == response.url:
                    return None
                return next_page_url
            return None
        except Exception as e:
            self.logger.error(f"Error extracting next page link from page {response.url}: {e}")
            return None