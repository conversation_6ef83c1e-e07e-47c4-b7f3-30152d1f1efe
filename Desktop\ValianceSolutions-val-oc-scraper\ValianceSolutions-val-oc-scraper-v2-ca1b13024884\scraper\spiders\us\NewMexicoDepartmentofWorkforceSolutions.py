import datetime
from typing import List
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization

class NewMexicoDepartmentofWorkforceSolutions(OCSpider):
    name = 'NewMexicoDepartmentofWorkforceSolutions'

    country = "US"
    
    custom_settings = {
		"DOWNLOADER_MIDDLEWARES" : {
			'scraper.middlewares.HeadlessBrowserProxy': 350,
		},
		"DOWNLOAD_DELAY" : 5,
	}
    
    HEADLESS_BROWSER_WAIT_TIME = 30000   # 30 Seconds wait time
    
    current_year = datetime.datetime.now().year

    start_urls_names = {
        f'https://www.dws.state.nm.us/en-us/News/Latest-News/acat/2/yeararchive/{current_year}': "News"
    }

    charset = "utf-8"

    article_data_map={}

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Central"
    
    def get_articles(self, response) -> list:  
        articles = response.xpath("//h2//@href").getall()
        return [link for link in articles if not link.lower().endswith(".pdf")]
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self,response) -> str:
        return response.xpath("//h2[@class='edn_articleTitle']//text()").get()    
        
    def get_body(self, response) -> str: 
        return body_normalization(response.xpath("//article[@class='edn_article edn_articleDetails']//h2//following-sibling::text()").getall())
    
    def get_images(self, response) -> list[str]:
        return []

    def date_format(self) -> str:
        return " %A, %B %d, %Y"

    def get_date(self, response) -> str:
        return response.xpath("//time//text()").get()

    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None) -> List[str]:
        return response.xpath("//article[@class='edn_article edn_articleDetails']//a[contains(@href,'.pdf')]//@href").get()
    
    def get_page_flag(self) -> bool:
        return True
    
    def get_next_page(self, response) -> str:
        next_page_links = response.xpath("//div[@class='article_pager']//a//@href").getall()
        if not next_page_links:
            return 
        for link in next_page_links:
            if "next" in link.lower():  
                return response.urljoin(link)
        return response.urljoin(next_page_links[-1])