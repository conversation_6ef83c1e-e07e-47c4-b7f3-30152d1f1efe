from typing import Optional
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
from datetime import datetime

class NewYorkStateDepartmentOfLabor(OCSpider):
    name = "NewYorkStateDepartmentOfLabor"

    country = "US"

    start_urls_names = {
        "https://dol.ny.gov/newsroom": "Newsrooms",
    }

    custom_settings = {
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 351, 
        },
        "DOWNLOAD_DELAY": 1  
}
    
    charset = "utf-8"

    HEADLESS_BROWSER_WAIT_TIME = 100  # 30 seconds wait time

    @property
    def language(self): 
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "America/New_York"
    
    def get_articles(self, response) -> list:
        base_url = "https://dol.ny.gov"
        hrefs = response.xpath('//div[@class="view-content"]//article//a/@href').getall()
        relative_links = {link for link in hrefs if link.startswith("/")}
        full_links = [base_url + link for link in relative_links]
        return full_links
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return (
        response.xpath('//div[@class="hero-inner hero-news-inner hero-has-image-content"]//h1/span/text()').get()
        or response.xpath('//h1[contains(@class, "a-title")]/text()').get()
        or ""
    )

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="webny-card-description"]//text()').getall()) 
        
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return"%m-%d-%Y"
    
    def get_date(self, response) -> str:
        clean_date = (
            response.xpath('//div[@class="webny-card-date"]/text()').get()
            or response.xpath('//div[@class="m-newsHero__dateCreated"]//span[@class="a-date a-hero__date"]/text()').get()
        )
        if clean_date:
            return datetime.strptime(clean_date.strip(), "%B %d, %Y").strftime("%m-%d-%Y")
        return ""
            
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]:
        next_page = response.xpath('//li[@class="pager__item pager__item--next"]/a/@href').get()
        if not next_page:
           return None
        else:
            return next_page