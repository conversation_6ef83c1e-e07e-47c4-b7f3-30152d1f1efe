from scraper.OCSpider import OCSpider
import scrapy
from scraper.utils.helper import body_normalization

class KansasDepartmentofCommerce(OCSpider):
    name = 'KansasDepartmentofCommerce'

    country = "US"

    start_urls_names = {
        "https://www.kansascommerce.gov/news/": "News",
    }

    api_start_urls = {
        'https://www.kansascommerce.gov/news/': {
            "url": "https://www.kansascommerce.gov/wp-admin/admin-ajax.php",
            "payload": {
                "action": "news_load_more_posts",
                "page": "1"  
            }
        }
    }
    charset = "utf-8"

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            self.logger.error(f"No API configuration found for start_url: {start_url}")
            return
        api_url = api_data["url"]
        payload = api_data["payload"]
        current_page = payload["page"]
        payload["page"] = str(current_page)
        yield scrapy.FormRequest(
            url=api_url,
            method="POST",
            headers={"Content-Type": "application/x-www-form-urlencoded; charset=UTF-8"},
            formdata=payload,
            callback=self.parse,
            dont_filter=True,
            meta={
                "start_url": start_url,
                "api_url": api_url,
                "payload": payload,
                "current_page": current_page
            },
        )

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "US/Central"

    @property
    def language(self):
        return "English"

    def get_articles(self, response) -> list:
        articles = response.xpath("//a[@class='im-news-story']//@href").getall()
        return articles 
    
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//h1//text()").get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='copy']//p//text() | //div[@class='copy']//ul//li//text()").getall())

    def get_images(self, response) -> list:
        return response.xpath("//div[@class='container margin-container']//img//@src").getall()

    def date_format(self) -> str:
        return "%b %d, %Y"

    def get_date(self, response) -> str:
        date_text = response.xpath("//p[@class='date']//text()").get()
        return date_text.strip() if date_text else ""

    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
        return ""

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        current_page = int(response.meta.get("current_page"))
        if current_page:
            next_page = current_page + 1
            return str(next_page)
        return None

    def go_to_next_page(self, response, start_url=None, current_page=None):
        api_url = response.meta.get("api_url")
        payload = response.meta.get("payload")
        next_page = self.get_next_page(response)
        if next_page:
            payload["page"] = str(next_page) 
            yield scrapy.FormRequest(
                url=api_url,
                method='POST',
                formdata=payload,
                headers={"Content-Type": "application/x-www-form-urlencoded; charset=UTF-8"},
                callback=self.parse_intermediate,
                meta={
                    "start_url": start_url,
                    "api_url": api_url,
                    "payload": payload,
                    "current_page": next_page
                }
            )
        else:
            self.logger.info(f"No more pages to crawl for start_url: {start_url}")