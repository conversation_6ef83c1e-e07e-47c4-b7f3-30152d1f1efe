import scrapy
import re
from datetime import datetime
from scraper.OCSpider import OCSpider

class Haier(OCSpider):
    name = "<PERSON><PERSON>"
    
    charset = "iso-8859-1"

    start_urls_names = {
        "https://smart-home.haier.com/en/xwycjrl/gsxw/?spm=inverstor.cn_ggyxw_pad.news_20191022.40493": "Company News",
        "https://smart-home.haier.com/cn/gpxx/yjbg/?spm=inverstor.cn_gpxx_pad.yjbg.40492":"AccountingCompany News",
        "https://smart-home.haier.com/cn/gpxx/iv/?spm=inverstor.cn_list_pad.news_20191022.41397":"IFRS Version",
        "https://smart-home.haier.com/cn/aggg/":"A Share Announcements",
        "https://smart-home.haier.com/en/dggg/?spm=inverstor.en_ggyxw_pc.news_20191022.40509":"D share Announcements",
        "https://smart-home.haier.com/en/ahr/":"AD hoc Release",
        "https://smart-home.haier.com/cn/hggg/?spm=inverstor.cn_ggyxw_pc.news_20191022.41111":"H share Announcements",
        "https://smart-home.haier.com/cn/gpxx/yjbg/?spm=inverstor.cn_gpxx_pad.yjbg.40492":"China Accounting",
        "https://smart-home.haier.com/en/gpxx/msg/?spm=inverstor.cn_list_pad.news_20191022.42814":"Coporate communication",
        "https://smart-home.haier.com/en/sm/":"Share Holding metting",
        "https://smart-home.haier.com/en/dd/":"Director Dealing",
        "https://smart-home.haier.com/en/vra/":"Voating Rights",
    }

    api_start_urls = {
        'https://smart-home.haier.com/en/xwycjrl/gsxw/?spm=inverstor.cn_ggyxw_pad.news_20191022.40493': {
            'url': 'https://smart-home.haier.com/en/xwycjrl/gsxw/datalist.json',
        },
        'https://smart-home.haier.com/cn/gpxx/yjbg/?spm=inverstor.cn_gpxx_pad.yjbg.40492': {
            'url': 'https://smart-home.haier.com/cn/gpxx/yjbg/datalist.json',
        },
        'https://smart-home.haier.com/cn/gpxx/iv/?spm=inverstor.cn_list_pad.news_20191022.41397': {
            'url': 'https://smart-home.haier.com/cn/gpxx/iv/datalist.json',
        },
        'https://smart-home.haier.com/cn/aggg/': {
            'url': 'https://asia.tools.euroland.com/tools/Pressreleases/Main/GetNews/',
            "payload": {
                "strDateFrom": "30/03/2000",
                "strDateTo": "",
                "typeFilter": "",
                "orderBy": "0",
                "pageIndex": "0",
                "pageJummp": "10",
                "hasTypeFilter": "False",
                "searchPhrase": "",
                "companyCode": "cn-600690",
                "onlyInsiderInfo": "False",
                "lang": "zh-CN",
                "v": "cn-600690_v2_12",
                "alwaysIncludeInsiders": "False",
                "strYears": ""
            }
        },

        'https://smart-home.haier.com/en/dggg/?spm=inverstor.en_ggyxw_pc.news_20191022.40509': {
            'url': 'https://smart-home.haier.com/en/dggg/datalist.json',
        },

        'https://smart-home.haier.com/en/ahr/': {
            'url': 'https://smart-home.haier.com/en/ahr/datalist.json',
        },

        'https://smart-home.haier.com/cn/hggg/?spm=inverstor.cn_ggyxw_pc.news_20191022.41111': {
                        "payload": {
                "strDateFrom": "01/01/2025",
                "strDateTo": "",
                "typeFilter": "",
                "orderBy": "0",
                "pageIndex": "0",
                "pageJummp": "10",
                "hasTypeFilter": "False",
                "searchPhrase": "",
                "companyCode": "cn-600690",
                "onlyInsiderInfo": "False",
                "lang": "zh-CN",
                "v": "Hshare_v2",
                "alwaysIncludeInsiders": "False",
                "strYears": ""
            }

        },

        'https://smart-home.haier.com/cn/gpxx/yjbg/?spm=inverstor.cn_gpxx_pad.yjbg.40492': {
            'url': 'https://smart-home.haier.com/cn/gpxx/yjbg/datalist.json',
        },

        'https://smart-home.haier.com/en/gpxx/msg/?spm=inverstor.cn_list_pad.news_20191022.42814': {
            'url': 'https://smart-home.haier.com/en/gpxx/msg/datalist.json',
        },

        'https://smart-home.haier.com/en/sm/': {
            'url': 'https://smart-home.haier.com/en/sm/datalist.json',
        },

        'https://smart-home.haier.com/en/dd/': {
            'url': 'https://smart-home.haier.com/en/dd/datalist.json',
        },

        'https://smart-home.haier.com/en/vra/': {
            'url': 'https://smart-home.haier.com/en/vra/datalist.json',
        }

    }

    article_date_map = {}

    article_data_map = {}

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"

    @property
    def timezone(self):
        return "Asia/Shanghai"

    custom_settings = {
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter'
    }

    def parse_intermediate(self, response):
        start_url = response.request.meta['start_url']
        api_data = self.api_start_urls.get(start_url)
        if not api_data:
            self.logger.error(f"No API configuration found for start_url: {start_url}")
            return
        if start_url in [
            "https://smart-home.haier.com/en/xwycjrl/gsxw/?spm=inverstor.cn_ggyxw_pad.news_20191022.40493",
            "https://smart-home.haier.com/cn/gpxx/yjbg/?spm=inverstor.cn_gpxx_pad.yjbg.40492",
            "https://smart-home.haier.com/cn/gpxx/iv/?spm=inverstor.cn_list_pad.news_20191022.41397",
            "https://smart-home.haier.com/en/dggg/?spm=inverstor.en_ggyxw_pc.news_20191022.40509",
            "https://smart-home.haier.com/en/ahr/"
        ]:
            api_url = api_data["url"]
            yield scrapy.Request(
                url=api_url,
                method="GET",
                headers={"Content-Type": "application/x-www-form-urlencoded"},
                callback=self.get_articles,
                meta={"start_url": start_url},
                dont_filter=True,
            )
        else:
            start_url = response.meta.get("start_url")
            api_data = self.api_start_urls.get(start_url)
            if not api_data:
                self.logger.error(f"No API configuration found for start_url: {start_url}")
                return
            api_url = api_data["url"]
            payload = api_data["payload"]
            payload["pageIndex"] = "0" 
            yield scrapy.FormRequest(
                url=api_url,
                method="POST", 
                headers={"Content-Type": "application/x-www-form-urlencoded"},
                formdata=payload,
                callback=self.get_articles,
                meta={
                    "start_url": start_url,
                    "api_url": api_url,
                    "payload": payload,
                    "current_page": 0,
                },
                dont_filter=True,
            )

    def get_articles(self, response) -> list:
        start_url = response.meta.get("start_url")
        data = response.json()
        requests = []
        
        if start_url in [
            "https://smart-home.haier.com/en/xwycjrl/gsxw/?spm=inverstor.cn_ggyxw_pad.news_20191022.40493",
            "https://smart-home.haier.com/cn/gpxx/yjbg/?spm=inverstor.cn_gpxx_pad.yjbg.40492",
            "https://smart-home.haier.com/cn/gpxx/iv/?spm=inverstor.cn_list_pad.news_20191022.41397",
            "https://smart-home.haier.com/en/dggg/?spm=inverstor.en_ggyxw_pc.news_20191022.40509",
            "https://smart-home.haier.com/en/ahr/"
        ]:
            for entry in data:
                url = entry.get("Url", "").strip()
                title = entry.get("Name", "").strip()
                date = entry.get("Time", "").strip()
                pdf_name = entry.get("pdf", "").strip()
                cleaned_url = self.get_href(url)
                if cleaned_url:
                    full_url = response.urljoin(cleaned_url)
                    self.article_date_map[full_url] = date
                    self.article_data_map[full_url] = {"title": title, "pdf": [response.urljoin(pdf_name) if pdf_name else None]}
                    requests.append(scrapy.Request(
                        url=full_url,
                        callback=self.parse_article,
                        meta={"start_url": start_url, "entry": {"url": full_url, "title": title}},
                        dont_filter=True,
                    ))
        else:
            news_items = data.get("News", [])
            self.logger.info(f"News items from API: {news_items}")
            for item in news_items:
                article_id = item.get("ID") 
                title = item.get("title")
                date = item.get("formatedDate")
                if article_id and title and date:
                    article_url = f"https://asia.tools.euroland.com/tools/Pressreleases/GetPressRelease/?ID={article_id}&lang=zh-CN&companycode=cn-600690&v=cn-600690_v2_12"
                    self.article_date_map[article_url] = date
                    self.article_data_map[article_url] = {"title": title, "pdf": [article_url]}
                    requests.append(scrapy.Request(
                        url=article_url,
                        callback=self.parse_article,
                        meta={"start_url": start_url, "entry": {"url": article_url, "title": title}},
                        dont_filter=True,
                    ))
            
            if self.get_page_flag():
                pagination_requests = list(self.go_to_next_page(response))
                requests.extend(pagination_requests)
                
        return requests

    def get_href(self, entry) -> str:
        if isinstance(entry, str):
            match = re.search(r"window\.location\.href\s*=\s*encodeURI\('([^']+)'", entry)
            if match:
                return match.group(1)
        return ""

    def get_title(self, response):
        entry = response.meta.get("entry")
        if isinstance(entry, dict) and 'url' in entry:
            entry_url = entry['url']
        elif isinstance(entry, str):
            entry_url = entry
        else:
            return ""
        title = self.article_data_map.get(entry_url, {}).get("title")
        if title:
            return title.strip()
        else:
            self.logger.warning(f"No title found for URL {entry_url}")
        return ""

    def get_body(self, response):
        return " "

    def get_images(self, response):
        return []

    def date_format(self) -> str:
        return '%Y-%m-%d'

    def get_date(self, response):
        entry = response.meta.get("entry")
        if isinstance(entry, dict) and 'url' in entry:
            entry_url = entry['url']
        else:
            entry_url = entry if isinstance(entry, str) else response.url
        date_str = self.article_date_map.get(entry_url)
        if date_str:
            try:
                return date_str.strip()
            except ValueError:
                self.logger.warning(f"Date format error for URL {response.url}: {date_str}")
        return None

    def get_authors(self, response):
        return []

    def get_document_urls(self, response, entry=None):
        entry = response.meta.get("entry")
        if isinstance(entry, dict) and 'url' in entry:
            entry_url = entry['url']
        elif isinstance(entry, str):
            entry_url = entry
        else:
            return []
        return self.article_data_map.get(entry_url, {}).get("pdf", [])

    def get_page_flag(self) -> bool:
        return False

    def go_to_next_page(self, response) -> list:
        start_url = response.meta.get("start_url")
        payload = response.meta.get("payload")
        current_page = response.meta.get("current_page", 0)
        api_url = response.meta.get("api_url")
        data = response.json()
        total_items = data.get("total", 0)
        items_per_page = 10
        total_pages = (total_items // items_per_page) + (1 if total_items % items_per_page > 0 else 0)
        
        requests = []
        
        if current_page >= total_pages:
            self.logger.info(f"Reached the last page: {current_page}")
            return requests
            
        next_page = current_page + 1
        new_payload = dict(payload)
        new_payload["pageIndex"] = str(next_page)
        
        if start_url in [
            "https://smart-home.haier.com/en/xwycjrl/gsxw/?spm=inverstor.cn_ggyxw_pad.news_20191022.40493",
            "https://smart-home.haier.com/cn/gpxx/yjbg/?spm=inverstor.cn_gpxx_pad.yjbg.40492",
            "https://smart-home.haier.com/cn/gpxx/iv/?spm=inverstor.cn_list_pad.news_20191022.41397",
            "https://smart-home.haier.com/en/ahr/"
        ]:
            headers = {
                "Content-Type": "application/x-www-form-urlencoded",
                "Accept": "application/json",
            }
            requests.append(scrapy.Request(
                url=api_url,
                method="GET",
                headers=headers,
                callback=self.get_articles,
                meta={
                    "start_url": start_url,
                    "api_url": api_url,
                    "payload": new_payload,
                    "current_page": next_page,
                },
                dont_filter=True,
            ))
        else:
            headers = {
                "accept": "application/json, text/javascript, */*; q=0.01",
                "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
                "origin": "https://asia.tools.euroland.com",
                "referer": "https://asia.tools.euroland.com/tools/pressreleases/?companycode=cn-600690&v=cn-600690_v2_12&lang=zh-cn",
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "x-requested-with": "XMLHttpRequest"
            }
            requests.append(scrapy.FormRequest(
                url=api_url,
                method="POST",
                headers=headers,
                formdata=new_payload,
                callback=self.get_articles,
                meta={
                    "start_url": start_url,
                    "api_url": api_url,
                    "payload": new_payload,
                    "current_page": next_page,
                },
                dont_filter=True
            ))
            
        return requests