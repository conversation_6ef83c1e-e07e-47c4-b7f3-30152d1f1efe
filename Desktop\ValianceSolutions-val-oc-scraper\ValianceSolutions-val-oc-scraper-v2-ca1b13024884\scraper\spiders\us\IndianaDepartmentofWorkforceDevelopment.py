from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
import datetime
import scrapy
import re 

class IndianaDepartmentOfWorkforceDevelopment(OCSpider):
    name = "IndianaDepartmentOfWorkforceDevelopment"

    country = "US"

    HEADLESS_BROWSER_WAIT_TIME = 5000  # 5 seconds wait time

    custom_settings = {
    "DOWNLOADER_MIDDLEWARES": {
        'scraper.middlewares.HeadlessBrowserProxy': 350,
        'scrapy.downloadermiddlewares.redirect.RedirectMiddleware': 600
    },
    "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    "DOWNLOAD_DELAY": 5
}

    start_urls_names = {
        "https://www.in.gov/dwd/newsroom/news-and-articles/": "Newsroom",
    }
    
    def parse_intermediate(self, response):
        current_year = datetime.datetime.now().year
        for year in range(2017, current_year + 1): 
            url = f"https://www.in.gov/dwd/newsroom/news-and-articles/#{year}"
            yield scrapy.Request(url, callback=self.parse, meta={
                'start_url': url,
                'year': year,
                'dont_redirect': False,
            })
    
    charset = "utf-8"

    @property
    def language(self): 
        return "English"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "America/Chicago"
    
    def get_articles(self, response):
        links = response.xpath('//div[contains(@class, "columns")]/p/a/@href').getall()
        unique_links = list(set(links))
        total_links = len(unique_links)
        for start_idx in range(0, total_links, 100):
            return unique_links[start_idx:start_idx + 100]
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1//text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//section[@id="content_container_876258"]//text()').getall())

    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return"%m-%d-%Y"
    
    def get_date(self, response) -> str:
        paragraphs = response.xpath('//p//text()').getall()
        text = " ".join(paragraphs).strip()
        date_match = re.search(r'\b([A-Z][a-z]+\.? \d{1,2},? \d{4})\b', text)
        if date_match:
            date_str = date_match.group(1).replace(".", "").replace(",", "")
            try:
                return datetime.datetime.strptime(date_str, "%B %d %Y").strftime("%m-%d-%Y")
            except ValueError:
                try:
                    return datetime.datetime.strptime(date_str, "%b %d %Y").strftime("%m-%d-%Y")
                except ValueError as e:
                    print(f"Date parsing error: {e}")
                    return None
        else:
            return None

    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        # No next page to scrape
        return None