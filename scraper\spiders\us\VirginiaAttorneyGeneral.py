from typing import Optional
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
from datetime import datetime

class VirginiaAttorneyGeneral(OCSpider):
    name = "VirginiaAttorneyGeneral"

    country = "US"

    start_urls_names = {
        "https://www.oag.state.va.us/media-center/news-releases": "News Releases",
    }
    
    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self): 
        return "English"

    @property
    def timezone(self):
        return "America/New_York"
    
    article_date_map = {}  # Mapping date with articles from start URL

    def get_articles(self, response) -> list:
        # Extract articles from function
        self.extract_articles_with_dates(response)
        return [response.urljoin(link) for link in response.xpath("//tbody//tr//th[@class='list-title']/a/@href").getall()]
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//main[@id="maincontent"]/p[2]//text()').get() 

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="com-content-article__body"]//p[not(@style="text-align: center")]//text()').getall()) 
        
    def get_images(self, response) -> list:
        return response.xpath('//div[@class="com-content-article__body"]//p[@class="center"]/img/@src').getall()
    
    def date_format(self) -> str:
        return"%m-%d-%Y"
    
    def get_date(self, response) -> str:
        Raw_date = self.article_date_map.get(response.url, "").strip()
        date_str = Raw_date.split(" - ")[0].strip()
        date_obj = datetime.strptime(date_str, "%B %d, %Y")  
        return date_obj.strftime("%m-%d-%Y")
        
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]: 
        next_page =  response.xpath('//ul[@class="pagination ms-0 mb-4"]//li/a[@aria-label="Go to next page"]/@href').get()
        if next_page:
            return response.urljoin(next_page)
        else:
            return None
 
    def extract_articles_with_dates(self, response):
        # Function to extract dates of respective articles from start URL
        for article in response.xpath("//tbody//tr//th[@class='list-title']/a"):
            url = article.xpath("./@href").get()
            full_url = response.urljoin(url)
            date_text = article.xpath("text()").get()
            if date_text:
                date = date_text.strip().split(" - ")[0]  # Extract the date part before " - "
                self.article_date_map[full_url] = date
        return self.article_date_map