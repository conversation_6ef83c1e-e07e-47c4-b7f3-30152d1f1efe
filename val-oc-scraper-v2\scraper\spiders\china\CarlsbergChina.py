from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime

class <PERSON><PERSON><PERSON>a(OCSpider):
    name = "<PERSON><PERSON><PERSON><PERSON><PERSON>"

    start_urls_names = {
        "https://www.carlsbergchina.com.cn/zh/%E6%96%B0%E9%97%BB%E4%B8%AD%E5%BF%83/%E5%85%A8%E9%83%A8%E6%96%B0%E9%97%BB/": "重庆啤酒",
        "https://www.sse.com.cn/aboutus/mediacenter/hotandd/": "重庆啤酒",
    }

    charset = "utf-8"
    
    current_page =2

    @property
    def source_type(self) -> str:
        return "SOE"
    
    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="news-list__result"]//a/@href | //div[@class="sse_list_1 js_listPage"]//dl/dd/a/@href').getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h2[@class="page__heading news-page__heading"]/text() | //div[@class="article-infor "]//h2/text()').get().strip()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('///div[@class="rte rte--no-pad"]//p//text() | //div[@class="allZoom"]//p//text()').getall())
    
    def get_images(self, response) -> list:
        return response.xpath('//div[@class="rte rte--no-pad"]//img/@src').getall()
    
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date_str = response.xpath('//time[@class="news-page__time"]/text() | //div[@class="article-infor "]//div[@class="article_opt"]/i/text()').get()
        if date_str:
            date_str = date_str.strip()
            for fmt in ["%d.%m.%y", "%Y-%m-%d"]:
                try:
                    date_obj = datetime.strptime(date_str, fmt)
                    return date_obj.strftime(self.date_format())
                except ValueError:
                    continue
            return date_str
        return ""
             
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]: 
        url = response.url
        if url =="https://www.carlsbergchina.com.cn/zh/%E6%96%B0%E9%97%BB%E4%B8%AD%E5%BF%83/%E5%85%A8%E9%83%A8%E6%96%B0%E9%97%BB/":
            next_page = response.xpath('//a[@aria-label="下一步"]//@href').get()
            if next_page:
                return next_page
            return None
        else:
            current_page=self.current_page
            next_page = f'https://www.sse.com.cn/aboutus/mediacenter/hotandd/s_list_{current_page}.shtml'
            if current_page<=108:
                current_page+=1
                return next_page
            return None    