from typing import List
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class SseThreeSixty(OCSpider):
    name = "SseThreeSixty"

    start_urls_names = {
         "http://www.sse.com.cn/assortment/stock/list/info/announcement/index.shtml?productId=601360": "SSE Announcements",
        "https://www.360.cn/news.html": "360 News",
    }

    charset = "utf-8"


    @property
    def source_type(self) -> str:
        return "SOE"
    
    @property
    def timezone(self) -> str:
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="txt"]').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response, entry=None) -> str:
        return response.xpath('//title/text()').get().strip()
    
    def get_body(self, response, entry=None) -> str:
        return body_normalization(response.xpath('//div[@class="content-text"]//p//text()').getall())
    
    def date_format(self) -> str:
        return "%Y-%m-%d"
    
    def get_date(self, response, entry=None) -> int:
        date = response.xpath('//div[@class="article-info"]').get()
        return date
    
    def get_authors(self, response, entry=None) -> List[str]:
        return []
    
    def get_document_urls(self, response, entry=None) -> list:
        return response.xpath('//a[contains(@href, ".pdf")]/@href').getall()
    
    def get_images(self, response, entry=None) -> List[str]:
        return response.xpath('//img/@src').getall()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response): 
        return response.xpath('//li[@data-label="Load More"]//a/@href').get()