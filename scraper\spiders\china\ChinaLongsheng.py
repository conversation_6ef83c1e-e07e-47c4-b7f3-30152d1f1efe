from datetime import datetime
from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ChinaLongsheng(OCSpider):
    name = "ChinaLongsheng"

    start_urls_names = {
        "https://www.longsheng.com/news": "浙江龙盛",
        "https://www.longsheng.com/notice": "浙江龙盛",
        "https://www.longsheng.com/periodic": "浙江龙盛"
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "private_enterprise"
    
    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        return  response.xpath('//div[@class="contentCon newsCon"]//ul/li/a/@href | //div[@class="contentCon noticeCon"]//ul/li/a/@href').getall()
       
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//div[@class="contentCon newsCon"]//h4//text() | //div[@class="contentCon newsCon"]//table//tbody//tr/td//a//text()').get()
    
    def get_body(self, response) -> str:
        return  body_normalization(response.xpath('//div[@class="contentCon newsCon"]//p//text()').getall())
    
    def get_images(self, response) -> list:
        return []
    
    def date_format(self) -> str:
        return '%Y.%m.%d'
    
    def get_date(self, response) -> str:
        date_str = response.xpath('//div[@class="contentCon newsCon"]//h4//em/text() | //div[@class="contentCon newsCon"]//table//tbody//tr[2]/td[2]//text()').get().strip()
        for fmt in ['%Y.%m.%d','%Y-%m-%d', '%d.%m.%Y', '%d-%m-%Y', '%Y/%m/%d']:
            try:
                date_obj = datetime.strptime(date_str, fmt)
                return date_obj.strftime('%Y.%m.%d')
            except ValueError:
                continue

    def get_authors(self, response):
        return []
    
    def get_document_urls(self, response, entry=None) -> list:
        return response.xpath('//div[@class="contentCon newsCon"]//table//tbody//tr/td//a/@href').get()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]: 
        next_page = response.xpath('//div[@class="pagination"]/a[contains(text(), "下一页")]/@href').get()
        if next_page:
            return response.urljoin(next_page)
        return None