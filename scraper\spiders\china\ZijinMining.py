from typing import List
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ZijinMining(OCSpider):
    name = "ZijinMining"

    start_urls_names = {
        "http://www.zjky.cn/news/news_list.jsp": "紫金矿业", #No pdf
        "http://www.zjky.cn/news/media_focus.jsp": "紫金矿业", #No pdf
        "http://www.zjky.cn/investor/year-report.jsp": "紫金矿业", 
        "http://www.zjky.cn/investor/half-report.jsp": "紫金矿业",
        "http://www.zjky.cn/investor/quarter-report.jsp": "紫金矿业",
        "http://www.zjky.cn/investor/Agu.jsp": "紫金矿业",
        "http://www.zjky.cn/investor/Hgu.jsp": "紫金矿业",
    }

    article_data_map ={}

    charset = "iso-8859-1"

    @property
    def source_type(self) -> str:
        return "SOE"
    
    @property
    def timezone(self) -> str:
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
            articles = []
            for article in response.xpath("//div[@class='bd']//ul//li | //div[@class='content']//ul//li"):
                url = article.xpath(".//a/@href").get()
                title = article.xpath(".//a//div[@class='_t']//text() | .//a//div[@class='tit el']//text()").get()
                date = article.xpath(".//a//div[@class='left']//div[@class='m_date']//text() |.//a//div[@class='txt']//div[@class='date']//text()").get()
                if url and title and date:
                    full_url = url
                    title = title.strip()
                    if '.pdf' in full_url.lower():
                        pdf = full_url
                    else:
                        pdf = "None"
                    clean_date = date.strip()
                    self.article_data_map[full_url] = {"title": title, "date": clean_date, "pdf": pdf}
                    articles.append(full_url) 
            return articles
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response, entry=None) -> str:
        return self.article_data_map.get(response.request.meta.get('entry'), {}).get("title", "")
    
    def get_body(self, response, entry=None) -> str:
        if ".pdf" in response.url.lower():
            return ""
        return body_normalization(response.xpath("//div[@class='myart']//text()").getall())
        
    def date_format(self) -> str:
        return "%Y/%m/%d"
    
    def get_date(self, response, entry=None) -> int:
        date = self.article_data_map.get(response.request.meta.get('entry'), {}).get("date", "")
        date = date.replace(" ","")
        return date
        
    def get_authors(self, response, entry=None) -> List[str]:
        return []
    
    def get_document_urls(self, response, entry=None) -> list:
        pdf_url = self.article_data_map.get(response.request.meta.get('entry'), {}).get("pdf")
        return [f'http://www.zjky.cn/investor{pdf_url}'] if pdf_url and pdf_url != "None" else []
    
    def get_images(self, response, entry=None) -> List[str]:
        if ".pdf" in response.url.lower():
            return ""
        return response.xpath("//div[@class='myart']//img//@src").getall()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> List[str]:
        next_page= response.xpath("//div[@class='util-page page']//a[@class='gobutton next']//@href").get()
        if next_page:
            return next_page
        return None