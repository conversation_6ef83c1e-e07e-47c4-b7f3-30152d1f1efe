from typing import Optional
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class <PERSON><PERSON><PERSON><PERSON><PERSON>(OCSpider):
    name = "<PERSON><PERSON><PERSON><PERSON><PERSON>"

    start_urls_names = {
        "https://www.gtja.com/content/events/news.html": "国泰君安",
        "https://www.gtja.com/content/events/media.html": "国泰君安",
        "https://www.gtja.com/content/notice/personal.html": "国泰君安",
        "https://www.gtja.com/content/notice/corporate.html": "国泰君安",
        "https://www.gtja.com/content/research.html": "国泰君安",
    }

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
            'scraper.middlewares.HeadlessBrowserProxy': 350,
        },
    }
    
    HEADLESS_BROWSER_WAIT_TIME = 10000

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "SOE"
    
    @property
    def timezone(self):
        return "Asia/Shanghai"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="iboxp"]//a//@href | //div[@class="innerlist"]//ul[@class="noticelist notice"]//li//a//@href').getall()
        
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h2[@class="titd"]//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="txts"]//p//text() | //div[@class="detail-notice gold-arc article-detail"]//p//text()').getall())
     
    def date_format(self) -> str:
        return"%Y-%m-%d"
    
    def get_date(self, response) -> str:
        return response.xpath('//span[@id="time"]//text()').re_first(r'\d{4}-\d{2}-\d{2}')
    
    def get_images(self, response) -> list:
        return response.xpath("//div[@class='detail-notice gold-arc article-detail']//img//@src").getall()
  
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> Optional[str]: 
        return None