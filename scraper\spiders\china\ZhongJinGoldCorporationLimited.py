from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from typing import List

class ZhongJinGoldCorporationLimited(OCSpider):
    name = "<PERSON>hong<PERSON>inGoldCorporationLimited"

    start_urls_names = {
        "https://zjgold.chinagoldgroup.com/3190.html": "中金黄金",
        "https://zjgold.chinagoldgroup.com/3232.html":"中金黄金",
        "https://zjgold.chinagoldgroup.com/3191.html":"中金黄金",
        "https://zjgold.chinagoldgroup.com/3209.html" :"中金黄金",
        "https://zjgold.chinagoldgroup.com/3222.html":"中金黄金",
    }

    custom_settings = {
    "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',
    }

    charset = "iso-8859-1"

    @property
    def source_type(self) -> str:
        return "SOE"
    
    @property
    def timezone(self):
        return "Asia/Shanghai"

    article_data_map = {}

    def get_articles(self, response):
        self.extract_articles_with_dates(response)
        return list(self.article_data_map.keys())

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return self.article_data_map[response.request.meta.get('entry')].get("title")
    
    def get_body(self, response) -> str:
        if '.pdf' in response.url.lower():
            return ''
        return body_normalization(response.xpath("//p[@class='indent']//text() | //p//text()").getall())

    def get_authors(self, response):
        return []
    
    def date_format(self) -> str:
        return '%Y-%m-%d'
    
    def get_date(self, response) -> str:
        return self.article_data_map[response.request.meta.get('entry')].get("date")
    
    def get_document_urls(self, response, entry=None) -> List[str]:
        if ".pdf" not in response.url.lower():
            pdf = response.xpath("//p//a[contains(@href,'.pdf')]//@href").getall()
            pdfs=[]
            for i in pdf:
                if "https://zjgold.chinagoldgroup.com" not in i:
                    i =f'https://zjgold.chinagoldgroup.com{i}'
                    pdfs.append(i)
            return pdfs
        return self.article_data_map[response.request.meta.get('entry')].get("pdf")
    
    def get_images(self, response) -> list:
        if ".pdf" in response.url.lower():
            return []
        return response.xpath("//div[@class='detail-content']//@src").getall()
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) -> str:
        next_page = response.xpath('//a[@class="i-pager-next"]/@href').get()
        if next_page:
            full_url = response.urljoin(next_page)
            return full_url
        return None
    
    def extract_articles_with_dates(self, response):
        mapping = {}
        articles = response.xpath("//div[@class='second-news-item']")
        for article in articles:
            a_tag = article.xpath(".//div[@class='second-news-item-title']/a")
            url = a_tag.xpath("./@href").get()
            title = a_tag.xpath("./@title").get()
            date = article.xpath(".//div[@class='second-news-item-date']//text()").get()
            if url and title and date:
                full_url = response.urljoin(url.strip())
                clean_date = date.strip()
                mapping[full_url] = {
                    "title": title.strip(),
                    "date": clean_date,
                    "pdf": [full_url] if url.endswith(".pdf") else []
                }
        self.article_data_map.update(mapping)
        return self.article_data_map