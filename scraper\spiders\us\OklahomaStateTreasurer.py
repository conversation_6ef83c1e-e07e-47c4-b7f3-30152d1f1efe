from datetime import datetime
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization
import scrapy
import json
import logging
from typing import Optional

class OklahomaStateTreasurer(OCSpider):

    name = "OklahomaStateTreasurer"

    country="US"

    start_urls_names = {
        "https://oklahoma.gov/treasurer/news-releases.html":"News Releases",
    }

    api_start_urls = {
        'https://oklahoma.gov/treasurer/news-releases.html': {
            "url" : "https://oklahoma.gov/content/sok-wcm/en/treasurer/news-releases/jcr:content/responsivegrid-second/newslisting.json?page={current_page}&q=",
            "payload" : {
                "q" : "",
                "page" : "1"
            }
        }
    }

    def parse_intermediate(self, response):
        start_url=response.meta.get('start_url')
        current_page = response.meta.get("current_page",1)
        api_data=self.api_start_urls.get(start_url)
        api_url = api_data["url"].format(current_page=current_page)
        yield scrapy.Request(
            url=api_url,
            callback=self.parse,
            headers={
                'content-type' : 'application/json'
            },
            dont_filter=True,
            meta={
                "start_url": start_url,
                "api_url": api_data["url"],
                "current_page": current_page,
            },
        )

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return 'ministry'
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Central"
    
    def get_articles(self, response) -> list:
        data=json.loads(response.text)
        if data:
            article_urls=[]
            for item in data.get('items',{}):
                url=item.get('newsUrl')
                article_urls.append(url)
        return article_urls
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath('//h1[@class="cmp-title__text"]//text()').get()
       
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="cmp-text"]//p/text()').getall())

    def date_format(self) -> str:
        return "%A,%Y-%m-%d"
    
    def get_date(self, response) -> str:
        date = response.xpath('//div[@class="caption bottom-margin cmp-last-modified-date--large__text left-right-padding"]/span//text()').get()
        date_obj = datetime.strptime(date, "%A, %B %d, %Y")
        formatted_date = date_obj.strftime("%A,%Y-%m-%d")
        return formatted_date

    def get_images(self, response, entry=None) :
        return []
    
    def get_authors(self, response, entry=None) -> list[str]:
        return []
    
    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response,current_page) -> str:
        if response.status != 200:
            self.logger.error(f"Error {response.status}: Not incrementing page.")
            return None
        current_page = int(response.meta.get('current_page'))+1
        return str(current_page)
    
    def go_to_next_page(self, response, start_url, current_page: Optional[str] = "1"):
        api_url = response.meta.get("api_url")
        if not api_url:
            logging.info("API URL not found in meta data.")
            return None
        next_page = self.get_next_page(response, current_page)
        if next_page:
            api_url = api_url.format(current_page=next_page)
            yield scrapy.Request(
                url=api_url,
                method='GET',
                callback=self.parse_intermediate, 
                dont_filter=True,
                meta={
                        "api_url": api_url,
                        'current_page': next_page, 
                        'start_url': start_url,
                    }
            )
        else:
            logging.info("No more pages to fetch.")
            yield None