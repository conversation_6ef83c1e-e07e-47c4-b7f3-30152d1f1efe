from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ChinaInternationalCultureAssociation(OCSpider):
    name = "ChinaInternationalCultureAssociation"

    start_urls_names = {
        'https://cn.chinaculture.org/site/wenhua/dongtai.html': '动  态',
        'https://cn.chinaculture.org/site/wenhua/gonggao.html': '公  告',
    }
    
    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return 'IndustryAssociation'

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="fl tcon"]//h2/a/@href').getall()

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//div[@class="content"]//h1//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="detail"]//p//text()').getall())

    def date_format(self) -> str:
        return '%Y-%m-%d'

    def get_date(self, response) -> str:
        return response.xpath("//div[@class='infos']//text()").re_first(r"\d{4}-\d{2}-\d{2}")

    def get_images(self, response) -> list:
        return [
            response.urljoin(url)
            for url in
            response.xpath('//div[@class="detail"]//p//img/@src').getall()
        ]
    
    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        next_page = response.xpath('//div[@class="pages"]//a[contains(text(), "下一页")]/@href').get()
        if next_page:
            next_page_url = response.urljoin(next_page)
            if next_page_url == response.url:
                return None
            return next_page_url
        return None