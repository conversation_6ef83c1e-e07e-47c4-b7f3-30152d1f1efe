from typing import List
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime
from urllib.parse import urljoin
import re, json

class AlaskaGovernorNewsroom(OCSpider):
    name = "alaskagovernor"
    country = "US"

    start_urls_names = {
        "https://gov.alaska.gov/newsroom/": "Alaska Governor Newsroom",
    }

    custom_settings = {
        "DOWNLOAD_DELAY": 2,
    }

    @property
    def source_type(self): return "Government"
    @property
    def language(self): return "English"
    @property
    def timezone(self): return "America/Anchorage"
    def get_page_flag(self): return True

    def get_articles(self, response) -> list:
        return response.xpath('//article//h4/a/@href').getall()

    def get_title(self, response) -> str:
        return next((response.xpath(x).get().strip() for x in [
            '//h1/text()', '//article/h1/text()', '//h2/text()'
        ] if response.xpath(x).get()), None)

    def get_body(self, response) -> str:
        for sel in [
            '//div[@class="entry-content"]//p/text()',
            '//article//p/text()',
            '//div[@class="entry-content"]//text()'
        ]:
            parts = response.xpath(sel).getall()
            if parts: return body_normalization(parts)

    def get_date(self, response) -> int:
        for sel in ['//article//time/text()', '//span[@class="date"]/text()']:
            val = response.xpath(sel).get()
            if val:
                try: return int(datetime.strptime(val.strip(), "%b %d, %Y").timestamp())
                except: pass

        match = re.search(r'/(\d{4})/(\d{2})/(\d{2})/', response.url)
        if match:
            y, m, d = map(int, match.groups())
            return int(datetime(y, m, d).timestamp())

        return int(datetime.now().timestamp())

    def get_authors(self, response) -> List[str]:
        val = response.xpath('//span[@class="author"]/text() | //div[@class="author"]/text()').get()
        return [val.strip()] if val else []

    def get_images(self, response) -> List[str]:
        return [
            urljoin(response.url, i)
            for i in response.xpath(
                '//div[@class="entry-content"]//img/@src | //div[@class="featured-image"]//img/@src'
            ).getall()
        ]

    def get_next_page(self, response) -> List[str]:
        next_link = response.xpath(
            '//a[@class="next page-numbers"]/@href | //a[contains(text(), "Load More")]/@href'
        ).get()
        return [next_link] if next_link else []

    def get_document_urls(self, response) -> list:
        return [
            urljoin(response.url, i)
            for i in response.xpath(
                '//div[@class="entry-content"]//a[contains(@href, ".pdf") or contains(@href, ".doc") or contains(@href, ".docx")]/@href'
            ).getall()
        ]

    def parse(self, response):
        yield from response.follow_all(self.get_articles(response), self.parse_article)
        yield from response.follow_all(self.get_next_page(response), self.parse)

    def parse_article(self, response):
        article = {
            'url': response.url,
            'title': self.get_title(response),
            'body': self.get_body(response),
            'images': json.dumps(self.get_images(response)),
            'date': datetime.fromtimestamp(self.get_date(response)).strftime('%Y-%m-%d %H:%M:%S'),
            'document_urls': json.dumps(self.get_document_urls(response)),
            'authors': ', '.join(self.get_authors(response)),
        }
        stats = self.crawler.stats
        stats.inc_value('articles_crawled')
        stats.inc_value('articles_successfully_scraped')
        self.logger.info(f"Extracted: {article['title']}")
        return article

    def close(self, reason):
        self.logger.info(f"Spider closed: {self.name}, reason: {reason}")
