from typing import Optional, List
from scraper.OCSpider import <PERSON><PERSON>pider
from scraper.utils.helper import body_normalization
from datetime import datetime
import scrapy

class HongtaSecurities(OCSpider):
    name = "HongtaSecurities"

    start_urls_names = {
        "https://www.hongtastock.com/tzzgx/dqbg/index_1.html": "NEWS",
        
    }


    proxy_country = "cn"

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES":
            {
                'scraper.middlewares.GeoProxyMiddleware': 350,
            },
        "DOWNLOAD_DELAY": 2,
    }


    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_articles(self, response) -> list:
        articles = response.xpath('//dl[@class="newsList"]//dd//a[@href]/@href').getall()
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response, entry=None) -> str:
        return response.xpath('//div[@class="articleTitle"]/text()').get()

    def get_body(self, response, entry=None) -> str:
        return ""

    def get_images(self, response, entry=None) -> List[str]:
        return []

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response, entry=None) -> str:
        date_text = response.xpath('//div[@class="articleTime"]/span[1]/text()').get()
        clean_date = date_text.replace("日期：", "").strip()
        return clean_date

    def get_authors(self, response, entry=None) -> List[str]:
        return []
    
    def get_document_urls(self, response, entry=None) -> list:
        pdf_urls = response.xpath('//div[@class="articleHand"]//a[@class="pdf"]/@href').getall()
        return [response.urljoin(url) for url in pdf_urls]

    def get_page_flag(self) -> bool:
        return False  

    def get_next_page(self, response) -> str:
        current_page = response.meta.get('current_page', 1)
        next_page_number = int(current_page) + 1
        base_url = response.meta['start_url'].rsplit('/', 1)[0] + '/'
        next_page_url = f"{base_url}index_{next_page_number}.html"
        return next_page_url
