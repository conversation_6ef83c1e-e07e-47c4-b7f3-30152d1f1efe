from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization

class NewHampshireDepartmentOfTransportation(OCSpider):
    name = 'NewHampshireDepartmentOfTransportation'

    country = "US"
    
    custom_settings = {
		"DOWNLOADER_MIDDLEWARES" : {
			'scraper.middlewares.HeadlessBrowserProxy': 350,
		},
		"DOWNLOAD_DELAY" : 5,
	}
    
    HEADLESS_BROWSER_WAIT_TIME = 30000   # 30 Seconds wait time
    
    start_urls_names = {
        'https://www.dot.nh.gov/news-and-media': "News"
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Central"

    def get_articles(self, response) -> list:
        articles = response.xpath("//div[@class='title']//a//@href").getall()
        return [response.urljoin(link) for link in articles]
    
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//h1//text()").get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='field field--name-body field--type-text-with-summary field--label-hidden field__item']//p//text()").getall())
    
    def get_images(self, response) -> list:
        return ""
    
    def date_format(self) -> str:
        return '%B %d, %Y'

    def get_date(self, response) -> str:
        return response.xpath("//div[@class='date']//span/following::text()[1]").get(default="").strip()
        
    def get_authors(self, response):
        return response.xpath("//div[@class='office-info']//span[@itemprop= 'contactPoint']/text()").getall()

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        next_page = response.xpath("//li//a[@title='Next']//@href").get()
        if next_page:
            return response.urljoin(next_page) 
        else:
            return None