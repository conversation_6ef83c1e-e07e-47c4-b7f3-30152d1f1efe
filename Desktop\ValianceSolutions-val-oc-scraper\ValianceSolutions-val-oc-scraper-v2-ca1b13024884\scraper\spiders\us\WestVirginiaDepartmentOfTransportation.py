from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime
import scrapy

class WestVirginiaDepartmentOfTransportation(OCSpider):
    name = "WestVirginiaDepartmentOfTransportation"

    country = "US"

    start_urls_names = {
        "https://transportation.wv.gov/" : "News"
    }

    def parse_intermediate(self, response):
        start_url = response.meta.get("start_url")
        current_year = response.meta.get("current_year",datetime.now().year)
        url = f"{start_url}/communications/PressRelease/Pages/{current_year}-Release.aspx"
        yield scrapy.Request(
            url = url,
            callback = self.parse,
            dont_filter = True, 
            meta = {
                "start_url":start_url,
                "current_year":current_year
            }
        )

    charset="utf-8"
    
    @property
    def language(self) -> str:
        return "English"
    
    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self) -> str:
        return "US/Eastern"
    
    def get_articles(self, response) :
        return response.xpath('//a[contains(text(),"read more")]/@href').getall()
    
    def get_href(self, entry: str) -> str:
        return entry

    def get_title(self, response) :
        return response.xpath('//div[@class="container-fluid p-4"]//h1//text()').get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@id="articleContent"]//p//text()').getall())

    def get_images(self, response) :
        return response.xpath('//div[@id="articleContent"]//p//img/@src').getall()

    def date_format(self) -> str:
        return "%m/%d/%Y"

    def get_date(self, response) -> str:
        return response.xpath('//div[@id="articleDate"]//text()').get().strip()
    
    def get_authors(self, response) :
        return []
    
    def get_page_flag(self) -> bool:
        return False
    
    def get_next_page(self, response) :
        current_year = int(response.meta.get("current_year"))-1
        if response.status != 200:
            self.logger.info("Response status is not 200, stopping pagination.")
            return None
        else:
            return str(current_year)
    
    def go_to_next_page(self, response, start_url, current_page=None):
        start_url = response.meta.get("start_url")
        previous_year = self.get_next_page(response)
        url=f"{start_url}/communications/PressRelease/Pages/{previous_year}-Release.aspx"
        self.logger.info(f"Next page URL: {url}")
        if previous_year:
            yield scrapy.Request(
                url = url,
                callback=self.parse_intermediate,
                meta = {
                    "start_url":start_url,
                    "current_year":previous_year
                }
            )
        else:
            self.logger.info("No more pages to scrape")
            return None