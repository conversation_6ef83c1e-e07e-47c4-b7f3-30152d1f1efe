from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime
from scraper.middlewares import HeadlessBrowserProxy
import scrapy

class NewYorkStateDepartmentofTaxationandFinance(OCSpider):
    name = "NewYorkStateDepartmentofTaxationandFinance"

    country = "US"

    start_urls_names = {
        "https://www.tax.ny.gov/press/rel/": "Press Releases"
    }

    include_rules = [r'.*www\.tax\.ny\.gov.*']

    charset = "utf-8"
    
    @property
    def source_type(self) -> str:
        return "ministry"

    @property
    def timezone(self) -> str:
        return "US/Eastern"
    
    @property
    def language(self):
        return "English"
    
    def parse_intermediate(self, response):
        hbp = HeadlessBrowserProxy()
        start_url = response.meta.get("start_url")
        current_year = response.meta.get("current_year",datetime.now().year)
        url = f"{start_url}{current_year}/"
        yield scrapy.Request(
            url = (hbp.get_proxy(url,timeout=50000)),
            callback = self.parse,
            meta = {
                "start_url":start_url,
                "current_year":current_year
            }
        )
    
    def get_articles(self, response) :
        urls = response.xpath('//li[@class="link-item"]//a/@href').getall()
        article_urls=[]
        for url in urls:
            article_urls.append(response.urljoin(url).replace("https://proxy.scrapeops.io","https://www.tax.ny.gov/"))
        return article_urls
    
    def get_href(self, entry: str) -> str:
        return entry

    def get_title(self, response) :
        return response.xpath('//h1[@class="tax-page-title"]//text()').get() or response.xpath('//h2//text()').get()  # Example: For 1st Xpath: https://www.tax.ny.gov/press/rel/2025/onemonth031425.htm,  For 2nd Xpath: https://www.cs.ny.gov/pio/pressrel/Taxpayer-Representative-Exam-Now-Open.cfm
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="tax-cols"]//text()').getall() or response.xpath('//main[@id="tax-content"]//p/text()').getall() or response.xpath('//main[@id="tax-content"]//li/text()').getall())  # Example: For 1st Xpath: https://www.tax.ny.gov/press/rel/2025/onemonth031425.htm, For 2nd Xpath: https://ag.ny.gov/press-release/2022/attorney-general-james-and-acting-tax-commissioner-hiller-announce-arrest-alleged, For 3rd Xpath: https://www.tax.ny.gov/press/rel/2022/rollownwarrant082222.htm

    def get_images(self, response) :
        return []

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response) -> str:
        return response.xpath("//time/@datetime").get()
    
    def get_authors(self, response) :
        return []

    def get_next_page(self, response) :
        current_year=int(response.meta.get("current_year"))-1
        if response.status!=200:
            return None
        else:
            return str(current_year)

    def get_page_flag(self) -> bool:
        return False
    
    def go_to_next_page(self, response, start_url, current_page=None):
        start_url = response.meta.get("start_url")
        previous_year = self.get_next_page(response)
        if previous_year:
            yield scrapy.Request(
                url=f"{start_url}{previous_year}/",
                callback=self.parse_intermediate,
                meta = {
                    "start_url":start_url,
                    "current_year":previous_year
                }
            )
        else:
            self.logger.info("No more pages to scrape")
            return None