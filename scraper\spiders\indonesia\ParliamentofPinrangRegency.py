from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class ParliamentofPinrangRegency(OCSpider):
    name = "ParliamentofPinrangRegency"
    
    country = "ID"

    start_urls_names = {
        "default": "https://dprd.pinrangkab.go.id/berita/"
    }
    
    charset = "utf-8"

    @property
    def language(self): 
        return "Indonesian"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def timezone(self):
        return "Asia/Jakarta"
    
    def get_articles(self, response) -> list:
        return response.xpath("//div[@class='elementor-post__text']//h3//a/@href").getall()

    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self, response) -> str:
        return response.xpath("//div[@class='elementor-widget-container']//h1/text()").get().strip()
        
    def get_body(self, response) -> str:
        return body_normalization(
            response.xpath("//div[@class='elementor-widget-container']//p//text()").getall()
        ) 
        
    def get_images(self, response) -> list:
        return response.xpath("//div[@class='elementor-widget-container']//img/@src").getall()
    
    def date_format(self) -> str:
        return "%d %B %Y"
    
    def get_date(self, response) -> str:
        return response.xpath("//span[contains(@class, 'elementor-post-info__item--type-date')]/time/text()").get().strip()
    
    def get_authors(self, response):
        return response.xpath("//span[contains(@class, 'elementor-post-info__item--type-author')]/text()").getall()
    
    def get_page_flag(self) -> bool:
        return False
     
    def get_next_page(self, response): 
        return None  # No pagination logic provided
