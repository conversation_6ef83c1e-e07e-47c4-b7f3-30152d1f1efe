from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization

class HawaiiDepartmentofBusinessEconomicDevelopmentAndTourism(OCSpider):
    name = 'HawaiiDepartmentofBusinessEconomicDevelopmentAndTourism'

    country = "US"

    start_urls_names = {
        "https://dbedt.hawaii.gov/news/": "News",
    }

    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def timezone(self):
        return "Pacific/Honolulu"

    @property
    def language(self):
        return "English"

    def get_articles(self, response) -> list:
        articles =list(set(response.xpath("//div[@class='scp_post clearfix']//a//@href").getall()))
        return articles 
    
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//div[@class='pagetitle']//h2//text()").get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//div[@class='primary-content']//p//text() | //div[@class='primary-content']//ul//li//text()").getall())

    def get_images(self, response) -> list:
        return []

    def date_format(self) -> str:
        return "%B %d, %Y"

    def get_date(self, response) -> str:
        date = response.xpath("//div[@class='primary-content']//p[contains(text(),': ')]/text()").get()
        date = date.split(": ", 1)[-1]
        return date
    
    def get_authors(self, response):
        return []

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        next_page = response.xpath("//div[@class='nav-previous alignleft']//a//@href").get()
        if next_page:
            return next_page
        return None