from typing import Optional, List
from scraper.OCSpider import OCSpider
from scraper.utils.helper import body_normalization
from datetime import datetime
import scrapy
import re

class ShaanxiCoalIndustryCompanyLimited(OCSpider):
    name = "ShaanxiCoalIndustryCompanyLimited"

    start_urls_names = {
       
        "https://www.shxcoal.com/news/news": "Shaanxi Coal News",
         "https://www.shxcoal.com/news/notice": "Notice Announcement",
        
    }


    proxy_country = "cn"

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES":
            {
                'scraper.middlewares.GeoProxyMiddleware': 350,
            },
        "DOWNLOAD_DELAY": 2,
    }


    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "IndustryAssociation"

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def get_articles(self, response) -> list:
        articles = response.xpath('//ul[@class="list-unstyled"]//li//div[@class="news-con"]//h1/a/@href ').getall()
        return articles

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response, entry=None) -> str:
        return response.xpath('//div[@class="danpian-h1"]//text()').get().strip()

    def get_body(self, response, entry=None) -> str:
        return body_normalization(response.xpath('//div[@class="danpian-con"]//text()').getall())

    def get_images(self, response, entry=None) -> List[str]:
        return response.xpath('//div[@class="danpian-con"]//img/@src').getall()

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response, entry=None) -> str:
        date_text = response.xpath('//div[@class="danpian-h2"]/text()').get()
        if not date_text:
            return None
        match = re.search(r'\d{4}-\d{2}-\d{2}', date_text)
        return match.group() if match else None

    def get_authors(self, response, entry=None) -> List[str]:
        return []
    

    def get_page_flag(self) -> bool:
        return False  

    def get_next_page(self, response) -> Optional[str]: 
        next_page = response.xpath('//li[@class="page-item"]/a[@rel="next"]/@href').get()
        if next_page:
            return next_page
        return None
