from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization

class OhioAttorneyGeneral(OCSpider):
    name = "OhioAttorneyGeneral"
    
    country = "US"

    proxy_country = "us"

    custom_settings = {
        "DOWNLOADER_MIDDLEWARES":
            {
                # for using geo targeted proxy, add this middleware
                'scraper.middlewares.GeoProxyMiddleware': 350,
            },
    }

    start_urls_names = {
        'https://www.ohioattorneygeneral.gov/Media/News-Releases' : "News Releases"
    }

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Eastern"

    def get_articles(self, response) -> list:
        return response.xpath('//div[@class="ohio-news"]//a/@href').extract()
   
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath('//h2[@class="news"]//text()').get()

    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="newsbody"]//p//text()').extract())

    def date_format(self) -> str:
        return '%m/%d/%Y'

    def get_date(self, response) -> str:
        return response.xpath('//div[@class="rightColumn"]//p//text()').get()

    def get_images(self, response) -> list:
        return []

    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False
     
    def get_next_page(self, response) -> str:
        return response.xpath('//a[@class="UnselectedNext"]/@href').get()