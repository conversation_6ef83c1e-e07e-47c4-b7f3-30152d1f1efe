from scraper.OCSpider import <PERSON><PERSON><PERSON><PERSON>
from scraper.utils.helper import body_normalization
from urllib.parse import urljoin
import re
from datetime import datetime
import pytz

class Gac<PERSON>pider(OCSpider):
    name = "GacSpider"
    country = "CN"

    start_urls_names = {
        "https://www.gac.com.cn/cn/news": "GAC News",
        "https://www.gac.com.cn/cn/invest/notice": "GAC Investor Notices A-Share",
        "https://www.gac.com.cn/cn/invest/notice?type=1": "GAC Investor Notices H-Share",
        "https://www.gac.com.cn/cn/csr/reportList": "GAC ESG Reports",
    }
    charset = "utf-8"
    handle_httpstatus_list = [200, 301, 302, 404, 500]

    # Custom settings for better performance
    custom_settings = {
        "DOWNLOAD_DELAY": 0.5,  # Reduced for faster crawling
        "CONCURRENT_REQUESTS_PER_DOMAIN": 8,  # Increased for better performance
        "DOWNLOAD_FAIL_ON_DATALOSS": False,
        "DOWNLOAD_TIMEOUT": 60,
        "RETRY_TIMES": 3,
        "DUPEFILTER_CLASS": 'scrapy.dupefilters.BaseDupeFilter',  # Disable duplicate filtering
        "COOKIES_ENABLED": False,  # Disable cookies for better performance
        "REDIRECT_ENABLED": True,  # Enable redirects
        "HTTPCACHE_ENABLED": True,  # Enable HTTP caching
        "HTTPCACHE_EXPIRATION_SECS": 0,  # Never expire cache
        "HTTPCACHE_DIR": 'httpcache',  # Cache directory
        "HTTPCACHE_IGNORE_HTTP_CODES": [503, 504, 505, 500, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409],  # Ignore these HTTP codes
        "HTTPCACHE_STORAGE": 'scrapy.extensions.httpcache.FilesystemCacheStorage',  # Cache storage
    }

    @property
    def source_type(self) -> str:
        return "Corporate"

    @property
    def language(self) -> str:
        return "Chinese"

    @property
    def timezone(self):
        return "Asia/Shanghai"

    def parse_intermediate(self, response):
        """Optional method for headless mode if needed"""
        return self.parse(response)

    def get_articles(self, response) -> list:
        article_links = []
        print(f"Processing URL: {response.url}")

        # Different XPath patterns based on URL
        if "news" in response.url:
            # For news page - target the news items with multiple patterns to ensure we get all articles
            # First try to get all links that contain "news/detail" or "baseid" which are article identifiers
            links = response.xpath('//a[contains(@href, "/news/detail") or contains(@href, "baseid=")]/@href').getall()

            # Try to find links in the news list container
            more_links = response.xpath('//div[contains(@class, "news-list")]//a/@href | //div[contains(@class, "news-item")]//a/@href').getall()
            # Add unique links
            for link in more_links:
                if link not in links:
                    links.append(link)

            # Get all links and filter for those that look like article links
            all_links = response.xpath('//a/@href').getall()
            for link in all_links:
                if ('detail' in link or 'baseid=' in link) and link not in links:
                    links.append(link)

            print(f"Found {len(links)} links on news page")
        elif "invest/notice" in response.url:
            # For investor notices page - try multiple patterns
            links = response.xpath('//div[contains(@class, "notice-list")]//a/@href | //a[contains(@href, "detail")]/@href | //a[contains(@href, "baseid=")]/@href | //a[contains(@href, ".pdf")]/@href').getall()

            # Get all links and filter for those that look like article links or PDF files
            all_links = response.xpath('//a/@href').getall()
            for link in all_links:
                if ('detail' in link or 'baseid=' in link or '.pdf' in link) and link not in links:
                    links.append(link)

            print(f"Found {len(links)} links on invest/notice page")
        elif "csr/reportList" in response.url:
            # For ESG reports page - try multiple patterns
            links = response.xpath('//div[contains(@class, "report-list")]//a/@href | //a[contains(@href, "detail")]/@href | //a[contains(@href, ".pdf")]/@href').getall()

            # Get all links and filter for those that look like article links or PDF files
            all_links = response.xpath('//a/@href').getall()
            for link in all_links:
                if ('detail' in link or 'baseid=' in link or '.pdf' in link) and link not in links:
                    links.append(link)

            print(f"Found {len(links)} links on csr/reportList page")
        else:
            # Generic fallback
            links = response.xpath('//a[contains(@href, "detail") or contains(@href, "baseid=") or contains(@href, ".pdf")]/@href').getall()
            print(f"Found {len(links)} links on generic page")

        # Process links
        for link in links or []:
            if link and not any(nav in link for nav in ['javascript:', '#', 'mailto:', 'tel:']):
                # For news pages, we want to include all detail links
                if 'news' in response.url:
                    if 'detail' in link or 'baseid=' in link:
                        article_links.append(link)
                # For other pages, include PDF files as well
                else:
                    if 'detail' in link or 'baseid=' in link or link.lower().endswith('.pdf'):
                        article_links.append(link)

        # Remove duplicates while preserving order
        unique_links = []
        for link in article_links:
            if link not in unique_links:
                unique_links.append(link)

        print(f"Processed {len(unique_links)} valid article links")
        return [response.urljoin(link) for link in unique_links]

    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response, entry=None) -> str:
        # PDF files have different title extraction logic
        if response.url.endswith('.pdf'):
            # Extract filename from URL for PDF files
            filename = response.url.split('/')[-1]
            # Remove URL encoding and file extension
            title = re.sub(r'\.pdf.*$', '', filename)
            # Replace hyphens and underscores with spaces
            title = re.sub(r'[-_]', ' ', title)
            # Remove any URL encoding
            title = re.sub(r'%[0-9A-Fa-f]{2}', ' ', title)
            return title.strip()

        # For HTML pages - specific to GAC website
        title_xpaths = [
            '//div[contains(@class, "detail_title")]/text()',
            '//div[contains(@class, "news_title")]/text()',
            '//h1/text()',
            '//div[@class="title"]/text()',
            '//div[contains(@class, "title")]/text()',
            '//title/text()'
        ]

        for xpath in title_xpaths:
            title = response.xpath(xpath).get()
            if title and title.strip():
                return title.strip()

        # Fallback to URL-based title
        url_parts = response.url.split('/')
        if url_parts and url_parts[-1]:
            return f"Article: {url_parts[-1]}"

        return "No Title"

    def get_body(self, response, entry=None) -> str:
        # PDF files don't have extractable body text through normal means
        if response.url.endswith('.pdf'):
            return f"PDF Document: {response.url}"

        # For HTML pages - specific to GAC website
        body_xpaths = [
            '//div[contains(@class, "news_content")]//text()',
            '//div[contains(@class, "detail_content")]//text()',
            '//div[contains(@class, "content")]//text()',
            '//div[@class="content"]//text()',
            '//article//text()'
        ]

        for xpath in body_xpaths:
            body_parts = response.xpath(xpath).getall()
            if body_parts and ''.join(body_parts).strip():
                return body_normalization(body_parts)

        return "No content available"

    def get_images(self, response, entry=None) -> list:
        # PDF files don't have extractable images through normal means
        if response.url.endswith('.pdf'):
            return []

        # For HTML pages
        img_xpaths = [
            '//div[@class="news_content"]//img/@src',
            '//div[@class="detail_content"]//img/@src',
            '//div[@class="content"]//img/@src',
            '//div[contains(@class, "content")]//img/@src',
            '//article//img/@src',
            '//img/@src'  # Fallback to all images
        ]

        for xpath in img_xpaths:
            img_urls = response.xpath(xpath).getall()
            if img_urls:
                return [urljoin(response.url, img) for img in img_urls]

        return []

    def date_format(self) -> str:
        return "%Y-%m-%d"

    def get_date(self, response, entry=None) -> str:
        # For PDF files, try to extract date from filename or URL
        if response.url.endswith('.pdf'):
            # Try to find date pattern in URL or filename
            date_match = re.search(r'(\d{4}[-_/]\d{1,2}[-_/]\d{1,2}|\d{4}\d{2}\d{2})', response.url)
            if date_match:
                date_str = date_match.group(1)
                # Normalize date format
                if re.match(r'\d{8}', date_str):  # Format: YYYYMMDD
                    return f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"
                return date_str.replace('_', '-').replace('/', '-')

            # If no date in URL, use current date
            return datetime.now(pytz.timezone(self.timezone)).strftime(self.date_format())

        # For HTML pages - specific to GAC website
        date_xpaths = [
            '//div[contains(@class, "news_date")]/text()',
            '//div[contains(@class, "detail_date")]/text()',
            '//span[contains(@class, "date")]/text()',
            '//div[contains(@class, "date")]/text()',
            '//span[contains(@class, "time")]/text()',
            '//div[contains(@class, "time")]/text()'
        ]

        for xpath in date_xpaths:
            date_str = response.xpath(xpath).get()
            if date_str:
                # Try different date formats
                date_patterns = [
                    r'(\d{4}-\d{1,2}-\d{1,2})',  # YYYY-MM-DD
                    r'(\d{4}/\d{1,2}/\d{1,2})',  # YYYY/MM/DD
                    r'(\d{4}\.\d{1,2}\.\d{1,2})',  # YYYY.MM.DD
                    r'(\d{4}年\d{1,2}月\d{1,2}日)'  # YYYY年MM月DD日
                ]

                for pattern in date_patterns:
                    date_match = re.search(pattern, date_str)
                    if date_match:
                        date_found = date_match.group(1)
                        # Convert to standard format
                        if '/' in date_found:
                            date_found = date_found.replace('/', '-')
                        elif '.' in date_found:
                            date_found = date_found.replace('.', '-')
                        elif '年' in date_found:
                            date_found = date_found.replace('年', '-').replace('月', '-').replace('日', '')
                        return date_found

        # Return current date if no date found
        return datetime.now(pytz.timezone(self.timezone)).strftime(self.date_format())

    def get_authors(self, response, entry=None) -> list:
        # PDF files typically don't have extractable author info
        if response.url.endswith('.pdf'):
            return ["广汽集团"]

        # For HTML pages
        author_xpaths = [
            '//div[@class="author"]/text()',
            '//span[@class="author"]/text()',
            '//div[contains(@class, "author")]/text()',
            '//span[contains(@class, "author")]/text()',
            '//div[@class="source"]/text()',
            '//span[@class="source"]/text()',
            '//div[contains(@class, "source")]/text()',
            '//span[contains(@class, "source")]/text()'
        ]

        for xpath in author_xpaths:
            author = response.xpath(xpath).get()
            if author and author.strip():
                # Clean up author name
                author = author.strip()
                # Remove common prefixes
                author = re.sub(r'^(作者[：:]\s*|来源[：:]\s*|编辑[：:]\s*|记者[：:]\s*)', '', author)
                return [author]

        # Default author
        return ["广汽集团"]

    def get_document_urls(self, response, entry=None) -> list:
        # If the current page is a PDF, include it as a document
        if response.url.endswith('.pdf'):
            return [response.url]

        # For HTML pages, look for document links
        doc_patterns = [
            '//a[contains(@href, ".pdf")]/@href',
            '//a[contains(@href, ".doc")]/@href',
            '//a[contains(@href, ".docx")]/@href',
            '//a[contains(@href, ".xls")]/@href',
            '//a[contains(@href, ".xlsx")]/@href',
            '//a[contains(@href, ".ppt")]/@href',
            '//a[contains(@href, ".pptx")]/@href',
            '//a[contains(@href, ".zip")]/@href',
            '//a[contains(@href, ".rar")]/@href'
        ]

        doc_urls = []
        for pattern in doc_patterns:
            urls = response.xpath(pattern).getall()
            if urls:
                doc_urls.extend(urls)

        # Remove duplicates and convert to absolute URLs
        return [urljoin(response.url, doc) for doc in set(doc_urls)]

    def get_next_page(self, response) -> str:
        # For news page, try to construct pagination URL based on observed pattern
        if "news" in response.url:
            # Check if we're already on a paginated page
            if "page=" in response.url:
                current_page_match = re.search(r'page=(\d+)', response.url)
                if current_page_match:
                    current_page = int(current_page_match.group(1))
                    # Limit to a reasonable number of pages to avoid infinite loops
                    if current_page < 10:  # Assuming max 10 pages
                        next_page = current_page + 1
                        next_url = re.sub(r'page=\d+', f'page={next_page}', response.url)
                        print(f"Next page URL: {next_url}")
                        return next_url
            else:
                # Add page parameter
                if "?" in response.url:
                    next_url = f"{response.url}&page=2"
                else:
                    next_url = f"{response.url}?page=2"
                print(f"Next page URL: {next_url}")
                return next_url

        # For invest/notice page
        elif "invest/notice" in response.url:
            # Check if we're already on a paginated page
            if "page=" in response.url:
                current_page_match = re.search(r'page=(\d+)', response.url)
                if current_page_match:
                    current_page = int(current_page_match.group(1))
                    # Limit to a reasonable number of pages to avoid infinite loops
                    if current_page < 10:  # Assuming max 10 pages
                        next_page = current_page + 1
                        next_url = re.sub(r'page=\d+', f'page={next_page}', response.url)
                        print(f"Next page URL: {next_url}")
                        return next_url
            else:
                # Add page parameter
                if "?" in response.url:
                    next_url = f"{response.url}&page=2"
                else:
                    next_url = f"{response.url}?page=2"
                print(f"Next page URL: {next_url}")
                return next_url

        # For csr/reportList page
        elif "csr/reportList" in response.url:
            # Check if we're already on a paginated page
            if "page=" in response.url:
                current_page_match = re.search(r'page=(\d+)', response.url)
                if current_page_match:
                    current_page = int(current_page_match.group(1))
                    # Limit to a reasonable number of pages to avoid infinite loops
                    if current_page < 10:  # Assuming max 10 pages
                        next_page = current_page + 1
                        next_url = re.sub(r'page=\d+', f'page={next_page}', response.url)
                        print(f"Next page URL: {next_url}")
                        return next_url
            else:
                # Add page parameter
                if "?" in response.url:
                    next_url = f"{response.url}&page=2"
                else:
                    next_url = f"{response.url}?page=2"
                print(f"Next page URL: {next_url}")
                return next_url

        # Check for pagination links - generic approach
        next_page_xpaths = [
            '//a[contains(text(), "下一页")]/@href',
            '//a[contains(text(), "Next")]/@href',
            '//a[@class="next"]/@href',
            '//a[contains(@class, "next")]/@href',
            '//li[contains(@class, "next")]/a/@href',
            '//div[contains(@class, "pagination")]//a[contains(text(), "下一页")]/@href',
            '//div[contains(@class, "pagination")]//a[contains(@class, "next")]/@href',
            '//div[contains(@class, "pagination")]//a[contains(@class, "pagination-next")]/@href'
        ]

        for xpath in next_page_xpaths:
            next_page = response.xpath(xpath).get()
            if next_page:
                next_url = response.urljoin(next_page)
                print(f"Next page URL from XPath: {next_url}")
                return next_url

        return None

    def get_page_flag(self) -> bool:
        return True

    def get_meta(self, response, entry=None) -> list:
        return []

    def get_pdf(self, response, entry=None):
        if response.url.endswith('.pdf'):
            return response.body
        return None
