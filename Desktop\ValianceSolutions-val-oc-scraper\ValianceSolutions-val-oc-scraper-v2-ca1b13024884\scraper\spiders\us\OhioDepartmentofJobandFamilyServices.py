import scrapy
from scraper.OCSpider import <PERSON>CSpider
from scraper.utils.helper import body_normalization

class OhioDepartmentofJobandFamilyServices(OCSpider):
    name = 'OhioDepartmentofJobandFamilyServices'

    country = "US"
    
    custom_settings = {
		"DOWNLOADER_MIDDLEWARES" : {
			'scraper.middlewares.HeadlessBrowserProxy': 350,
		},
		"DOWNLOAD_DELAY" : 5,
	}
    
    HEADLESS_BROWSER_WAIT_TIME = 30000   # 30 Seconds wait time
    
    start_urls_names = {
        'https://jfs.ohio.gov/wps/portal/gov/jfs/home/<USER>/all-news/': "News"
    }

    charset = "utf-8"

    def parse_intermediate(self, response):
        all_articles = list(set(response.xpath("//div[@class='core-news__list js-iop-items-container']//a//@href").getall()))
        total_articles = len(all_articles)
        articles_per_page = 100
        start_url = response.meta.get("start_url", response.url)
        for start_idx in range(0, total_articles, articles_per_page):
            yield scrapy.Request(
                url=start_url,
                callback=self.parse,
                meta={
                    'start_idx': start_idx, 
                    'articles': all_articles, 
                    'start_url': start_url
                },
                dont_filter=True
            )

    @property
    def source_type(self) -> str:
        return 'ministry'

    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Central"

    def get_articles(self, response) -> list:
        all_articles = response.meta.get('articles', [])
        start_idx = response.meta.get('start_idx', 0)
        end_idx = start_idx + 100
        return all_articles[start_idx:end_idx]
    
    def get_href(self, entry) -> str:
        return entry

    def get_title(self, response) -> str:
        return response.xpath("//h1[@class='odx-content__title']//text()").get()
    
    def get_body(self, response) -> str:
        return body_normalization(response.xpath("//section[@class='odx-content__body']//p//text()").getall())
    
    def get_images(self, response) -> list:
        return ""
    
    def date_format(self) -> str:
        return '%B %d, %Y'

    def get_date(self, response) -> str:
        return response.xpath("//div[@class='odx-content__info margin-bottom-sm']//span//text()").get(default="").strip()
        
    def get_authors(self, response):
        return response.xpath("//section[@class='odx-content__body']//p//a[contains(@href,'.pdf')]//text()").get()

    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response) -> str:
        return None