import logging
from typing import Optional
import scrapy
from scraper.OCSpider import <PERSON><PERSON>pid<PERSON>
from scraper.utils.helper import body_normalization
import re

class IdahoDepartmentOfLabour(OCSpider):
    name = 'IdahoDepartmentOfLabour'
    
    country = "US"

    start_urls_names = {
        'https://idahoatwork.com/category/news-releases/': 'News'
    }
    
    charset = "utf-8"

    @property
    def source_type(self) -> str:
        return "ministry"
    
    @property
    def language(self):
        return "English"

    @property
    def timezone(self):
        return "US/Central"
    
    def get_articles(self, response) -> list:
        return response.xpath('//div[@id="content"]//article//h1/a/@href').getall()
    
    def get_href(self, entry) -> str:
        return entry
    
    def get_title(self,response) -> str:
        return response.xpath('//h1[@class= "entry-title"]/text()').get()
        
    def get_body(self, response) -> str:
        return body_normalization(response.xpath('//div[@class="entry-content"]/p/text()').getall())    
            
    def get_images(self, response) -> list[str]:
        return []

    def date_format(self) -> str:
        return "%B %d, %Y"

    def get_date(self, response) -> str:
        content = response.xpath("//p[contains(.//b/text(), 'For Immediate Release:') or contains(.//strong/text(), 'For Immediate Release:')]").get()
        if not content:
            return None
        date_match = re.search(r'For Immediate Release:.*?([A-Za-z]+\.?\s+\d{1,2},?\s+\d{4})', content)
        if date_match:
            raw_date = date_match.group(1).strip()
            clean_date = re.sub(r'\s+', ' ', raw_date)
            try:
                month_mapping = {
                    'jan': 'January', 'feb': 'February', 'mar': 'March', 'apr': 'April',
                    'may': 'May', 'jun': 'June', 'jul': 'July', 'aug': 'August',
                    'sep': 'September', 'oct': 'October', 'nov': 'November', 'dec': 'December'
                }
                parts_match = re.search(r'([A-Za-z]+\.?)[\s,]*(\d{1,2})[\s,]*(\d{4})', clean_date)
                if parts_match:
                    month_text = parts_match.group(1).lower().replace('.', '')
                    day = parts_match.group(2)
                    year = parts_match.group(3)
                    month = month_mapping.get(month_text[:3], month_text)
                    formatted_date = f"{month} {int(day)}, {year}"
                    return formatted_date
            except Exception as e:
                return None

    def get_authors(self, response):
        return []
    
    def get_page_flag(self) -> bool:
        return False

    def get_next_page(self, response, current_page):
        if response.status != 200:
            return None
        else:
            return str(int(current_page) + 1)
    
    def go_to_next_page(self, response, start_url, current_page: Optional[int] = 1):
        next_page = self.get_next_page(response, current_page)
        url=f"https://idahoatwork.com/category/news-releases/page/{next_page}/"
        if next_page:
            yield scrapy.Request(
                url=url,
                method='GET',
                callback=self.parse, 
                dont_filter=True,
                 meta={
                        'current_page': next_page, 
                        'start_url': start_url,
                    }
            )
        else:
            logging.info("No more pages to fetch.")
            yield None